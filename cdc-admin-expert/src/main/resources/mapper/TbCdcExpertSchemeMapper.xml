<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.expert.mapper.TbCdcExpertSchemeMapper">

    <delete id="delExpertScheme" >
        DELETE FROM app.tb_cdc_expert_assess_scheme
    </delete>

    <insert id="addExpertScheme" parameterType="java.util.List">
        INSERT INTO app.tb_cdc_expert_assess_scheme (id, assess_attribute, assess_weight,deleted,create_user_id,create_user,create_time)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id}, #{item.assessAttribute},#{item.assessWeight},0, #{item.createUserId},#{item.createUser},#{item.createTime})
        </foreach>
    </insert>




</mapper>
