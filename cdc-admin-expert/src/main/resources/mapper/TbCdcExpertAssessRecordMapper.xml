<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.expert.mapper.TbCdcExpertAssessRecordMapper">

    <delete id="delExpertAssessRecordByExpertId" parameterType="string">
        DELETE FROM app.tb_cdc_expert_assess_record
        WHERE expert_id = #{expertId}
    </delete>

    <insert id="addExpertAssessRecord" parameterType="java.util.List">
        INSERT INTO app.tb_cdc_expert_assess_record (id, expert_id,assess_scheme_id, assess_attribute, assess_weight, score,deleted,create_user_id,create_user,create_time)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id}, #{item.expertId},#{item.assessSchemeId}, #{item.assessAttribute}, #{item.assessWeight}, #{item.score},0,
            #{item.createUserId}, #{item.createUser}, #{item.createTime})
        </foreach>
    </insert>

    <select id="findExportIdByScores" parameterType="java.util.List" resultType="java.lang.String">
        select expert_id from (
            select tcear.expert_id,
            <foreach collection="accessZoneParams" item="item" index="index" separator=",">
                sum(case when assess_scheme_id='${item.id}' then score else 0 end) as ${'id_' + index}
            </foreach>
            from tb_cdc_expert_assess_record tcear  group by tcear.expert_id
        ) as access_record
        <where>
            <foreach collection="accessZoneParams" item="item" index="index" separator="AND">
                ${'id_' + index} &gt;= ${item.minScore} AND ${'id_' + index} &lt;= ${item.maxScore}
            </foreach>
        </where>
    </select>

</mapper>
