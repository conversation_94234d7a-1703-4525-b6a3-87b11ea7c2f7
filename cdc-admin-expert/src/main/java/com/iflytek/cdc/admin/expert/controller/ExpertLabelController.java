package com.iflytek.cdc.admin.expert.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.iflytek.cdc.admin.expert.dto.ExpertLabelDTO;
import com.iflytek.cdc.admin.expert.dto.ExpertLabelPageDTO;
import com.iflytek.cdc.admin.expert.entity.TbCdcExpertLabel;
import com.iflytek.cdc.admin.expert.service.ExpertLabelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Api(tags = "专家标签管理")
@RequestMapping("/pt/{version}/expertLabel")
public class ExpertLabelController {

    @Resource
    private ExpertLabelService expertLabelService;


    @PostMapping("/findExpertLabel")
    @ApiOperation("查询专家标签列表")
    public IPage<TbCdcExpertLabel> findExpertLabel(@RequestBody ExpertLabelPageDTO labelPageDTO) {
        return expertLabelService.findExpertLabel(labelPageDTO);
    }

    @PostMapping("/findAllExpertLabels")
    @ApiOperation("查询所有专家标签列表")
    public List<TbCdcExpertLabel> findAllExpertLabels(@RequestBody ExpertLabelPageDTO labelPageDTO) {
        return expertLabelService.findAllExpertLabels(labelPageDTO);
    }

    @PostMapping("/findExpertLabelByIds")
    @ApiOperation("查询专家标签列表")
    public List<TbCdcExpertLabel> findExpertLabelByIds(@RequestParam(required = false, name = "ids") String ids) {
        return expertLabelService.findExpertLabelByIds(ids);
    }

    @PostMapping("/addExpertLabel")
    @ApiOperation("新增专家标签信息")
    public long addExpertLabel(@RequestBody ExpertLabelDTO request,
                               @RequestParam("loginUserId") String loginUserId,
                               @RequestParam("loginUserName") String loginUserName) {
        return expertLabelService.addExpertLabel(request, loginUserId, loginUserName);
    }

    @PostMapping("/updateExpertLabel")
    @ApiOperation("更新专家标签信息")
    public long updateExpertLabel(@RequestBody ExpertLabelDTO request,
                                  @RequestParam("loginUserId") String loginUserId,
                                  @RequestParam("loginUserName") String loginUserName) {
        return expertLabelService.updateExpertLabel(request, loginUserId, loginUserName);
    }

    @GetMapping("/delExpertLabel")
    @ApiOperation("删除专家标签信息")
    public long delExpertLabel(String id,
                               @RequestParam("loginUserId") String loginUserId,
                               @RequestParam("loginUserName") String loginUserName) {
        return expertLabelService.delExpertLabel(id, loginUserId, loginUserName);
    }

}
