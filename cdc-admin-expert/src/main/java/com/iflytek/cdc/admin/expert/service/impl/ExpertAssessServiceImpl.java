package com.iflytek.cdc.admin.expert.service.impl;


import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iflytek.cdc.admin.common.apiservice.BatchCommonService;
import com.iflytek.cdc.admin.common.constants.CommonConstants;
import com.iflytek.cdc.admin.expert.dto.ExpertLabelDTO;
import com.iflytek.cdc.admin.expert.entity.TbCdcExpert;
import com.iflytek.cdc.admin.expert.entity.TbCdcExpertLabel;
import com.iflytek.cdc.admin.expert.mapper.TbCdcExpertLabelMapper;
import com.iflytek.cdc.admin.expert.service.ExpertAssessService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class ExpertAssessServiceImpl implements ExpertAssessService {
    

}
