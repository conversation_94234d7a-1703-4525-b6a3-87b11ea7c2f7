package com.iflytek.cdc.admin.expert.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.common.apiservice.BatchCommonService;
import com.iflytek.cdc.admin.common.constants.CommonConstants;
import com.iflytek.cdc.admin.common.vo.uap.UapUserPo;
import com.iflytek.cdc.admin.expert.dto.ExpertLabelDTO;
import com.iflytek.cdc.admin.expert.dto.ExpertLabelPageDTO;
import com.iflytek.cdc.admin.expert.entity.TbCdcExpertLabel;
import com.iflytek.cdc.admin.expert.mapper.TbCdcExpertLabelMapper;
import com.iflytek.cdc.admin.expert.service.ExpertLabelService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static com.iflytek.cdc.admin.common.interceptor.UserInfoInterceptor.USER_INFO;

/**
 * 专家标签管理实现类
 */
@Service
public class ExpertLabelServiceImpl implements ExpertLabelService {

    @Resource
    private TbCdcExpertLabelMapper tbCdcExpertLabelMapper;
    @Resource
    private BatchCommonService batchCommonService;

    @Override
    public IPage<TbCdcExpertLabel> findExpertLabel(ExpertLabelPageDTO labelPageDTO) {
        LambdaQueryWrapper<TbCdcExpertLabel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbCdcExpertLabel::getDeleted, CommonConstants.STATUS_OFF);
        queryWrapper.eq(ObjectUtil.isNotEmpty(labelPageDTO.getId()), TbCdcExpertLabel::getId, labelPageDTO.getId());
        queryWrapper.like(ObjectUtil.isNotEmpty(labelPageDTO.getLabelName()), TbCdcExpertLabel::getLabelName, labelPageDTO.getLabelName());

        PageHelper.startPage(labelPageDTO.getPageIndex(), labelPageDTO.getPageSize());
        List<TbCdcExpertLabel> listExpertLabels = tbCdcExpertLabelMapper.selectList(queryWrapper);
        PageInfo<TbCdcExpertLabel> pageInfo = new PageInfo<>(listExpertLabels);
        Page<TbCdcExpertLabel> page = new Page<>();
        page.setRecords(pageInfo.getList());
        page.setCurrent(labelPageDTO.getPageIndex());
        page.setSize(labelPageDTO.getPageSize());
        page.setTotal(pageInfo.getTotal());
        page.setPages(pageInfo.getPages());

        return page;
    }

    @Override
    public List<TbCdcExpertLabel> findAllExpertLabels(ExpertLabelPageDTO labelPageDTO) {
        LambdaQueryWrapper<TbCdcExpertLabel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbCdcExpertLabel::getDeleted, CommonConstants.STATUS_OFF);
        queryWrapper.eq(ObjectUtil.isNotEmpty(labelPageDTO.getId()), TbCdcExpertLabel::getId, labelPageDTO.getId());
        queryWrapper.like(ObjectUtil.isNotEmpty(labelPageDTO.getLabelName()), TbCdcExpertLabel::getLabelName, labelPageDTO.getLabelName());

        return tbCdcExpertLabelMapper.selectList(queryWrapper);
    }

    @Override
    public List<TbCdcExpertLabel> findExpertLabelByIds(String ids) {
        if (StringUtils.isBlank(ids)) {
            LambdaQueryWrapper<TbCdcExpertLabel> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TbCdcExpertLabel::getDeleted, CommonConstants.STATUS_OFF);
            return tbCdcExpertLabelMapper.selectList(queryWrapper);
        } else {
            String[] idArr = ids.split(",");
            LambdaQueryWrapper<TbCdcExpertLabel> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TbCdcExpertLabel::getDeleted, CommonConstants.STATUS_OFF);
            queryWrapper.in(TbCdcExpertLabel::getId, idArr);
            return tbCdcExpertLabelMapper.selectList(queryWrapper);
        }

    }

    @Override
    public long addExpertLabel(ExpertLabelDTO request, String loginUserId, String loginUserName) {
        checkExistLabel(request);
        TbCdcExpertLabel expertLabel = new TbCdcExpertLabel();
        UapUserPo user = USER_INFO.get();
        expertLabel.setId(batchCommonService.uuid(TbCdcExpertLabel.TB_NAME))
                .setLabelName(request.getLabelName())
                .setCreateUserId(user.getId())
                .setCreateUser(user.getName())
                .setCreateTime(new Date());
        return tbCdcExpertLabelMapper.insert(expertLabel);
    }

    @Override
    public long updateExpertLabel(ExpertLabelDTO request, String loginUserId, String loginUserName) {
        checkExistLabel(request);
        UapUserPo user = USER_INFO.get();
        TbCdcExpertLabel expertLabel = new TbCdcExpertLabel();
        expertLabel.setId(request.getId())
                .setLabelName(request.getLabelName())
                .setModifyUserId(user.getId())
                .setModifyUser(user.getName())
                .setModifyTime(new Date());
        return tbCdcExpertLabelMapper.updateById(expertLabel);
    }


    @Override
    public long delExpertLabel(String id, String loginUserId, String loginUserName) {
        TbCdcExpertLabel tbCdcExpertLabel = tbCdcExpertLabelMapper.selectById(id);
        UapUserPo user = USER_INFO.get();
        tbCdcExpertLabel.setDeleted(1)
                .setModifyUserId(user.getId())
                .setModifyUser(user.getName())
                .setModifyTime(new Date());
        return tbCdcExpertLabelMapper.updateById(tbCdcExpertLabel);
    }

    public void checkExistLabel(ExpertLabelDTO request) {
        if (ObjectUtil.isEmpty(request.getLabelName())) {
            throw new MedicalBusinessException("标签名称不能为空！");
        }
        LambdaQueryWrapper<TbCdcExpertLabel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbCdcExpertLabel::getLabelName, request.getLabelName())
                .eq(TbCdcExpertLabel::getDeleted, CommonConstants.STATUS_OFF)
                .ne(ObjectUtil.isNotEmpty(request.getId()), TbCdcExpertLabel::getId, request.getId());
        long count = tbCdcExpertLabelMapper.selectCount(queryWrapper);
        if (count > 0) {
            throw new MedicalBusinessException("标签名称已存在,请更换标签名称！");
        }
    }
}
