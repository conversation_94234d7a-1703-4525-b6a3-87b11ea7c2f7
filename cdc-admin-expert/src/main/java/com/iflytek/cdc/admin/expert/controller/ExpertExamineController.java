package com.iflytek.cdc.admin.expert.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.iflytek.cdc.admin.expert.dto.ExpertAssessRecordDTO;
import com.iflytek.cdc.admin.expert.dto.ExpertExamineDTO;
import com.iflytek.cdc.admin.expert.dto.ExpertPageDTO;
import com.iflytek.cdc.admin.expert.entity.TbCdcExpert;
import com.iflytek.cdc.admin.expert.service.ExpertAssessRecordService;
import com.iflytek.cdc.admin.expert.service.ExpertService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@Api(tags = "专家审核管理")
@RequestMapping("/pt/{version}/expertAssess")
public class ExpertExamineController {

    @Resource
    private ExpertService expertService;



    @PostMapping("/findExpertExaminePage")
    @ApiOperation("专家审核分页查询")
    public IPage<TbCdcExpert> findExpertExaminePage(@RequestBody ExpertPageDTO expertPageDTO){
        return expertService.findExpertExaminePage(expertPageDTO);
    }



    @PostMapping("/saveExpertExamine")
    @ApiOperation("专家审核操作")
    public long saveExpertExamine(@RequestBody ExpertExamineDTO expertExamine,
                           @RequestParam("loginUserId") String loginUserId,
                           @RequestParam("loginUserName") String loginUserName){
        return expertService.saveExpertExamine(expertExamine,loginUserId,loginUserName);
    }
    



}
