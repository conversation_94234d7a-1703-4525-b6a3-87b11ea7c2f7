package com.iflytek.cdc.admin.expert.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.iflytek.cdc.admin.common.util.easyexcel.ImportResult;
import com.iflytek.cdc.admin.common.vo.uap.UapUserVo;
import com.iflytek.cdc.admin.expert.dto.ExpertAccessZoneDTO;
import com.iflytek.cdc.admin.expert.dto.ExpertExamineDTO;
import com.iflytek.cdc.admin.expert.dto.ExpertPageDTO;
import com.iflytek.cdc.admin.expert.entity.TbCdcExpert;
import com.iflytek.cdc.admin.expert.vo.ExpertGroupVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 专家管理服务接口
 */
public interface ExpertService {

    //新增专家信息
    String addExpertInfo(TbCdcExpert expert, String loginUserId, String loginUserName);

    //分页查询专家信息
    IPage<TbCdcExpert> findExpertPage(ExpertPageDTO expertPageDTO);

    Map<String, List<TbCdcExpert>> findExpertByLabelId(String labelIds);

    List<TbCdcExpert> findRecommendExperts(ExpertAccessZoneDTO expertAccessZoneDTO);

    //更新专家信息
    long updateExpertInfo(TbCdcExpert expert, String loginUserId, String loginUserName);

    //删除专家信息
    long delExpertInfo(String id);

    //获取专家字典集合
    Map<String, List<String>> findExpertDictList();

    //新增/更新uap信息
    String saveUapUserInfo(UapUserVo vo, String loginUserId);

    //专家分类查询
    List<ExpertGroupVO> findExpertClassify(String classifyType);

    //根据专家分类进行分页查询
    IPage<TbCdcExpert> findExpertPageByClassify(String classifyType, String classifyValue, Integer pageNum, Integer pageSize);


    //专家审核分页查询
    IPage<TbCdcExpert> findExpertExaminePage(ExpertPageDTO expertPageDTO);


    //专家审核操作
    long saveExpertExamine(ExpertExamineDTO expertExamine,
                           String loginUserId, String loginUserName);

    ImportResult uploadExcel(MultipartFile file, String loginUserId, String loginUserName);

    void downloadDataExcel(HttpServletRequest request, HttpServletResponse response, ExpertPageDTO expertPageDTO);

    void downloadErrorExcel(String filePath, HttpServletResponse response);
}
