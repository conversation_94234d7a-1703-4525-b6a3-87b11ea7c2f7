package com.iflytek.cdc.admin.expert.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.iflytek.cdc.admin.common.util.easyexcel.EasyExcelUtil;
import com.iflytek.cdc.admin.common.util.easyexcel.ImportResult;
import com.iflytek.cdc.admin.expert.dto.ExpertAccessZoneDTO;
import com.iflytek.cdc.admin.expert.dto.ExpertPageDTO;
import com.iflytek.cdc.admin.expert.dto.ExportExcelDTO;
import com.iflytek.cdc.admin.expert.entity.TbCdcExpert;
import com.iflytek.cdc.admin.expert.service.ExpertService;
import com.iflytek.cdc.admin.expert.vo.ExpertGroupVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RestController
@Api(tags = "专家库管理")
@RequestMapping("/pt/{version}/expert")
public class ExpertController {

    @Resource
    private ExpertService expertService;


    @PostMapping("/addExpertInfo")
    @ApiOperation("新增专家信息")
    public String addExpertInfo(@RequestBody TbCdcExpert expert,
                                @RequestParam("loginUserId") String loginUserId,
                                @RequestParam("loginUserName") String loginUserName) {
        return expertService.addExpertInfo(expert, loginUserId, loginUserName);
    }

    @PostMapping("/findExpertPage")
    @ApiOperation("分页查询专家列表")
    public IPage<TbCdcExpert> findExpertPage(@RequestBody ExpertPageDTO expertPageDTO) {
        return expertService.findExpertPage(expertPageDTO);
    }

    @GetMapping("/findExpertsByLabelIds")
    @ApiOperation("根据标签ids查询专家")
    public Map<String, List<TbCdcExpert>> findExpertByLabelId(@RequestParam("labelIds") String labelIds) {
        return expertService.findExpertByLabelId(labelIds);
    }

    @PostMapping("/findRecommendExperts")
    @ApiOperation("根据评估分数查询推荐专家")
    public List<TbCdcExpert> findRecommendExperts(@RequestBody ExpertAccessZoneDTO expertAccessZoneDTO) {
        return expertService.findRecommendExperts(expertAccessZoneDTO);
    }

    @PostMapping("/updateExpertInfo")
    @ApiOperation("编辑/更新专家信息")
    long updateExpertInfo(@RequestBody TbCdcExpert expert,
                          @RequestParam("loginUserId") String loginUserId,
                          @RequestParam("loginUserName") String loginUserName) {
        return expertService.updateExpertInfo(expert, loginUserId, loginUserName);
    }

    @GetMapping("/delExpertInfo")
    @ApiOperation("删除专家信息")
    long delExpertInfo(@RequestParam("id") String id) {
        return expertService.delExpertInfo(id);
    }

    @GetMapping("/findExpertDictList")
    @ApiOperation("获取专家字典集合")
    public Map<String, List<String>> findExpertDictList() {
        return expertService.findExpertDictList();
    }


    @GetMapping("/findExpertClassify")
    @ApiOperation("专家分类查询")
    public List<ExpertGroupVO> findExpertClassify(@RequestParam("classifyType") String classifyType) {
        return expertService.findExpertClassify(classifyType);
    }

    @GetMapping("/findExpertPageByClassify")
    @ApiOperation("根据专家分类进行分页查询")
    public IPage<TbCdcExpert> findExpertPageByClassify(@RequestParam("classifyType") String classifyType,
                                                       @RequestParam("classifyValue") String classifyValue,
                                                       @RequestParam("pageNum") Integer pageNum,
                                                       @RequestParam("pageSize") Integer pageSize) {
        return expertService.findExpertPageByClassify(classifyType, classifyValue, pageNum, pageSize);
    }

    @ApiOperation("下载excel模板")
    @GetMapping("/downloadExcel")
    public void downloadExcel(HttpServletRequest request, HttpServletResponse response) {
        List<ExportExcelDTO> list = new ArrayList<>();
        list.add(new ExportExcelDTO());
        EasyExcelUtil.writeSelectedExcelList(request, response, list, "下载专家库导入模板", ExportExcelDTO.class);
    }

    @ApiOperation("上传excel")
    @PostMapping("/uploadExcel")
    public ImportResult uploadExcel(@RequestParam("file") MultipartFile file,
                                    @RequestParam("loginUserId") String loginUserId,
                                    @RequestParam("loginUserName") String loginUserName) {
        return expertService.uploadExcel(file, loginUserId, loginUserName);
    }

    @ApiOperation("导出excel数据")
    @PostMapping("/downloadExcelData")
    public void batchDownloadData(HttpServletRequest request, HttpServletResponse response, @RequestBody ExpertPageDTO expertPageDTO) {
        expertService.downloadDataExcel(request, response, expertPageDTO);
    }

    @ApiOperation("下载失败excel")
    @PostMapping("/downloadErrorExcel")
    public void downloadErrorExcel(String filePath, HttpServletResponse response, @PathVariable String version) {
        expertService.downloadErrorExcel(filePath, response);
    }
}
