package com.iflytek.cdc.admin.common.apiservice;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.iflytek.cdc.admin.common.dto.workbench.*;
import com.iflytek.cdc.admin.common.util.ApiTool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class InterfaceApiService {
    @Resource
    private ApiService apiService;

    @Resource
    private ApiTool apiTool;
    @Value("${cdc.ecd.platform.service.name:http://cdc-ecd-platform-service}")
    private String cdcEcdPlatformServiceName;

    @Value("${cdc.wuhu.ecd.platform.service.name:http://hep-full}")
    private String cdcWuhuEcdPlatformServiceName;
    @Value("${cdc.epi.invest.service.name:http://cdc-epi-invest-service}")
    private String cdcEpiInvestServiceName;

    public TaskLevelAndQualitativeStatisticDto getEpiTaskLevelAndQualitativeStatistic(DataStatisticQueryDto statisticQueryDto) {
        TaskLevelAndQualitativeStatisticDto statisticDto = new TaskLevelAndQualitativeStatisticDto();
        try {
            log.info("获取流调系统事件等级和事件定性统计，参数:{}", JSONObject.toJSONString(statisticQueryDto));
            String url = cdcEpiInvestServiceName + "/v1/pt/portal/dataStatistic/getTaskLevelAndQualitativeStatistic";
            statisticDto = apiService.doPost(url, statisticQueryDto, TaskLevelAndQualitativeStatisticDto.class);
            log.info("获取流调系统事件等级和事件定性统计结果:{}", JSONObject.toJSONString(statisticDto));
        } catch (Exception e) {
            log.error("获取流调系统事件等级和事件定性统计结果异常！", e);
        }
        return statisticDto;
    }

    public TaskLevelAndQualitativeStatisticDto getEcdTaskLevelAndQualitativeStatistic(DataStatisticQueryDto statisticQueryDto) {
        TaskLevelAndQualitativeStatisticDto statisticDto = new TaskLevelAndQualitativeStatisticDto();
        try {
            log.info("获取应急系统事件等级和事件定性统计，参数:{}", JSONObject.toJSONString(statisticQueryDto));
            String url = cdcEcdPlatformServiceName + "/v1/pt/portal/dataStatistic/getTaskLevelAndQualitativeStatistic";
            ResultDto resultDto = apiService.doPost(url, statisticQueryDto, ResultDto.class);
            log.info("获取应急系统事件等级和事件定性统计结果:{}", JSONObject.toJSONString(resultDto));
            if (resultDto != null && resultDto.getCode().equals("200")) {
                statisticDto = JSONObject.parseObject(JSONObject.toJSONString(resultDto.getData(), SerializerFeature.WriteMapNullValue), TaskLevelAndQualitativeStatisticDto.class);
            }
        } catch (Exception e) {
            log.error("获取应急系统事件等级和事件定性统计结果异常！", e);
        }
        return statisticDto;
    }

    public Object getWuhuEcdStatistic(DataStatisticQueryDto statisticQueryDto) {
        WuhuEcdStatisticDto wuhuEcdStatisticDto = new WuhuEcdStatisticDto();
        try {
            log.info("获取芜湖数据统计指标参数：{}", JSONObject.toJSONString(statisticQueryDto));
            String eventCountUrl = cdcWuhuEcdPlatformServiceName + "/api/workbench/eventCount?loginUserId=" + statisticQueryDto.getLoginUserId();
            ResultDto eventCountresultDto = apiService.doGet(eventCountUrl, ResultDto.class);
            log.info("获取芜湖数据统计指标事件数量统计结果：{}", JSONObject.toJSONString(eventCountresultDto));
            if (eventCountresultDto != null && eventCountresultDto.getCode().equals("200")) {
                JSONArray jsonArray = JSONObject.parseArray(JSONObject.toJSONString(eventCountresultDto.getData()));
                List<WuhuEcdEventCountStatisticDto> eventCountStatisticDtos = jsonArray.toJavaList(WuhuEcdEventCountStatisticDto.class);
                wuhuEcdStatisticDto.setEventCount(eventCountStatisticDtos);
            }
            String eventTimeUrl = cdcWuhuEcdPlatformServiceName + "/api/workbench/eventTimeStatistics";
            ResultDto eventTimeResultDto = apiService.doPost(eventTimeUrl, statisticQueryDto, ResultDto.class);
            log.info("获取芜湖数据统计指标事件时间统计结果：{}", JSONObject.toJSONString(eventTimeResultDto));
            if (eventTimeResultDto != null && eventTimeResultDto.getCode().equals("200")) {
                WuhuEcdEventTimeStatisticDto eventTimeStatisticDto = JSONObject.parseObject(JSONObject.toJSONString(eventTimeResultDto.getData()), WuhuEcdEventTimeStatisticDto.class);
                wuhuEcdStatisticDto.setEventTime(eventTimeStatisticDto);
            }
        } catch (Exception e) {
            log.error("获取芜湖数据统计指标异常！", e);
        }
        return wuhuEcdStatisticDto;
    }
}
