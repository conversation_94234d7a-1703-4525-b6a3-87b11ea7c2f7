package com.iflytek.cdc.admin.common.util;

import com.iflytek.fsp.flylog.sdk.java.core.brave.utils.StringUtil;
import org.springframework.beans.BeanUtils;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 集合处理工具类
 * <AUTHOR>
 */
public class CollectionUtil {

    /**
     * 返回逗号拆分完成后的集合
     * @param source
     * @return
     */
    public static List<String> getSplitList(String source){
        if (StringUtil.isEmpty(source)) {
            return new ArrayList<>();
        }
        return Arrays.asList(source.split(","));
    }

    public static <T> List<T> sort(List<T> value, String property, String direction, Class<T> tClass){
        Method readMethod = BeanUtils.getPropertyDescriptor(tClass, property).getReadMethod();
        value.sort(((o1, o2) -> {
            try {
                int compareValue;
                Object value1 = readMethod.invoke(o1);
                Object value2 = readMethod.invoke(o2);
                if (value1 == null){
                    if (value2 != null){
                        compareValue = 1;
                    }else {
                        compareValue = -1;
                    }
                }else if (value2 == null){
                    compareValue =  -1;
                }else {
                    if (value1 instanceof Integer && value2 instanceof Integer){
                        compareValue =  ((Integer) value1).compareTo((Integer) value2);
                    }else if (value1 instanceof Long && value2 instanceof Long){
                        compareValue = ((Long) value1).compareTo((Long) value2);
                    }else if (value1 instanceof Double && value2 instanceof Double){
                        compareValue = ((Double) value1).compareTo((Double) value2);
                    } else if (value1 instanceof BigDecimal && value2 instanceof BigDecimal){
                        compareValue = ((BigDecimal) value1).compareTo((BigDecimal) value2);
                    }
                    else if (value1 instanceof Date && value2 instanceof Date){
                        compareValue = ((Date) value1).compareTo((Date) value2);
                    }else {
                        compareValue =  value1.toString().compareTo(value2.toString());
                    }
                }
                if ("desc".equals(direction)){
                    return -compareValue;
                }
                return compareValue;
            } catch (IllegalAccessException | InvocationTargetException e) {
                throw new RuntimeException(e);
            }

        }));
        return value;
    }

    /**
     * 常量类不需要public构造方法
     */
    private CollectionUtil(){
        throw new IllegalStateException("常量类");
    }
}
