package com.iflytek.cdc.admin.common.util.easyexcel;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ImportResult {

    @ApiModelProperty(value = "导入成功数")
    private long successNum;

    @ApiModelProperty(value = "导入失败数")
    private long failNum;

    @ApiModelProperty(value = "总数")
    private long totalNum;

    @ApiModelProperty(value = "文件路径")
    private String filePath;
}
