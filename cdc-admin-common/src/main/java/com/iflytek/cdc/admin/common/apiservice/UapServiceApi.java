package com.iflytek.cdc.admin.common.apiservice;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.common.enums.AreaLevelEnum;
import com.iflytek.cdc.admin.common.util.ApiTool;
import com.iflytek.cdc.admin.common.util.CollectionUtil;
import com.iflytek.cdc.admin.common.vo.LoginUserAreaVO;
import com.iflytek.cdc.admin.common.vo.uap.*;
import com.iflytek.medicalboot.core.dto.PageData;
import com.iflytek.medicalboot.core.dto.PageRequest;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.zhyl.uap.ext.pojo.UapMdmEmpUserDto;
import com.iflytek.zhyl.uap.usercenter.pojo.UapUserExtendDto;
import com.iflytek.zhyl.uap.usercenter.pojo.cdc.DepartmentDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

/**
 * Uap服务
 * from cdc-platform-api:com.iflytek.fpva.cdc.service.impl.TbCdcConfigServiceImpl
 *
 * <AUTHOR>
 * @date 2022-06-16 16:01
 */
@Service
@Slf4j
public class UapServiceApi {
    @Value("${userCenter.version:3.0.0}")
    private String userCenterVersion;

    @Value("${uap-service-ext-service:http://uap-service-ext-service}")
    private String uapServiceExtService;
    
    @Resource
    private ApiTool apiTool;

    @Resource
    private ApiService apiService;
    @Value("${appCode.workbench:cdc-portal-web}")
    private String appCodeWorkbench;
    @Value("${common.internalService.uap-service-ext-service.orgType:J100}")
    private String orgType;

    public List<UapPortalApp> queryUserPortalApp(String loginUserId) {

        List<UapPortalApp> uapPortalApps = new ArrayList<>();
        String url = uapServiceExtService + "/v1/tc/mdmEmp/queryPortalApp?loginUserId=" + loginUserId;
        try {
            uapPortalApps= apiService.doGetList(url, UapPortalApp.class);
        } catch (Exception e) {
            log.error("获取用户portal页应用列表失败", e);
        }
        return uapPortalApps;
    }

    /**
     * 获取用户appDetailList
     * @param loginUserId
     * @return
     */
    public List<UapPortalAppDetail> getUserAppDetailList(String loginUserId) {
        return buildUserAbuppDetailList(this.queryUserPortalApp(loginUserId));
    }

    private List<UapPortalAppDetail> buildUserAbuppDetailList(List<UapPortalApp> uapPortalApps) {
        List<UapPortalAppDetail> allApps = new ArrayList<>();
        for (UapPortalApp uapPortalApp : uapPortalApps) {
            List<UapPortalAppDetail> appList = uapPortalApp.getAppList();
            allApps.addAll(appList);
        }
        return allApps;
    }
    public List<UapAuthNode> getMenu(String loginUserId, String appCode) {
        try {
            String appCodeStr = StrUtil.isBlank(appCode) ? appCodeWorkbench : appCode;
            String authUrl = uapServiceExtService + "/v1/pt/TUapAuthority/user/authTree?appCode=" + appCodeStr + "&check=true&loginUserId=" + loginUserId;
            JSONObject jsonObject = apiService.doGet(authUrl, JSONObject.class);
            JSONArray authArray = jsonObject.getJSONArray("nodes");
            return authArray.toJavaList(UapAuthNode.class);
        } catch (Exception e) {
            log.error("请求用户中心获取菜单列表信息失败", e);
            return new ArrayList<>();
        }
    }
    /**
     * 校验用户是否属于脱敏角色
     *
     * @param roleId      不脱敏角色ID
     * @param loginUserId 用户ID
     * @return true：脱敏；false：不脱敏
     */
    public boolean checkRoleByUser(String roleId, String loginUserId) {
        if (userCenterVersion.equals("5.0.0")) {
            String url = uapServiceExtService + "/v1/pt/mdmRole/queryUserRoleList?userId=" + loginUserId;
            List<UapTreeNode> result = apiTool.doGet(url, JSONArray.class).toJavaList(UapTreeNode.class);
            return checkRole(roleId, result);
        } else {
            String url = uapServiceExtService + "/v1/pt/TUapRole/tree?loginUserId=" + loginUserId;
            JSONObject requestMap = new JSONObject();
            UapTreeNode result = apiTool.doPost(url, requestMap, UapTreeNode.class);
            assert result != null;
            return checkRole(roleId, result);
        }
    }

    public Boolean checkUserIsDesensitizationUser(String loginUserId) {
        String url = uapServiceExtService + "/v1/pt/mdmEmp/" + loginUserId;
        UapMdmEmpUserDto userDto = apiTool.doGet(url, UapMdmEmpUserDto.class);
        if (userDto.getExtands() != null) {
            for (UapUserExtendDto extendDto : userDto.getExtands()) {
                if (extendDto.getNameCode().equals("TM")) {
                    if (extendDto.getValue().equals("1")) {
                        return true;
                    } else if (extendDto.getValue().equals("0")) {
                        return false;
                    }
                }
            }
        }
        return false;
    }

    public UapOrgPo getUserOrg(String loginUserName) {
        //获取用户所在机构 判断级别 组装数据
        //2020-12-29 rest调用获取机构
        String userUrl = uapServiceExtService + "/v1/pv/TUapUser/withPassword/" + loginUserName;
        String orgId = Objects.requireNonNull(apiTool.doGet(userUrl, UapUserPo.class)).getOrgId();
        if (userCenterVersion.equals("5.0.0")) {
            String orgUrl = uapServiceExtService + "/v1/pv/TUapOrganization/byDepartmentId?departmentId=" + orgId;
            return apiTool.doGet(orgUrl, UapOrgPo.class);
        } else {
            String orgUrl = uapServiceExtService + "/v1/pt/TUapOrganization/" + orgId;
            return Objects.requireNonNull(apiTool.doGet(orgUrl, JSONObject.class)).getObject("uapOrganization", UapOrgPo.class);
        }
    }

    /**
     * 根据orgId查询
     */
    public List<UapMdmEmpUserDto> listByOrgId(String orgId){
        String url = uapServiceExtService + "/v1/pt/mdmEmp/listByOrgId?orgId="+ orgId;
        return apiService.doGetList(url, UapMdmEmpUserDto.class);
    }

    private boolean checkRole(String roleId, List<UapTreeNode> userPos) {
        for (UapTreeNode uapRoleUserPo : userPos) {
            if (roleId.equals(uapRoleUserPo.getId())) {
                return true;
            }
        }
        return false;
    }
    private boolean checkRole(String roleId, UapTreeNode userPo) {
        String id = userPo.getId();
        if (roleId.equals(id)) {
            return true;
        }

        List<UapTreeNode> nodes = userPo.getNodes();
        if (!CollectionUtils.isEmpty(nodes)) {
            for (UapTreeNode uapTreeNode : nodes) {
                if (checkRole(roleId, uapTreeNode)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 获取不脱敏角色ID
     *
     * @param appCode  portal app
     * @param roleName 不脱敏角色
     * @return 不脱敏角色ID
     */
    public String getUapRoleIdByAppCodeAndRoleName(String appCode, String roleName) {
        String uapRoleId = null;

        List<UapRole> uapRoleList = getUapRoleListByAppCode(appCode);
        for (UapRole uapRole : uapRoleList) {
            if (roleName.equals(uapRole.getName())) {
                uapRoleId = uapRole.getId();
            }
        }
        return uapRoleId;
    }

    public List<UapRole> getUapRoleListByAppCode(String appCode) {
        String url = uapServiceExtService + "/v1/pt/TUapRole/byAppCode?appCode=" + appCode;
        List<UapRole> result = new ArrayList<>();

        try {
            UapRole[] uapRoles = apiTool.doGet(url, UapRole[].class);
            assert uapRoles != null;
            result = Arrays.asList(uapRoles);
        } catch (Exception e) {
            log.error("获取角色失败,", e);
        }
        return result;
    }

    public UapUserPo getUser(String loginUserId) {
        String url = uapServiceExtService + "/v1/pt/TUapUser/" + loginUserId;
        JSONObject jsonObject = apiTool.doGet(url, JSONObject.class);
        return Optional.ofNullable(jsonObject).map(item -> item.getObject("uapUser", UapUserPo.class)).orElse(new UapUserPo());
    }


    /**
     * 设置参数的省市区
     */
    public <T> void setProvinceAndCityAndDistrict(String loginUserName,
                                                  T t,
                                                  BiConsumer<T , List<String>> province,
                                                  BiConsumer<T , List<String>> city,
                                                  BiConsumer<T , List<String>> district,
                                                  BiConsumer<T , List<String>> street){
        LoginUserAreaVO currUserArea = getCurrUserArea(loginUserName);
        List<String> provinceCodes = currUserArea.getProvinceCodes();
        List<String> cityCodes = currUserArea.getCityCodes();
        List<String> districtCodes = currUserArea.getDistrictCodes();
        List<String> streetCodes = currUserArea.getStreetCodes();
        province.accept(t, provinceCodes);
        city.accept(t, cityCodes);
        district.accept(t, districtCodes);
        street.accept(t, streetCodes);
    }

    public LoginUserAreaVO getCurrUserArea(String loginUserName){
        Integer areaLevel = null;
        UapOrgPo userOrg = getUserOrg(loginUserName);
        if (userOrg.getProvinceCode() == null
                && userOrg.getCityCode() == null
                && userOrg.getDistrictCode() == null){
            throw new MedicalBusinessException("当前登录用户没有所属区，请检查用户配置");
        }
        List<String> provinceCodes = new ArrayList<>();
        List<String> cityCodes = new ArrayList<>();
        List<String> districtCodes = new ArrayList<>();

        if (StringUtils.isNotEmpty(userOrg.getProvinceCode())){
            provinceCodes = CollectionUtil.getSplitList(userOrg.getProvinceCode());
        }

        if (StringUtils.isNotEmpty(userOrg.getCityCode())){
            cityCodes = CollectionUtil.getSplitList(userOrg.getCityCode());
        }

        if (StringUtils.isNotEmpty(userOrg.getDistrictCode())){
            districtCodes = CollectionUtil.getSplitList(userOrg.getDistrictCode());
        }
        if (!provinceCodes.isEmpty()){
            areaLevel = AreaLevelEnum.PROVINCE.getValue();
        }
        if (!cityCodes.isEmpty()){
            areaLevel = AreaLevelEnum.CITY.getValue();
        }
        if (!districtCodes.isEmpty()){
            areaLevel = AreaLevelEnum.DISTRICT.getValue();
        }
        LoginUserAreaVO loginUserAreaVO = new LoginUserAreaVO();
        loginUserAreaVO.setAreaLevel(areaLevel);
        loginUserAreaVO.setProvinceCodes(provinceCodes);
        loginUserAreaVO.setCityCodes(cityCodes);
        loginUserAreaVO.setDistrictCodes(districtCodes);
        return loginUserAreaVO;
    }


    /**
     * uap 查询机构科室树 逐层加载
     */
    public List<UapLazyOrgInfo> lazyOrgOrDept(String id, String loginUserId){
        String url  = String.format("%s/v1/pt/ImaDept/lazyOrgOrDept?id=%s&loginUserId=%s", uapServiceExtService, id, loginUserId);
        return apiTool.doGet(url, JSONArray.class).toJavaList(UapLazyOrgInfo.class);
    }

    /**
     * 根据orgId 查询机构用户
     */
    @SuppressWarnings("rawtypes")
    public PageInfo<UapOrgUser> searchUapUserByOrgId(UapUserSearchQueryDTO queryDTO, String loginUserId){
        PageRequest<UapUserSearchQueryDTO> pageRequest = new PageRequest<>();
        pageRequest.setFilter(queryDTO);
        pageRequest.setPageSize(queryDTO.getPageSize());
        pageRequest.setPageNumber(queryDTO.getPageIndex());
        String url = String.format("%s/v1/pt/mdmEmp/search?loginUserId=%s", uapServiceExtService, loginUserId );
        PageData resp = apiTool.doPost(url, pageRequest, PageData.class);
        PageData<UapOrgUser, UapUserSearchQueryDTO> result = JSONUtil.toBean(JSONUtil.toJsonStr(resp), new TypeReference<PageData<UapOrgUser, UapUserSearchQueryDTO>>() {}.getType(), false);
        PageInfo<UapOrgUser> pageInfo = new PageInfo<>(result.getEntities());
        pageInfo.setTotal(result.getNext().getTotal());
        return pageInfo;
    }

    /**
     * 添加uap
     */
    public String addUapApp(UapApp app){
        String url = String.format("%s/v1/pt/TUapApp", uapServiceExtService );
        return apiService.doPost(url, app, String.class);
    }

    /**
     * 获取权限树
     */
    public UapAuthorityTree listUapAuthorityTree(String loginUserId){
        String url  =  String.format("%s/v1/pt/TUapAuthority/tree?menu=&loginUserId=%s", uapServiceExtService, loginUserId );
        return apiService.doGet(url, UapAuthorityTree.class);
    }

    /**
     * 添加菜单
     */
    public String addUapMenu(UapMenu menu){
        try {
            String url = String.format("%s/v1/pt/TUapAuthority", uapServiceExtService );
            return apiService.doPost(url, menu, String.class);
        }catch (Exception e){
            throw new MedicalBusinessException(menu.getCode() + ":" + menu.getName() + "同步失败");
        }
    }

    /**
     * 修改菜单
     */
    public void updateMenu(UapMenu uapMenu){
        String url = String.format("%s/v1/pt/TUapAuthority/%s", uapServiceExtService, uapMenu.getId());
        apiTool.doPut(url, uapMenu);
    }

    /**
     * 删除菜单
     */
    public void deleteMenu(String id){
        String url = String.format("%s/v1/pt/TUapAuthority/%s", uapServiceExtService, id);
        apiTool.doDelete(url);
    }


    public UserInfoDto queryUserInfo(String loginUserId, String appCode) {
        String url = uapServiceExtService + "/v1/pt/mdmEmp/" + loginUserId;
        TimaUserInfo uapUserDto = apiTool.doGet(url,TimaUserInfo.class);
        if (uapUserDto == null) {
            throw new MedicalBusinessException("用户不存在！");
        }
        UserInfoDto dto = new UserInfoDto();
        dto.setUsername(uapUserDto.getName());
        dto.setLoginUserId(uapUserDto.getId());
        dto.setPhone(uapUserDto.getPhone());
        dto.setLoginUserName(uapUserDto.getLoginName());
        dto.setEmail(uapUserDto.getEmail());

        //查询用户科室
        DepartmentDto userDepartment = this.queryDeptNames(uapUserDto.getOrgId(), loginUserId);
        dto.setDepartmentCode(userDepartment.getDepartmentCode());
        dto.setDepartmentName(userDepartment.getDepartmentName());
        //获取机构信息
        TuapOrganization org = this.queryUserMaxOrg(uapUserDto.getOrgId());
        List<String> orgIds = new ArrayList<>();
        if (org == null || StrUtil.isEmpty(org.getId())) {
            throw new MedicalBusinessException("用户未绑定机构！");
        } else {
            dto.setIsCdcRole(true);
            dto.setProvinceCode(ObjectUtil.defaultIfEmpty(org.getProvinceCode(), ""));
            dto.setCityCode(ObjectUtil.defaultIfEmpty(org.getCityCode(), ""));
            dto.setDistrictCode(ObjectUtil.defaultIfEmpty(org.getDistrictCode(), ""));
            dto.setProvinceName(ObjectUtil.defaultIfEmpty(org.getProvince(), ""));
            dto.setCityName(ObjectUtil.defaultIfEmpty(org.getCity(), ""));
            dto.setDistrictName(ObjectUtil.defaultIfEmpty(org.getDistrict(), ""));
            dto.setOrgName(org.getName());
            dto.setOrgId(org.getId());
            dto.setDepartmentCode(userDepartment.getDepartmentCode());
            dto.setDepartmentName(userDepartment.getDepartmentName());
            orgIds.add(org.getId());
        }
        dto.setOrgIds(orgIds);

        //获取菜单
        dto.setMenu(this.getMenu(loginUserId, appCode));
        //获取应用名称
        if (CollUtil.isNotEmpty(dto.getMenu())) {
            dto.setAppName(dto.getMenu().get(0).getName());
        } else {
            dto.setAppName(this.queryAppName(loginUserId,appCodeWorkbench));
        }

        return dto;
    }
    public String queryAppName(String loginUserId,String appCode) {
        String url = uapServiceExtService + "/v1/pt/TUapApp/search?loginUserId=" + loginUserId;
        try {
            HashMap<String, Object> paramMap = new HashMap<>();
            paramMap.put("pageIndex", 1);
            paramMap.put("pageNumber", 1);
            paramMap.put("pageSize", 1);
            HashMap<String, String> paramFilterMap = new HashMap<>();
            paramFilterMap.put("code", appCode);
            paramMap.put("filter", paramFilterMap);
            JSONObject object = apiTool.doPost(url, paramMap, JSONObject.class);
            if (object == null) {
                return StrUtil.EMPTY;
            }
            return JSON.parseArray(JSON.toJSONString(object.get("entities")), TuapApp.class).get(0).getName();
        } catch (Exception e) {
            throw new MedicalBusinessException("获取应用信息异常");
        }
    }
    public TuapOrganization queryUserMaxOrg(String orgId) {
        TuapOrganization org = this.getOrgInfo(orgId);
        if (org != null && orgType.equals(org.getOrgTypeCode())) {
            return org;
        } else if (org != null && StrUtil.isNotEmpty(org.getHigherOrg()) && !orgType.equals(org.getOrgTypeCode())) {
            return queryUserMaxOrg(org.getHigherOrg());
        } else {
            log.info("未分配疾控机构");
            org = new TuapOrganization();
            return org;
        }
    }

    public TuapOrganization getOrgInfo(String orgId) {
        if (StringUtils.isEmpty(orgId)) {
            return null;
        }
        String url = uapServiceExtService + "/v1/pt/TUapOrganization/"+orgId;
        try {
            com.iflytek.cdc.admin.common.vo.uap.UapOrgDto dto = apiTool.doGet(url, com.iflytek.cdc.admin.common.vo.uap.UapOrgDto.class);
            if (dto == null) {
                log.error("未查询到机构{}信息", orgId);
                throw new MedicalBusinessException("未查询到机构信息");
            }
            return dto.getUapOrganization();
        } catch (Exception e) {
            log.error("请求用户中心获取机构{}信息失败", orgId, e);
            throw new MedicalBusinessException("请求用户中心获取机构信息失败");
        }
    }

    public DepartmentDto queryDeptNames(String orgId, String loginUserId) {
        DepartmentDto departmentDto = new DepartmentDto();
        List<BaseSelectDto> list = this.queryDeptSelectListByLoginUserIdAndOrg(orgId, loginUserId);
        if (CollectionUtils.isEmpty(list)) {
            return departmentDto;
        }
        departmentDto.setDepartmentCode(list.get(0).getId());
        departmentDto.setDepartmentName(list.get(0).getName());
        return departmentDto;
    }

    public List<BaseSelectDto> queryDeptSelectListByLoginUserIdAndOrg(String orgId, String loginUserId) {

        List<BaseSelectDto> orgDeptList = queryDeptSelectListBaseSelect(orgId);
        List<TimaDept> deptList = this.queryDeptSelectListByLoginUserId(loginUserId);
        if (CollectionUtils.isEmpty(orgDeptList) || CollectionUtils.isEmpty(deptList)) {
            return Collections.emptyList();
        }
        return deptList.stream().filter(h ->
                orgDeptList.stream().anyMatch(t -> t.getId().equals(h.getOrgId()))).map(m -> {
            BaseSelectDto dto = new BaseSelectDto();
            dto.setId(m.getOrgId());
            dto.setName(m.getOrgName());
            return dto;
        }).collect(Collectors.toList());
    }

    public List<TimaDept> queryDeptSelectListByLoginUserId(String loginUserId) {

        String url = uapServiceExtService + "/v1/pt/mdmEmp/org/list?userId=" + loginUserId;
        try {
            JSONObject object = apiTool.doGet(url, JSONObject.class);
            TimaDept mainDept = JSON.parseObject(JSONArray.toJSONString(object.get("main")), TimaDept.class);
            List<TimaDept> list = JSON.parseArray(JSONArray.toJSONString(object.get("list")), TimaDept.class);
            list.add(0, mainDept);
            return list;
        } catch (Exception e) {
            log.error("获取部门列表信息失败", e);
            return new ArrayList<>();
        }
    }

    public List<BaseSelectDto> queryDeptSelectListBaseSelect(String orgId) {

        List<TuapOrganization> list = this.queryDeptSelectList(orgId);
        return Optional.ofNullable(list).orElse(new ArrayList<>()).stream().map(m -> {
            BaseSelectDto dto = new BaseSelectDto();
            dto.setId(m.getId());
            dto.setName(m.getName());
            return dto;
        }).collect(Collectors.toList());
    }

    public List<TuapOrganization> queryDeptSelectList(String orgId) {

        String url = uapServiceExtService + "/v1/pv/department/list?orgId=" + orgId;
        try {
            String object = apiTool.doGet(url, String.class);
            return JSON.parseArray(object, TuapOrganization.class);
        } catch (Exception e) {
            log.error("获取部门列表信息失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 新增uap用户
     */
    public String addUapUser(UapUserVo vo,String loginUserId) {
        String url = uapServiceExtService + "/v1/pt/mdmEmp?loginUserId=" + loginUserId;
        try {
            HashMap<String, Object> paramMap = new HashMap<>();
            HashMap<String, Object> mdmDataEmpMap = new HashMap<>();
            mdmDataEmpMap.put("sex", vo.getSexCode());
            HashMap<String, Object> tuapUserMap = new HashMap<>();
            tuapUserMap.put("name", vo.getName());
            tuapUserMap.put("loginName", vo.getPhone());
            tuapUserMap.put("phone", vo.getPhone());
            tuapUserMap.put("status", 1);
            tuapUserMap.put("userType", -1);
            tuapUserMap.put("email", vo.getEmail());
            paramMap.put("mdmDataEmp", mdmDataEmpMap);
            paramMap.put("roleIds", vo.getRoleIds());
            paramMap.put("tuapUser", tuapUserMap);
            log.info("新增用户信息参数：{}", JSONObject.toJSONString(paramMap));
            return apiTool.doPost(url, paramMap, String.class);
        } catch (Exception e) {
            log.error("新增用户信息异常", e);
            throw new MedicalBusinessException("新增用户信息异常");
        }
    }


    /**
     * 修改uap用户
     */

    public String ediTuapUser(UapUserVo vo,String loginUserId) {
        //查询用户机构列表
        List<String> orgIdList = this.getUserOrgListInfo(vo.getId(),loginUserId);
        String url = uapServiceExtService + "/v1/pt/mdmEmp/" + vo.getId() + "?loginUserId=" + loginUserId;
        try {
            HashMap<String, Object> paramMap = new HashMap<>();
            HashMap<String, Object> mdmDataEmpMap = new HashMap<>();
            mdmDataEmpMap.put("sex", vo.getSexCode());
            HashMap<String, Object> tuapUserMap = new HashMap<>();
            tuapUserMap.put("name", vo.getName());
            if(StrUtil.isEmpty(vo.getPhone())){
                tuapUserMap.put("loginName", vo.getName());
            } else {
                tuapUserMap.put("loginName", vo.getPhone());
            }
            tuapUserMap.put("phone", vo.getPhone());
            tuapUserMap.put("status", 1);
            tuapUserMap.put("userType", -1);
            tuapUserMap.put("email", vo.getEmail());
            tuapUserMap.put("orgId", vo.getOrgId());
            tuapUserMap.put("orgName", vo.getOrgName());
            paramMap.put("mdmDataEmp", mdmDataEmpMap);
            paramMap.put("roleIds", vo.getRoleIds());
            paramMap.put("tuapUser", tuapUserMap);
            paramMap.put("orgIdList", orgIdList);
            log.info("修改用户信息参数：{}", JSONObject.toJSONString(paramMap));
            apiTool.doPut(url, paramMap);
            return vo.getId();
        } catch (Exception e) {
            log.error("修改用户信息异常", e);
            throw new MedicalBusinessException("修改用户信息异常");
        }
    }


    private List<String> getUserOrgListInfo(String userId,String loginUserId) {
        List<String> orgIdList = new ArrayList<>();
        String orgQueryUrl = uapServiceExtService + "/v1/pt/mdmEmp/org/search?source=false&loginUserId=" + loginUserId;
        HashMap<String, Object> orgQueryMap = new HashMap<>();
        orgQueryMap.put("pageIndex", 1);
        orgQueryMap.put("pageSize", 100);
        HashMap<String, Object> filterMap = new HashMap<>();
        filterMap.put("userId", userId);
        filterMap.put("keyword", "");
        orgQueryMap.put("filter", filterMap);
        try {
            JSONObject jsonObject = apiTool.doPost(orgQueryUrl, orgQueryMap, JSONObject.class);
            if (jsonObject != null) {
                JSONArray entities = jsonObject.getJSONArray("entities");
                for (int i = 0; i < entities.size(); i++) {
                    JSONObject orgInfo = entities.getJSONObject(i);
                    String orgId = orgInfo.getString("id");
                    orgIdList.add(orgId);
                }
            }
        } catch (Exception e) {
            log.info("获取用户机构信息异常", e);
            throw new MedicalBusinessException("获取用户机构信息异常");
        }
        return orgIdList;
    }


    /**
     * 根据用户手机号码查询用户信息在uap中是否存在
     */
    public TimaUserInfo getUserInfoByPhone(String phone) {
        String url = uapServiceExtService + "/v1/pt/mdmEmp/byPhone?phone=" + phone;
        try {
            String jsonObject = apiTool.doGet(url, String.class);
            return JSON.parseObject(jsonObject, TimaUserInfo.class);
        } catch (Exception e) {
            throw new MedicalBusinessException("查询用户信息异常");
        }
    }


    /**
     * 根据用户信息获取角色集合信息
     * @param loginUserId
     * @return
     */
    public List<TnewUapRole> queryUserRoleList(String loginUserId) {
        String url = uapServiceExtService + "/v1/pt/mdmRole/queryUserRoleList?userId=" + loginUserId;
        try {
            String jsonObject = apiTool.doGet(url, String.class);
            return JSON.parseArray(jsonObject, TnewUapRole.class);
        } catch (Exception e) {
            log.error("请求用户中心获取角色列表信息失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取token 信息，转发获取uap的token
     */
    public UapToken getToken(String token){
        if (token.contains("ST ")){
            token = token.split("ST ")[1];
        }
        String url = uapServiceExtService + "/v1/pb/util/remoteStorage/" + token;
        return apiService.doGet(url, UapToken.class);
    }



    public JSONObject getUserByToken(String accessToken){
        String url = uapServiceExtService + "/v1/tc/UapAccount/current";
        Map<String, String> headers = new HashMap<>();
        headers.put("X-Token",  accessToken);
        return apiService.doGet(url, headers, JSONObject.class);

    }


}
