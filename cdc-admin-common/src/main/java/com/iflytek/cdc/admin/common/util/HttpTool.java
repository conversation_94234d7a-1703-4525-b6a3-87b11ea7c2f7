package com.iflytek.cdc.admin.common.util;


import com.google.gson.Gson;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

@Configuration
@ConditionalOnProperty(value = "api", havingValue = "http")
public class HttpTool implements ApiTool {
    private final RestTemplate restTemplate = new RestTemplate();

    @Override
    public <T> T doGet(String url, Class<T> responseType) {
        return new Gson().fromJson(HttpUtil.doGet(url), responseType);
    }

    @Override
    public <T> T doGet(String url, Map<String, String> headers, Class<T> responseType) {
        return new Gson().fromJson(HttpUtil.doGet(url, headers), responseType);
    }

    @Override
    public <T> T doPost(String url, Map<String, String> param, Class<T> responseType) {
        Object content = new Object();
        return new Gson().fromJson(HttpUtil.doPostJson(url, new Gson().toJson(content)), responseType);
    }

    @Override
    public <T> T doPost(String url, Object content, Class<T> responseType) {
        return new Gson().fromJson(HttpUtil.doPostJson(url, new Gson().toJson(content)), responseType);
    }

    @Override
    public void doPut(String url, Object content) {
        restTemplate.put(url, content);
    }

    @Override
    public void doDelete(String url) {
        restTemplate.delete(url);
    }
}
