package com.iflytek.cdc.admin.common.dto.workbench;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
@Data
@ApiModel("信号推送规则配置")
public class SignalPushRuleSaveDTO {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 是否重复提醒
     */
    @ApiModelProperty("是否重复提醒-1:是 0:否")
    private String isRepeat;
    /**
     * 重复提醒开始时间
     */
    @ApiModelProperty("重复提醒开始时间")
    private String repeatStartTime;
    /**
     * 重复提醒结束时间
     */
    @ApiModelProperty("重复提醒结束时间")
    private String repeatEndTime;
    /**
     *  重复提醒频次
     */
    @ApiModelProperty("重复提醒频次-15min/30min/1h")
    private String repeatFrequency;

    /**
     * 任务id
     */
    @ApiModelProperty(value = "任务id", required = false)
    private String taskId;
    /**
     * 发送者id
     */
    @ApiModelProperty(value = "发送者id", required = false)
    private String senderId;
    /**
     * 发送者
     */
    @ApiModelProperty(value = "发送者", required = false)
    private String sender;
    /**
     * 接收者id
     */
    @ApiModelProperty("接收者id")
    private String receiverId;
    /**
     * 接收者
     */
    @ApiModelProperty("接收者")
    private String receiver;
    /**
     * 消息配置id
     */
    @ApiModelProperty("消息配置id-1/传染病，2/症候群，3/多渠道，16/多渠道综合预警")
    private String messageConfigId;
    /**
     * 疾病编码
     */
    @ApiModelProperty("疾病编码")
    private String diseaseCode;
    /**
     * 疾病名称
     */
    @ApiModelProperty("疾病名称")
    private String diseaseName;
    /**
     * 风险等级id
     */
    @ApiModelProperty("风险等级id")
    private String riskLevelDetailId;
    /**
     * 信号处理人配置表id
     */
    @ApiModelProperty("信号处理人配置表id")
    private String warningChargePersonTableId;
    /**
     * 是否启用
     */
    @ApiModelProperty("是否启用-1:启用 0:禁用")
    private String isEnable;
}
