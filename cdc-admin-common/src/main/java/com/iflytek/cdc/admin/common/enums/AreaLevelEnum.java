package com.iflytek.cdc.admin.common.enums;

import cn.hutool.core.lang.Pair;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

public enum AreaLevelEnum {

    /**
     * 省级
     */
    PROVINCE(1),

    /**
     * 市级
     */
    CITY(2),

    /**
     * 区县级
     */
    DISTRICT(3),

    /**
     * 街道级
     */
    STREET(4);

    private final Integer value;

    AreaLevelEnum(Integer value) {
        this.value = value;
    }

    public Integer getValue() {
        return this.value;
    }

    /**
     * 获取区域级别及编码
     */
    public static Pair<Integer, List<String>> getAreaLevelAndCodes(List<String> provinceCodes, List<String> cityCodes, List<String> districtCodes, List<String> streetCodes){
        if (!CollectionUtils.isEmpty(streetCodes)){
            return Pair.of(STREET.getValue(), streetCodes);
        }
        if (!CollectionUtils.isEmpty(districtCodes)){
            return Pair.of(DISTRICT.getValue(), districtCodes);
        }
        if (!CollectionUtils.isEmpty(cityCodes)){
            return Pair.of(CITY.getValue(), cityCodes);
        }
        if (!CollectionUtils.isEmpty(provinceCodes)){
            return Pair.of(PROVINCE.getValue(), provinceCodes);
        }
        return Pair.of(null, new ArrayList<>());
    }
}
