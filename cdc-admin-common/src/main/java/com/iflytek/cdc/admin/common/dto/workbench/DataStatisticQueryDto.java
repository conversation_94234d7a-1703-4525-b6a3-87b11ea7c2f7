package com.iflytek.cdc.admin.common.dto.workbench;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class DataStatisticQueryDto {

    @ApiModelProperty(value = "用户id")
    private String loginUserId;

    @ApiModelProperty(value = "用户名")
    private String loginUserName;

    @NotBlank(message = "系统来源编码不能为空")
    @ApiModelProperty(value = "系统来源编码 CDC-EPI-INVEST或CDC-ECD-PLATFORM")
    private String systemCode;

    @ApiModelProperty(value = "统计类型，1-事件定性，2-事件等级")
    private String type;

    @NotBlank(message = "开始日期不能为空")
    @ApiModelProperty(value = "开始日期")
    private String startDate;

    @NotBlank(message = "结束日期不能为空")
    @ApiModelProperty(value = "结束日期")
    private String endDate;

    //以下为监测预警参数
    @ApiModelProperty(value = "开始日期", hidden = true)
    private String minFullDate;

    @ApiModelProperty(value = "结束日期", hidden = true)
    private String maxFullDate;

    @ApiModelProperty(value = "app编码")
    private String appCode;

    @ApiModelProperty(value = "自定义预警类型")
    private String customizedWarningType;

    @ApiModelProperty(value = "预警类型")
    private String warningType;

    @ApiModelProperty(value = "预警类型")
    private String eventType;

    @ApiModelProperty(value = "疾病编码")
    private String diseaseCode;

    @ApiModelProperty(value = "疾病名称")
    private String diseaseName;
}
