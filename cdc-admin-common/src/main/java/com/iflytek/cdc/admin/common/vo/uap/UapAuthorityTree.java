package com.iflytek.cdc.admin.common.vo.uap;

import lombok.Data;

import java.util.List;

@Data
public class UapAuthorityTree {
    private String appId;
    private String appName;
    private boolean checked;
    private String code;
    private String icon;
    private String id;
    private String name;
    private boolean noChildOrg;
    private List<UapAuthorityTree> nodes;
    private String pid;
    private String remark;
    private int sort;
    private int status;
    private String type;
    private String url;
    private int urlType;
    private String value;
}
