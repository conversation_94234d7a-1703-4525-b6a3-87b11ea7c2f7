package com.iflytek.cdc.admin.common.util.easyexcel;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IORuntimeException;
import cn.hutool.core.io.IoUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.iflytek.cdc.admin.common.constants.CommonConstants;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static com.alibaba.excel.EasyExcelFactory.read;

/**
 * @description: 导出工具类
 * @author: shenghuang
 * @create: 2021-01-28 19:37
 **/
@Slf4j
public class EasyExcelUtil {
    //分页条数 6万
    private static final int PAGESIZE = 60000;

    private EasyExcelUtil() {
    }

    /**
     * 获取默认表头内容的样式
     *
     * @return
     */
    private static HorizontalCellStyleStrategy getDefaultHorizontalCellStyleStrategy() {
        /** 表头样式 **/
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        headWriteCellStyle.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
        // 字体大小
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontName("微软雅黑");
        headWriteFont.setFontHeightInPoints((short) 10);
        headWriteCellStyle.setWriteFont(headWriteFont);
        //设置表头居中对齐
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        /** 内容样式 **/
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        contentWriteCellStyle.setWrapped(true);
        // 内容字体样式（名称、大小）
        WriteFont contentWriteFont = new WriteFont();
        contentWriteFont.setFontName("微软雅黑");
        contentWriteFont.setFontHeightInPoints((short) 10);
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        //设置内容垂直居中对齐
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        //设置内容水平居中对齐
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        //设置边框样式
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        // 头样式与内容样式合并
        return new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
    }

    /**
     * @param response
     * @param data     查询结果
     * @param fileName 导出文件名称
     * @param clazz    映射实体class类
     * @param <T>      查询结果类型
     * @throws Exception
     * <AUTHOR>
     * @createDate 2020-11-16
     */
    public static <T> void writeExcelList(HttpServletRequest request,HttpServletResponse response, List<T> data, String fileName, Class<? extends Object> clazz) {
        OutputStream out = null;
        try {
            List<List<T>> lists = ListSubUtil.splitList(data, PAGESIZE); // 分割的集合
            out = getOutputStream(fileName, request, response);
            ExcelWriterBuilder excelWriterBuilder = EasyExcelFactory.write(out, clazz).excelType(ExcelTypeEnum.XLSX).registerWriteHandler(getDefaultHorizontalCellStyleStrategy());
            if (lists.size() == 1) {
                excelWriterBuilder.sheet(fileName).doWrite(data);
            } else {
                ExcelWriter excelWriter = excelWriterBuilder.build();
                ExcelWriterSheetBuilder excelWriterSheetBuilder;
                WriteSheet writeSheet;
                for (int i = 1; i <= lists.size(); i++) {
                    excelWriterSheetBuilder = new ExcelWriterSheetBuilder(excelWriter);
                    excelWriterSheetBuilder.sheetNo(i);
                    excelWriterSheetBuilder.sheetName(fileName + i);
                    writeSheet = excelWriterSheetBuilder.build();
                    excelWriter.write(lists.get(i - 1), writeSheet);
                }
                excelWriter.finish();
            }
        } catch (Exception e) {
            log.error("导出失败！",e);
            throw new MedicalBusinessException("导出失败!");
        }
    }

    private static OutputStream getOutputStream(String fileName, HttpServletRequest request, HttpServletResponse response) {
        try {
            String userAgent = request.getHeader("User-Agent");
            if (userAgent.contains("Firefox")) {
                fileName = new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1);
            } else {
                fileName = URLEncoder.encode(fileName, "UTF-8");
            }
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
            return response.getOutputStream();
        } catch (Exception e) {
            log.error("导出失败！",e);
            throw new MedicalBusinessException("导出失败!");
        }
    }

    private static OutputStream getRespOutputStream(String fileName, HttpServletRequest request, HttpServletResponse response) {
        try {
            String userAgent = request.getHeader("User-Agent");
            if (userAgent.contains("Firefox")) {
                fileName = new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1);
            } else {
                fileName = URLEncoder.encode(fileName, "UTF-8");
            }
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
            return response.getOutputStream();
        } catch (Exception e) {
            log.error("导出失败！", e);
            throw new MedicalBusinessException("导出失败!");
        }
    }


    /**
     * 读取excel数据
     *
     * @param file   文件
     * @param clazz  对象
     * @param sheet  起始sheet页
     * @param number 起始行数
     * @param <T>
     * @return
     */
    public static <T> List<T> readExcelData(MultipartFile file, Class<T> clazz, int sheet, int number) {
        try {
            InputStream inputStream = file.getInputStream();
            return read(inputStream)
                    .registerConverter(new StringConverterUtil())
                    .head(clazz)
                    .sheet(sheet)
                    .headRowNumber(number)
                    .doReadSync();
        } catch (Exception e) {
            log.error("读取excel异常",e);
            throw new MedicalBusinessException("读取excel异常!");
        }
    }



    public static <T> void writeSelectedExcelList(HttpServletRequest request,HttpServletResponse response, List<T> data, String fileName, Class<? extends Object> clazz) {
        OutputStream out = null;
        try {
            Map<Integer, ExcelSelectedResolve> selectedMap = resolveSelectedAnnotation(clazz);
            List<List<T>> lists = ListSubUtil.splitList(data, PAGESIZE); // 分割的集合
            out = getOutputStream(fileName, request, response);
            ExcelWriterBuilder excelWriterBuilder = EasyExcelFactory.write(out, clazz).excelType(ExcelTypeEnum.XLSX).
                    registerWriteHandler(getDefaultHorizontalCellStyleStrategy()).registerWriteHandler(new SelectedSheetWriteHandler(selectedMap));
            if (lists.size() == 1) {
                excelWriterBuilder.sheet(fileName).doWrite(data);
            } else {
                ExcelWriter excelWriter = excelWriterBuilder.build();
                ExcelWriterSheetBuilder excelWriterSheetBuilder;
                WriteSheet writeSheet;
                for (int i = 1; i <= lists.size(); i++) {
                    excelWriterSheetBuilder = new ExcelWriterSheetBuilder(excelWriter);
                    excelWriterSheetBuilder.sheetNo(i);
                    excelWriterSheetBuilder.sheetName(fileName + i);
                    writeSheet = excelWriterSheetBuilder.build();
                    excelWriter.write(lists.get(i - 1), writeSheet);
                }
                excelWriter.finish();
            }
        } catch (Exception e) {
            log.error("导出失败！",e);
            throw new MedicalBusinessException("导出失败!");
        }
    }

    public static <T> void exportExcel(HttpServletRequest request,HttpServletResponse response, List<T> data, String fileName, Class<? extends Object> clazz) {
        OutputStream out = null;
        try {
            Map<Integer, ExcelSelectedResolve> selectedMap = resolveSelectedAnnotation(clazz);
            List<List<T>> lists = ListSubUtil.splitList(data, PAGESIZE); // 分割的集合
            out = getRespOutputStream(fileName, request, response);
            ExcelWriterBuilder excelWriterBuilder = EasyExcelFactory.write(out, clazz).excelType(ExcelTypeEnum.XLSX).
                    registerWriteHandler(getDefaultHorizontalCellStyleStrategy()).registerWriteHandler(new SelectedSheetWriteHandler(selectedMap));
            if (lists.size() == 1) {
                excelWriterBuilder.sheet(fileName).doWrite(data);
            } else {
                ExcelWriter excelWriter = excelWriterBuilder.build();
                ExcelWriterSheetBuilder excelWriterSheetBuilder;
                WriteSheet writeSheet;
                for (int i = 1; i <= lists.size(); i++) {
                    excelWriterSheetBuilder = new ExcelWriterSheetBuilder(excelWriter);
                    excelWriterSheetBuilder.sheetNo(i);
                    excelWriterSheetBuilder.sheetName(fileName + i);
                    writeSheet = excelWriterSheetBuilder.build();
                    excelWriter.write(lists.get(i - 1), writeSheet);
                }
                excelWriter.finish();
            }
        } catch (Exception e) {
            log.error("导出失败！", e);
            throw new MedicalBusinessException("导出失败!");
        }
    }

    /**
     * 写入错误信息到服务器
     * @param data
     * @param clazz
     * @param sheetName
     * @param <T>
     * @return
     */
    public static <T> String writeErrorExcel(List<T> data,Class<? extends Object> clazz,String sheetName) {
        try {
            String fileName = UUID.randomUUID().toString().replace("-", "") + ".xlsx";
            //导出excel临时文件
            EasyExcel.write(new FileOutputStream(CommonConstants.TEMP_FILE_PATH + fileName), clazz)
                    .registerWriteHandler(EasyExcelExportStylerUtil.getDefaultWriteHandler())
                    .sheet(sheetName).doWrite(data);
            return fileName;
        } catch (FileNotFoundException e) {
            log.error("导出失败！",e);
            throw new MedicalBusinessException("导出失败!");
        }
    }

    public static void exportErrorExcel(HttpServletResponse response,String filePath) {
        filePath = CommonConstants.TEMP_FILE_PATH + filePath;
        try (FileInputStream inputStream = new FileInputStream(filePath); ServletOutputStream outputStream = response.getOutputStream()) {
            String downLoadFileName = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename=" + downLoadFileName + ".xlsx");
            IoUtil.copy(inputStream, outputStream);
        } catch (IOException e) {
            log.error("导入文件下载失败: {}, 错误信息：", filePath, e);
            throw new MedicalBusinessException("下载失败!");
        } finally {
            try {
                // 删除临时文件
                FileUtil.del(filePath);
            } catch (IORuntimeException e) {
                log.error("临时文件删除失败: {}, 错误信息：", filePath, e);
                throw new MedicalBusinessException("下载失败!");
            }
        }
    }

    /**
     * 解析表头类中的下拉注解
     * @param head 表头类
     * @param <T> 泛型
     * @return Map<下拉框列索引, 下拉框内容> map
     */
    private static <T> Map<Integer, ExcelSelectedResolve> resolveSelectedAnnotation(Class<T> head) {
        Map<Integer, ExcelSelectedResolve> selectedMap = new HashMap<>();

        // getDeclaredFields(): 返回全部声明的属性；getFields(): 返回public类型的属性
        Field[] fields = head.getDeclaredFields();
        for (int i = 0; i < fields.length; i++){
            Field field = fields[i];
            // 解析注解信息
            ExcelSelected selected = field.getAnnotation(ExcelSelected.class);
            ExcelProperty property = field.getAnnotation(ExcelProperty.class);
            if (selected != null) {
                ExcelSelectedResolve excelSelectedResolve = new ExcelSelectedResolve();
                String[] source = excelSelectedResolve.resolveSelectedSource(selected);
                if (source != null && source.length > 0){
                    excelSelectedResolve.setSource(source);
                    excelSelectedResolve.setFirstRow(selected.firstRow());
                    excelSelectedResolve.setLastRow(selected.lastRow());
                    if (property != null && property.index() >= 0){
                        selectedMap.put(property.index(), excelSelectedResolve);
                    } else {
                        selectedMap.put(i, excelSelectedResolve);
                    }
                }
            }
        }
        return selectedMap;
    }
}
