package com.iflytek.cdc.admin.common.enums;

/**
 * <AUTHOR>
 * @description 角色枚举
 * @date 2021/5/11 13:50
 */
public enum RoleTypeEnum {
    /**
     * 管理员
     */
    MANAGER(1,"管理员"),
    /**
     * 主任
     */
    DIRECTOR(2,"主任"),
    /**
     * 科长
     */
    CHIEF(3,"科长"),
    /**
     * 科员
     */
    MEMBER(4,"科员");

    private Integer code;
    private String desc;

    RoleTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据名称获取RoleTypeEnum
     * @param name
     * @return
     */
    public static RoleTypeEnum getCodeByName(String name) {
        for (RoleTypeEnum roleTypeEnum : RoleTypeEnum.values()) {
            if (name.contains(roleTypeEnum.getDesc())) {
                return roleTypeEnum;
            }
        }
        return null;
    }
}
