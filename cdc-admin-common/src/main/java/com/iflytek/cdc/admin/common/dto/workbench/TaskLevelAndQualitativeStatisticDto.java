package com.iflytek.cdc.admin.common.dto.workbench;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class TaskLevelAndQualitativeStatisticDto {

    @ApiModelProperty(value = "事件数量")
    private List<PortalTaskCountStatisticDto> taskCount;

    @ApiModelProperty(value = "空间分布")
    private List<PortalTaskRegionDistributionStatisticDto> regionDistribution;
}
