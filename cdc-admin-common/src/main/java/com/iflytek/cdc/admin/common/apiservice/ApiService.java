package com.iflytek.cdc.admin.common.apiservice;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.iflytek.cdc.admin.common.util.ApiTool;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class ApiService {

    @Autowired
    private ApiTool apiTool;

    /**
     * Get 请求
     */
    public <T> T doGet(String url, Class<T> responseType){
        return apiTool.doGet(url, responseType);
    }

    /**
     * Get 请求
     */
    public <T> T doGet(String url, Map<String, String> headers, Class<T> responseType){
        return apiTool.doGet(url, headers, responseType);
    }

    /**
     * 返回集合数据
     */
    public <T> List<T> doGetList(String url, Class<T> responseType){
        return apiTool.doGet(url,  JSONArray.class).toJavaList(responseType);
    }

    /**
     * 返回集合数据
     */
    public <T> T doPost(String url, Object requestBody, Class<T> responseType){
        try {
            log.info(url);
            return apiTool.doPost(url, requestBody,  responseType);
        }catch (Exception e){
            log.error(e.getLocalizedMessage(), e);
            throw new MedicalBusinessException("调用出错："+ e.getLocalizedMessage());
        }
    }


    /**
     * 返回集合数据
     */
    public <T> List<T> doPostList(String url, Object requestBody, Class<T> responseType){
        return apiTool.doPost(url, requestBody,  JSONArray.class).toJavaList(responseType);
    }

    /**
     * post请求分页接口数据
     */
    public <T>  PageInfo<T> postPageInfo(String url, Object requestBody){
        Gson gson = new Gson();
        return  gson.fromJson(gson.toJson(apiTool.doPost(url, requestBody, PageInfo.class)), new TypeToken<PageInfo<T>>(){}.getType());
    }
}
