package com.iflytek.cdc.admin.mapper.brief;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.admin.dto.brief.TemplateSearchDto;
import com.iflytek.cdc.admin.entity.brief.IndicatorsEntity;
import com.iflytek.cdc.admin.entity.brief.TemplateEntity;
import com.iflytek.cdc.admin.vo.brief.TemplateSettingVo;
import com.iflytek.cdc.admin.vo.brief.TemplateVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * ${comments}
 * 
 * <AUTHOR>
 * @date 2024-12-30 14:52:02
 */
@Mapper
public interface BriefTemplateMapper extends BaseMapper<TemplateEntity> {

    List<TemplateVo> listBySearch(TemplateSearchDto searchDto);

    void deleteIndicatorsByTemplateId(String templateId, String attachmentFlag);

    void insertBatchIndicators(List<IndicatorsEntity> indicators);

    TemplateSettingVo getSettingDetail(String id);

    List<IndicatorsEntity> listIndicators(String id);
}
