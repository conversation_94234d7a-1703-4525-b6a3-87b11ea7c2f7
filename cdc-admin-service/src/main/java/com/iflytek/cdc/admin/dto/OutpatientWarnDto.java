package com.iflytek.cdc.admin.dto;

import com.iflytek.cdc.admin.entity.TbCdcmrOutpatientWarnRule;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class OutpatientWarnDto {
    /**
     * 主键
     */
    private String id;

    /**
     * 门诊类型编码
     */
    private String outpatientTypeCode;

    /**
     * 门诊类型名称
     */
    private String outpatientTypeName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 开启状态：0禁用1启用
     */
    private Integer status;

    /**
     * 删除标识：0未删除1删除
     */
    private Integer isDeleted;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 观察期(天)
     */
    private Integer observation;

    /**
     * 病例环比增长比例(%)
     */
    private Integer growthRate;

    /**
     * 生命周期(天)
     */
    private Integer maxLifeCycle;

    /**
     * 预警短信发送类型编码
     */
    private String smsSendTypeCode;

    /**
     * 预警短信发送类型描述
     */
    private String smsSendTypeDesc;

    private List<TbCdcmrOutpatientWarnRule> ruleList;

    Integer ruleCount;
}
