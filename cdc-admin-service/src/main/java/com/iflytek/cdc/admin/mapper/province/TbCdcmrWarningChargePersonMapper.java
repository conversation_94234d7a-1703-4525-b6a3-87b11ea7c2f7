package com.iflytek.cdc.admin.mapper.province;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.admin.dto.WarningChargePersonQuery;
import com.iflytek.cdc.admin.entity.TbCdcmrWarningChargePerson;
import com.iflytek.cdc.admin.model.mr.dto.DealPersonQueryDTO;
import com.iflytek.cdc.admin.vo.WarningChargePersonVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TbCdcmrWarningChargePersonMapper extends BaseMapper<TbCdcmrWarningChargePerson> {
    /**
     * 根据id插入或更新
     */
    void mergeInto(@Param("entities") List<TbCdcmrWarningChargePerson> entities);

    /**
     * 查询传染病处理人
     */
    List<WarningChargePersonVO> listDiseaseConfig(WarningChargePersonQuery query);

    /**
     * 根据id查询
     */
    TbCdcmrWarningChargePerson loadById(String id);

    /**
     * 根据id批量加载
     */
    List<TbCdcmrWarningChargePerson> bulkLoadByIds(List<String> ids);

    List<WarningChargePersonVO> getDealPersonInfoBy(List<DealPersonQueryDTO> dtoList);

    TbCdcmrWarningChargePerson getDealPersonInfoByDiseaseCodeAndRiskLevelDetailId(DealPersonQueryDTO dto);

    WarningChargePersonVO loadByDiseaseCode(@Param("diseaseCode") String diseaseCode,
                                            @Param("warningType") String warningType);

    List<WarningChargePersonVO> listMultichannelDiseaseConfig(WarningChargePersonQuery query);

    List<WarningChargePersonVO> listIntegratedDiseaseConfig(WarningChargePersonQuery query);

    /**
     * 综合预警责任人查询
     * 综合预警在各风险等级都只有一个责任人
     */
    WarningChargePersonVO getIntegratedWarningDealPersonInfo(String warningType);

}
