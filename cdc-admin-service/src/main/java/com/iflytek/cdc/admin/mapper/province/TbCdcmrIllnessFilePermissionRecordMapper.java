package com.iflytek.cdc.admin.mapper.province;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrIllnessFilePermissionRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbCdcmrIllnessFilePermissionRecordMapper extends BaseMapper<TbCdcmrIllnessFilePermissionRecord> {


    List<TbCdcmrIllnessFilePermissionRecord> getAuthApplyRecordListByUserId(
            @Param("loginUserId")String loginUserId,@Param("record") TbCdcmrIllnessFilePermissionRecord permissionRecord);

    List<TbCdcmrIllnessFilePermissionRecord> getAuthApplyRecordListByCreateUserId( @Param("loginUserId")String loginUserId
            ,@Param("record") TbCdcmrIllnessFilePermissionRecord permissionRecord);
}
