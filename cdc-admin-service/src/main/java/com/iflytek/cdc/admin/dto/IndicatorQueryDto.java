package com.iflytek.cdc.admin.dto;

import com.iflytek.cdc.admin.vo.CdcmrIndicatorVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据指标查询
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class IndicatorQueryDto extends PageInfoDTO {

    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("指标ID")
    private String indicatorLabel;

    @ApiModelProperty("指标名称")
    private String indicatorName;

    @ApiModelProperty("分析对象 （分析对象 code字符串 多个使用 英文逗号, 拼接）")
    private String analysisTargetCodes;

    @ApiModelProperty("指标类型")
    private String indicatorType;

    @ApiModelProperty("关键词过滤，模糊匹配指标ID或名称")
    private String keyword;

    @ApiModelProperty("适用疾病（指标定义模块过滤用）")
    private CdcmrIndicatorVO.ApplicableDisease applicableDiseaseQuery;

    /**
     * 业务过程
     */
    @ApiModelProperty("业务过程")
    private String businessProcess;
}
