package com.iflytek.cdc.admin.dto.amap.inputtips;

import java.util.List;
public class InputTips {

    private List<Tips> tips;
    private String status;
    private String info;
    private String infocode;
    private String count;
    public void setTips(List<Tips> tips) {
        this.tips = tips;
    }
    public List<Tips> getTips() {
        return tips;
    }

    public void setStatus(String status) {
        this.status = status;
    }
    public String getStatus() {
        return status;
    }

    public void setInfo(String info) {
        this.info = info;
    }
    public String getInfo() {
        return info;
    }

    public void setInfocode(String infocode) {
        this.infocode = infocode;
    }
    public String getInfocode() {
        return infocode;
    }

    public void setCount(String count) {
        this.count = count;
    }
    public String getCount() {
        return count;
    }
}
