package com.iflytek.cdc.admin.model.mr.dto;

import com.iflytek.cdc.admin.entity.mr.TbCdcmrMultichannelTopicConfig;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class TopicConfigEditDTO {

    @ApiModelProperty(value = "专题id")
    @NotNull
    private String topicId;

    @ApiModelProperty(value = "专题配置列表")
    private List<TbCdcmrMultichannelTopicConfig> topicConfigList;

}
