package com.iflytek.cdc.admin.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * tb_cdcmr_customized_warning_rule
 *
 * <AUTHOR>
@Data
public class TbCdcmrCustomizedWarningRule implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "自定义标准规则ID")
    private String id;
    @ApiModelProperty(value = "自定义标准ID")
    private String customizedWarnId;
    @ApiModelProperty(value = "规则说明")
    private String description;
    @ApiModelProperty(value = "潜伏期")
    private Integer incubation;
    @ApiModelProperty(value = "生命周期")
    private Integer maxLifeCycle;
    @ApiModelProperty(value = "短信发送类型编码")
    private String smsSendTypeCode;
    @ApiModelProperty(value = "短信发送类型描述")
    private String smsSendTypeDesc;
    @ApiModelProperty(value = "状态")
    private Integer status;
    @ApiModelProperty(value = "删除标识")
    private String deleteFlag;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "创建者")
    private String creator;
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    @ApiModelProperty(value = "更新者")
    private String updater;
}