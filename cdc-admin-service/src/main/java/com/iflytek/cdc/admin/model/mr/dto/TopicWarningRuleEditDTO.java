package com.iflytek.cdc.admin.model.mr.dto;

import com.iflytek.cdc.admin.entity.mr.TbCdcmrMultichannelTopicWarningRule;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class TopicWarningRuleEditDTO {

    @ApiModelProperty(value = "专题id")
    @NotNull
    private String topicId;

    @ApiModelProperty(value = "专题预警规则")
    private TbCdcmrMultichannelTopicWarningRule rule;

}
