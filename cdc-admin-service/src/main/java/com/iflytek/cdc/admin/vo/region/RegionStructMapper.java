package com.iflytek.cdc.admin.vo.region;

import com.iflytek.cdc.admin.constant.AreaLevelEnum;
import com.iflytek.cdc.admin.dto.GroupRegionDTO;
import com.iflytek.cdc.admin.entity.TbCdcmrRegion;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

@Mapper
public interface RegionStructMapper {

    RegionStructMapper INSTANCE = Mappers.getMapper(RegionStructMapper.class);


    GroupRegionVO groupEntityToVO(TbCdcmrRegion entity);

    TbCdcmrRegion groupDtoToEntity(GroupRegionDTO dto);

    TbCdcmrRegion copy(TbCdcmrRegion tbCdcmrRegion);

    GroupRegionFileVO voToFileVO(GroupRegionVO groupRegionVO);

    /**
     * 小区/组 的实体转换
     */
    default TbCdcmrRegion buildGroupRegionFrom(GroupRegionDTO groupRegionDTO) {
        TbCdcmrRegion tbCdcmrRegion = RegionStructMapper.INSTANCE.groupDtoToEntity(groupRegionDTO);
        tbCdcmrRegion.setRegionLevel(AreaLevelEnum.GROUP.getLevel());
        tbCdcmrRegion.setRegionCode(groupRegionDTO.getGroupCode());
        tbCdcmrRegion.setRegionName(groupRegionDTO.getGroupName());
        tbCdcmrRegion.setAliasName(groupRegionDTO.getGroupAliasName());
        tbCdcmrRegion.setParentRegionCode(tbCdcmrRegion.getVillageCode());
        //
        tbCdcmrRegion.setAddressDetail(tbCdcmrRegion.generateAddressDetail());

        return tbCdcmrRegion;
    }

    static TbCdcmrRegion updateRegionByParent(TbCdcmrRegion parent, @MappingTarget TbCdcmrRegion target) {
        if (parent == null) {
            return target;
        }

        if (parent.getRegionLevel() >= AreaLevelEnum.PROVINCE.getLevel() ) {
            target.setProvinceCode( parent.getProvinceCode() );
            target.setProvinceName( parent.getProvinceName() );
        }
        if ( parent.getRegionLevel() >= AreaLevelEnum.CITY.getLevel() ) {
            target.setCityCode(parent.getCityCode());
            target.setCityName(parent.getCityName());
        }
        if ( parent.getRegionLevel() >= AreaLevelEnum.DISTRICT.getLevel() ) {
            target.setDistrictCode( parent.getDistrictCode() );
            target.setDistrictName( parent.getDistrictName() );
        }
        if ( parent.getRegionLevel() >= AreaLevelEnum.STREET.getLevel() ) {
            target.setStreetCode( parent.getStreetCode() );
            target.setStreetName( parent.getStreetName() );
        }
        if ( parent.getRegionLevel() >= AreaLevelEnum.VILLAGE.getLevel() ) {
            target.setVillageCode( parent.getVillageCode() );
            target.setVillageName( parent.getVillageName() );
        }
        if ( parent.getRegionLevel() >= AreaLevelEnum.GROUP.getLevel() ) {
            target.setGroupCode( parent.getGroupCode() );
            target.setGroupName( parent.getGroupName() );
        }

        return target;
    }

}
