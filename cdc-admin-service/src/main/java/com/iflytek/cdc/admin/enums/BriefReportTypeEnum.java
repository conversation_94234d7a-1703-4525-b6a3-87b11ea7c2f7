package com.iflytek.cdc.admin.enums;

public enum BriefReportTypeEnum {
    /**
     * 传染病疫情分析
     */
    INFECTED_EPIDEMIC_ANALYSIS("INFECTED_EPIDEMIC_ANALYSIS", "传染病疫情分析"),

    /**
     * 传染病专病疫情分析
     */
    INFECTED_SPECIAL_EPIDEMIC_ANALYSIS("INFECTED_SPECIAL_EPIDEMIC_ANALYSIS", "传染病专病疫情分析"),


    /**
     * 传染病质量分析
     */
    INFECTED_QUALITY_ANALYSIS("INFECTED_QUALITY_ANALYSIS", "传染病质量分析"),

    /**
     * 医疗机构数据采集质量分析
     */
    DATA_COLLECTION_QUALITY_ANALYSIS("DATA_COLLECTION_QUALITY_ANALYSIS", "医疗机构数据采集质量分析"),

    /**
     * 传染病人工报告卡质量分析
     */
    INFECTED_REPORT_CARD_QUALITY_ANALYSIS("INFECTED_REPORT_CARD_QUALITY_ANALYSIS", "传染病人工报告卡质量分析");


    private final String code;
    private final String description;

    BriefReportTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }
}
