package com.iflytek.cdc.admin.model.processCheck.dto;

import com.iflytek.cdc.admin.model.mr.dto.CommonQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class ProcessCheckQueryDTO extends CommonQuery {

    @ApiModelProperty("疾病分类")
    private String type;

    @ApiModelProperty("疾病名称")
    private String diseaseName;

    @ApiModelProperty("审核时效")
    private Integer timeLimit;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("开始时间")
    private Date startDate;

    @ApiModelProperty("结束时间")
    private Date endDate;

}
