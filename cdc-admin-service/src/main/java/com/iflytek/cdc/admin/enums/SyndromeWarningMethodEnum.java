package com.iflytek.cdc.admin.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;

@Getter
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum SyndromeWarningMethodEnum {

    SCAM("SCAM", "单病例预警法"),

    EAT("EAT", "聚集性疫情法"),

    SWA("SWA", "症候群聚集预警算法"),

    ;

    private final String value;
    private final String label;

    SyndromeWarningMethodEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

}
