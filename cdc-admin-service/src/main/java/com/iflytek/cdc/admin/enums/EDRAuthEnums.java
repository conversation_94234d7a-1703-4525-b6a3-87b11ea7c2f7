package com.iflytek.cdc.admin.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * 区域机构映射
 */
@Getter
public enum EDRAuthEnums {

    SUCCESS("20001","成功"),
    NO_APPLY_RECODE("40001", "请申请档案查看权限"),
    APPLY_PENDING("40002", "档案查看申请审批中"),
    APPLY_REJECT("40003", "档案查看申请被驳回驳回原因：%s"),
    NO_CHECK_PERMISSION("40004", "请确保拥有档案查看权限"),
    CHECK_PERMISSION_EXPIRED("40005", "档案查看权限已过期请重新申请"),

    SUCCESS_STATUS("1", "审批通过"),
    PENDING_STATUS("0", "审批中"),
    REJECT_STATUS("-1", "审批驳回"),

    DEREGISTER("recordCancellation","deregister"),
    MODIFY("recordMaintenance","modify")
    ;

    private final String code;
    private final String msg;


    EDRAuthEnums(String code,String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getFormattedMsg(Object... args) {
        return String.format(this.msg, args);
    }

    public static String getValueByCode(String code) {
        if (StringUtils.isBlank(code)){
            return null;
        }else {
            return Arrays.stream(EDRAuthEnums.values())
                    .filter(item -> item.getCode().equals(code))
                    .findFirst()
                    .map(EDRAuthEnums::getMsg)
                    .orElse(null);
        }
    }

}
