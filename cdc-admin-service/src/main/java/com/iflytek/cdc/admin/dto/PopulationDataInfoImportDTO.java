package com.iflytek.cdc.admin.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.iflytek.cdc.admin.util.DateUtils;
import lombok.Data;

/**
 * 人员信息导入
 */
@Data
public class PopulationDataInfoImportDTO {

    @ExcelProperty("省")
    private String provinceName;

    @ExcelProperty("市")
    private String cityName;

    @ExcelProperty("县区")
    private String districtName;

    @ExcelProperty("街道")
    private String streetName;

    @ExcelProperty("常住人口数")
    @ColumnWidth(15)
    private String residentPopulation;

    @ExcelProperty("户籍人口数")
    @ColumnWidth(15)
    private String registeredPopulation;

    @ExcelProperty("GDP数据（亿）")
    @ColumnWidth(12)
    private String gdp;

    @ExcelProperty("城镇居民可支配收入（元）")
    @ColumnWidth(20)
    private String urbanDpi;

    @ExcelProperty("农村居民可支配收入（元）")
    @ColumnWidth(20)
    private String ruralDpi;

    @ExcelProperty("统计日期")
    @ColumnWidth(12)
    @DateTimeFormat(DateUtils.SHORT_DATE_FORMAT)
    private String statDate;
}
