package com.iflytek.cdc.admin.entity.mr;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 病例审核权限配置表;
 * <AUTHOR> dingyuan
 * @date : 2025-2-13
 */
@ApiModel(value = "病例审核权限配置表")
@Data
public class TbCdcmrCheckAuthorityConfig implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * uuid，主键
     */
    @ApiModelProperty(value = "uuid，主键")
    private String id ;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String type ;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name ;

    /**
     * 疾病code
     */
    @ApiModelProperty(value = "疾病code")
    private String diseaseCode ;

    /**
     * 疾病名称
     */
    @ApiModelProperty(value = "疾病名称")
    private String diseaseName ;

    /**
     * 症候群人群范围
     */
    @ApiModelProperty(value = "症候群人群范围")
    private String populationScope ;

    /**
     * 审核时效
     */
    @ApiModelProperty(value = "审核时效")
    private Integer timeLimit ;

    /**
     * 审核层级
     */
    @ApiModelProperty(value = "审核层级")
    private String checkLevel ;

    /**
     * 审核责任人
     */
    @ApiModelProperty(value = "审核责任人")
    private String checkPerson ;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Integer status ;

    /**
     * 删除标识
     */
    @ApiModelProperty(value = "删除标识")
    private String deleteFlag ;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String creator ;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    private String creatorId ;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime ;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updater ;

    /**
     * 更新人id
     */
    @ApiModelProperty(value = "更新人id")
    private String updaterId ;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime ;

}
