package com.iflytek.cdc.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel("基础公用视图")
@Data
public class BaseVO {
    @ApiModelProperty("创建者id")
    private String creatorId;
    @ApiModelProperty("创建者")
    private String creator;
    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("修改者id")
    private String updaterId;
    @ApiModelProperty("修改者")
    private String updater;
    @ApiModelProperty("修改时间")
    private Date updateTime;


}
