package com.iflytek.cdc.admin.dto;

import com.iflytek.cdc.admin.annotation.ExcelColumnName;
import lombok.Data;

@Data
public class SympWarnRuleExportDataDto {

    private String id;

    @ExcelColumnName(name = "症状名称")
    private String symptomName;

    @ExcelColumnName(name = "症状潜伏期")
    private String incubation;

    /**
     * 生命周期(天)
     */
    @ExcelColumnName(name = "信号最大生命周期")
    private String maxLifeCycle;

    @ExcelColumnName(name = "监测对象")
    private String monitorObject;

    @ExcelColumnName(name = "时间范围")
    private String timeRange;

    private String timeRangeUnit;

    @ExcelColumnName(name = "症状计算方式")
    private String formula;

    @ExcelColumnName(name = "病例数量")
    private String medicalCount;

    private String param1;

    private String param2;

    private String param3;

    private String param4;

}
