
package com.iflytek.cdc.admin.dto.amap.search;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName Pois - v2.0
 * @Description 高德地图 搜索服务-关键字查询 返回值解析类
 * <AUTHOR>
 * @Date 2021/6/11 9:34
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PoisV2 implements Serializable {

    private static final long serialVersionUID = -2171372272085388425L;

    private String typecode;
    private String address;
    private String adname;
    private String citycode;
    private String pcode;
    private String adcode;
    private String pname;
    private String cityname;
    private String name;
    private String location;
    private String id;
    private String type;

    /**
     * 自定义扩展字段
     */
    private double similarRate;
    private double similarRateV2;
    private int oriOrder;
}