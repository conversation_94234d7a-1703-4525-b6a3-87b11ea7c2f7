package com.iflytek.cdc.admin.enums;

public enum BusinessTypeEnum {
    POISONING("公卫类", "poisoning"),
    INFECTED("传染病", "infected");

    private String name;
    private String code;


    private BusinessTypeEnum(String name, String code) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        for (BusinessTypeEnum businessType : BusinessTypeEnum.values()) {
            if (businessType.getCode().equals(code)) {
                return businessType.getName();
            }
        }
        return code;
    }
}
