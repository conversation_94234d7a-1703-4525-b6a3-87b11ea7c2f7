
package com.iflytek.cdc.admin.dto.amap.geocode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName Pois
 * @Description  高德地图 逆地理编码 返回值解析类
 * <AUTHOR>
 * @Date 2021/6/11 9:30
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Pois implements Serializable {

    private static final long serialVersionUID = -4821521327843666248L;

    private String id;

    private String type;

    private String location;

}