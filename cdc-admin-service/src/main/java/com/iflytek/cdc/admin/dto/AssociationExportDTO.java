package com.iflytek.cdc.admin.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "AssociationExportDTO", description = "关联导出DTO")
public class AssociationExportDTO {
    /**
     * 关联类型
     */
    @ApiModelProperty(value = "关联类型列表",allowableValues = "DISEASE_TO_DIAGNOSIS-疾病病种词库关联疾病诊断词库,DIAGNOSIS_TO_DISEASE-疾病诊断词库关联疾病病种词库,DISEASE_TO_INFECTIOUS-疾病病种词库关联传染病主数据,INFECTIOUS_TO_DISEASE-传染病主数据关联疾病病种词库")
    private List<String> associationTypes;
}
