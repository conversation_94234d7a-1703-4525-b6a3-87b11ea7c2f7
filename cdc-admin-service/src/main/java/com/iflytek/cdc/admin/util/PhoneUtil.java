package com.iflytek.cdc.admin.util;

import com.google.i18n.phonenumbers.PhoneNumberToCarrierMapper;
import com.google.i18n.phonenumbers.Phonenumber.PhoneNumber;
import com.google.i18n.phonenumbers.geocoding.PhoneNumberOfflineGeocoder;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @ClassName PhoneUtil
 * @Description TODO 患者手机号解析归属地 工具类
 * <AUTHOR>
 * @Date 2021/6/15 17:00
 * @Version 1.0
 */
@Slf4j
public class PhoneUtil {

    private static final String HONG_KONG_SPECIAL_ADMINISTRATIVE_REGION = "香港特别行政区";
    private static final String MACAO_SPECIAL_ADMINISTRATIVE_REGION = "澳门特别行政区";
    private static final String TAIWAN = "台湾";


    /**
     * 标准 手机号码 长度
     */
    public static final int STANDARD_PHONE_NUMBER_LENGTH = 11;

    /**
     * 中国大陆区区号
     */
    private final static int COUNTRY_CODE = 86;

    /**
     * 提供与电话号码相关的运营商信息
     */
    private static PhoneNumberToCarrierMapper carrierMapper = PhoneNumberToCarrierMapper.getInstance();

    /**
     * 提供与电话号码有关的地理信息
     */
    private static PhoneNumberOfflineGeocoder geocoder = PhoneNumberOfflineGeocoder.getInstance();


    /**
     * 功能：根据手机号 判断手机运营商
     *
     * @param phoneNumber 手机号码
     * @return 如：广东省广州市移动
     */
    private static String getCarrier(String phoneNumber) {

        long phone = Long.parseLong(phoneNumber);

        PhoneNumber pn = new PhoneNumber();
        pn.setCountryCode(COUNTRY_CODE);
        pn.setNationalNumber(phone);
        // 返回结果只有英文，自己转成成中文
        String carrierEn = carrierMapper.getNameForNumber(pn, Locale.ENGLISH);
        String carrierZh = "";
        switch (carrierEn) {
            case "China Mobile":
                carrierZh += "移动";
                break;
            case "China Unicom":
                carrierZh += "联通";
                break;
            case "China Telecom":
                carrierZh += "电信";
                break;
            default:
                break;
        }
        return carrierZh;
    }

    /**
     * 功能：根据手机号 获取手机归属地
     *
     * @param phoneNumber 手机号码
     * @return 如：广东省广州市
     */
    private static String getGeo(String phoneNumber) {
        long phone = 0;
        try {
            phone = Long.parseLong(phoneNumber);
        } catch (NumberFormatException e) {
            log.error("异常： {},原因：{}", e, "手机号存在非常字符");
        }

        PhoneNumber pn = new PhoneNumber();
        pn.setCountryCode(COUNTRY_CODE);
        pn.setNationalNumber(phone);
        return geocoder.getDescriptionForNumber(pn, Locale.CHINESE);
    }

    /**
     * 通过 手机号 解析出 省市行政区
     *
     * @param phoneNumber 手机号码
     * @return 如：广东省广州市
     */
    public static String parseOutProvinceCityByTelephone(String phoneNumber) {
        try {
            if (phoneNumber.startsWith(String.valueOf(COUNTRY_CODE))) {
                phoneNumber = phoneNumber.substring(2);
            }
            if (phoneNumber.length() == STANDARD_PHONE_NUMBER_LENGTH) {
                phoneNumber = phoneNumber.replace("*", "0");
                return getGeo(phoneNumber);
            }
        } catch (Exception e) {
            log.error("异常： {},原因：{}", e, "手机号存在非常字符");
        }
        return "";
    }

    public static List<Map<String, String>> getAddressInfo(String address) {
        //1级 省 自治区 2级 市 自治州 地区 3级：区县市旗(镇？)
        Map<String, String> resultMap = new HashMap<>(4);

        String province = null, city = null, area = null, town = null, village = null;
        List<Map<String, String>> table = new ArrayList<Map<String, String>>();
        Map<String, String> row = null;

        if (address.startsWith(HONG_KONG_SPECIAL_ADMINISTRATIVE_REGION)) {
            resultMap.put("province", "香港");
            table.add(resultMap);
            return table;
        } else if (address.contains(MACAO_SPECIAL_ADMINISTRATIVE_REGION)) {
            resultMap.put("province", "澳门");
            table.add(resultMap);
            return table;
        } else if (address.contains(TAIWAN)) {
            resultMap.put("province", "台湾");
            table.add(resultMap);
            return table;
        } else {
            //普通地址
            String regex = "(?<province>[^省]+省|.+自治区|.+市)(?<city>[^自治州]+自治州|[^市]+市|[^盟]+盟|[^地区]+地区|.+区划)(?<area>[^市]+市|[^县]+县|[^旗]+旗|.+区)?(?<town>[^区]+区|.+镇|.+乡|.+街道|.+服务中心)?(?<village>.*)";
            Matcher m = Pattern.compile(regex).matcher(address);
            while (m.find()) {
                row = new LinkedHashMap<String, String>();
                province = m.group("province");
                row.put("province", province == null ? "" : province.trim());
                city = m.group("city");
                row.put("city", city == null ? "" : city.trim());
                area = m.group("area");
                row.put("area", area == null ? "" : area.trim());
                town = m.group("town");
                row.put("town", town == null ? "" : town.trim());
                village = m.group("village");
                row.put("village", village == null ? "" : village.trim());
                table.add(row);
            }
            return table;
        }
    }


    /**
     * 测试样例
     *
     * @param args
     */
    private static void main(String[] args) {
        Long s = System.currentTimeMillis();
        String geo = getGeo("18855052878");
        String carrier = getCarrier("13013120000");
        String parseOutProvinceCityByTelephone = parseOutProvinceCityByTelephone("1505524****");

        // 安徽省黄山市
        System.out.println(geo);

        // 联通
        System.out.println(carrier);

        // 安徽省六安市
        System.out.println(parseOutProvinceCityByTelephone);

        // 50:ms
        System.out.println(System.currentTimeMillis() - s + ":ms");
    }

}
