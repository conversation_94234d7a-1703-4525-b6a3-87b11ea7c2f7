package com.iflytek.cdc.admin.job;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.iflytek.cdc.admin.service.AdministrativeRegionService;
import com.iflytek.cdc.admin.util.CronParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;


@Slf4j
@Component
@EnableScheduling
public class StreetInfoAutoRefreshJob {

    private static final String DEFAULT_CRON = "0 5/30 * * * ?";

    @Resource
    AdministrativeRegionService administrativeRegionService;

    @Value("${job.schedule.cron.streetInfo:" + DEFAULT_CRON + "}")
    private String cronExpression;

    @Scheduled(cron = "${job.schedule.cron.streetInfo:" + DEFAULT_CRON + "}")
    public void autoSyncResult() {

        if(Objects.equals(cronExpression, Scheduled.CRON_DISABLED)) {
            log.info("街道自动更新任务Disabled. ");
            return;
        }

        int timeRange = (int) (CronParser.getTimeBetweenCron(cronExpression, DateUnit.MINUTE) + 10);

        // 前10分钟, 后5分钟
        DateTime startTime = DateUtil.offsetMinute(new Date(), -timeRange);
        DateTime endTime = DateUtil.offsetMinute(new Date(), 5);

        log.info("开始定时更新街道信息数据: {} - {}", startTime, endTime);
        administrativeRegionService.syncResult(startTime, endTime);
        log.info("结束定时更新街道信息数据: {} - {}", startTime, endTime);

    }
}
