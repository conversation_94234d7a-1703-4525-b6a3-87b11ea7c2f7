package com.iflytek.cdc.admin.util;

import cn.hutool.core.util.ObjectUtil;
import org.apache.commons.lang.StringUtils;

import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.Locale;
import java.util.regex.Pattern;

/**
 * @description: 校验
 * @author: shenghuang
 * @create: 2022-10-28 14:48
 **/
public class VerifyUtil {

    public static boolean verifyPhone(String phone) {
        if (StringUtils.isEmpty(phone) || phone.length() != 11) {
            return false;
        }
        Pattern pattern = Pattern.compile("^[1]([3-9])[0-9]{9}$");
        return pattern.matcher(phone).matches();
    }

    /**
     * 判断对象中的字段是否都为空
     *
     * @param obj 待校验的对象
     * @return ture->是， false否
     */
    public static boolean isAllFieldEmpty(Object obj) {
        Class<?> aClass = obj.getClass();
        Field[] fs = aClass.getDeclaredFields();
        boolean flag = true;
        for (Field f : fs) {
            f.setAccessible(true);
            Object val = null;
            try {
                val = f.get(obj);
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
            if (ObjectUtil.isNotEmpty(val)) {
                flag = false;
                break;
            }
        }
        return flag;
    }

    public static Object getFieldValue(Object obj, String name) {
        try {
            Class<?> aClass = obj.getClass();
            Field[] fs = aClass.getDeclaredFields();
            for (Field f : fs) {
                if (!f.getName().equals(name)) {
                    continue;
                }
                f.setAccessible(true);
                return f.get(obj);
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static Object getEnumValueForKey(String value, String enumName) {
        try {
                String[] split = enumName.split(",");
                return Arrays.stream(split).filter(t->t.split("\\|")[1].equals(value)).findFirst().map(m->m.split("\\|")[0]).orElse("");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static Object getEnumValueForValue(Object obj, String name, String enumName) {
        try {
            Class<?> aClass = obj.getClass();
            Field[] fs = aClass.getDeclaredFields();
            for (Field f : fs) {
                if (!f.getName().equals(name)) {
                    continue;
                }
                f.setAccessible(true);
                Object value = f.get(obj);
                String[] split = enumName.split(",");
                return Arrays.stream(split).filter(t->t.split("\\|")[0].equals(value)).findFirst().map(m->m.split("\\|")[1]).orElse("");
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static void setFieldValue(Object obj, String name, Object value) {
        try {
            Class<?> aClass = obj.getClass();
            Field[] fs = aClass.getDeclaredFields();
            for (Field f : fs) {
                if (!f.getName().equals(name)) {
                    continue;
                }
                f.setAccessible(true);
                if("java.lang.Integer".equals(f.getType().getTypeName())){
                    f.set(obj,ObjectUtil.isNotEmpty(value) ? Integer.valueOf(String.valueOf(value)) : null);
                }else{
                    f.set(obj,value);
                }
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
    }

    public static String formatDate(String str){
        SimpleDateFormat sdf1 = new SimpleDateFormat("EEE MMM dd HH:mm:ss z yyyy", Locale.ENGLISH);
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date parse = null;
        try {
            parse = sdf1.parse(str);
            return sdf2.format(parse);
        } catch (Exception e) {
        }
        return str;
    }

    public static String formatDateByFormat(String str,String format){
        SimpleDateFormat sdf1 = new SimpleDateFormat("EEE MMM dd HH:mm:ss z yyyy", Locale.ENGLISH);
        SimpleDateFormat sdf2 = new SimpleDateFormat(format);
        Date parse = null;
        try {
            parse = sdf1.parse(str);
            return sdf2.format(parse);
        } catch (Exception e) {
        }
        return str;
    }


}
