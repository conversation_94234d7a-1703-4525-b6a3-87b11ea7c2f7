package com.iflytek.cdc.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.admin.dto.*;
import com.iflytek.cdc.admin.entity.MedicalWarn;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface MedicalWarnMapper extends BaseMapper<MedicalWarn> {

    List<MedicalWarnPageListResponseDto> pageList(@Param("param") MedicalWarnPageListDto medicalWarnPageListDto);

    List<MedicalWarnPageListResponseDto> subPageList(@Param("param") MedicalWarnPageListDto medicalWarnPageListDto);


    @Update("update tb_cdcmr_medical_warn set rule_status = #{status}, update_datetime = now() where id = #{id}")
    void medicalWarnModifyStatus(@Param("id") String id, @Param("status") String status);

    void modify(MedicalWarnRuleSaveOrUpdateDto dto);

    void updateCodeName(@Param("updateCode") String code, @Param("updateCodeName") String codeName, @Param("updateUser") String updateUser);

    void insertMedicalWarns(@Param("medicalWarns") List<MedicalWarn> medicalWarns);

    void editMedicalWarn( MedicalWarnEditDto medicalWarnEditDto);


    void updateInfecInfo(UpdateInfecDTO ud);

    List<MedicalWarn> queryInfecInfo(SearchInfecInfoDTO searchInfecInfoDTO);

    List<WarnRuleExportDataDto> getAllChooseButtonData(@Param("param") MedicalWarnRuleExportDto dto, @Param("areaCode") String areaCode);

    List<WarnRuleExportDataDto> queryChooseExportData( @Param("areaCode") String areaCode);

    List<MedicalWarn> selectAll();

    MedicalWarn selectByPrimaryKey(String id);
}
