package com.iflytek.cdc.admin.dto.amap.geocode;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName AddressComponent
 * @Description 高德地图 逆地理编码 返回值解析类
 * <AUTHOR>
 * @Date 2021/6/11 9:30
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AddressComponent implements Serializable {

    private static final long serialVersionUID = -5322760821473482810L;

    /**
     * 国家名
     */
    private String country;

    /**
     * 省份名
     */
    private String province;

    /**
     * 城市名
     */
    private Object city;

    /**
     * 城市编码
     */
    private String citycode;

    /**
     * 区县名
     */
    private String district;

    /**
     * 区县编码
     */
    private String adcode;

    /**
     * 街道/乡 名
     */
    private Object township;

    /**
     * 街道/乡 编码
     */
    private Object towncode;


    public String getCity() {
        if (city instanceof String) {
            return (String) city;
        }
        return "";
    }

    public String getTownship() {
        if (township instanceof String) {
            return (String) township;
        }
        return "";
    }

    public String getTowncode() {
        if (towncode instanceof String) {
            return (String) towncode;
        }
        return "";
    }
}