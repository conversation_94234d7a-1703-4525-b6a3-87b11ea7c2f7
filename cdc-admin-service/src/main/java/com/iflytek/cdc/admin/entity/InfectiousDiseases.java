package com.iflytek.cdc.admin.entity;

import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName InfectiousDiseases
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/7/29 10:45
 * @Version 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InfectiousDiseases {
    /**
     * 主键
     */
    @ApiModelProperty(value = "id")
    private String id;

    /**
     * 传染病病种编码
     */
    @ApiModelProperty(value = "传染病病种编码")
    private String diseasesCode;

    /**
     * 传染病病种名称
     */
    @ApiModelProperty(value = "传染病病种名称")
    private String diseasesName;

    /**
     * 传染病病种类型
     */
    @ApiModelProperty(value = "传染病病种类型")
    private String diseasesType;

    /**
     * 传染病病种类型名称
     */
    @ApiModelProperty(value = "传染病病种类型名称")
    private String diseasesTypeName;

    /**
     * 传染病病种分类
     */
    @ApiModelProperty(value = "传染病病种分类")
    private String diseasesClassify;

    /**
     * 传染病病种分类名称
     */
    @ApiModelProperty(value = "传染病病种分类名称")
    private String diseasesClassifyName;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private String isEnable;

    /**
     * 创建用户
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新用户
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 同步时间
     */
    private Date syncTime;

    /**
     * 是否删除
     */
    private String isDelete;

    /**
     * 传染病类型代码
     */
    private String infectClassCode;

    /**
     * 传染病类型名称
     */
    private String infectClassName;

    /**
     * 传播途径代码
     */
    private String transmissionTypeCode;

    /**
     * 传播途径名称
     */
    private String transmissionTypeName;
}