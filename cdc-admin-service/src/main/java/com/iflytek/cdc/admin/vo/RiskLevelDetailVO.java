package com.iflytek.cdc.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("信号等级详情试图")
public class RiskLevelDetailVO {
    @ApiModelProperty(value = "风险详情主键")
    private String id;

    @ApiModelProperty(value = "风险等级id")
    private String riskLevelId;

    @ApiModelProperty(value = "风险分级")
    private String riskLevelCode;

    @ApiModelProperty(value = "风险分级名称")
    private String riskLevelName;

    @ApiModelProperty(value = "风险等级描述")
    private String riskLevelDesc;

    @ApiModelProperty(value = "标记颜色")
    private String highlightColor;

    @ApiModelProperty(value = "核实时限")
    private Integer checkTimeLimit;

    @ApiModelProperty(value = "现场调查时限")
    private Integer investTimeLimit;

    @ApiModelProperty(value = "发起研判时限")
    private Integer judgeTimeLimit;

    @ApiModelProperty(value = "疾病id")
    private String diseaseId;

    @ApiModelProperty(value = "疾病编码")
    private String diseaseCode;

    @ApiModelProperty(value = "疾病名称")
    private String diseaseName;

    @ApiModelProperty(value = "创建人ID")
    private String creatorId;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新人ID")
    private String updaterId;

    @ApiModelProperty(value = "更新人")
    private String updater;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "删除标识")
    private String deleteFlag;
}
