package com.iflytek.cdc.admin.entity.mr;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 主数据操作日志表;
 * <AUTHOR> dingyuan
 * @date : 2024-8-22
 */
@ApiModel(value = "主数据操作日志表")
@Data
public class TbCdcmrMasterDateLogs implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id ;

    /**
     * 主数据类型：传染病/症候群/症状/病原
     */
    @ApiModelProperty(value = "主数据类型：传染病/症候群/症状/病原")
    private String type ;

    /**
     * 原始主数据基础信息
     */
    @ApiModelProperty(value = "原始主数据基础信息")
    private String originData ;

    /**
     * 修改后主数据信息
     */
    @ApiModelProperty(value = "修改后主数据信息")
    private String newData ;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime ;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String creator ;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    private String creatorId ;

}
