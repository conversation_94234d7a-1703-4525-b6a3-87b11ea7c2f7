package com.iflytek.cdc.admin.dto.addressstandardize;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @ClassName FuzzyAddressNormalizationDTO
 * @Description
 * <AUTHOR>
 * @Date 2021/7/14 10:12
 * @Version 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "模糊地址智能解析入参")
public class AddressFuzzyNormalizationDTO implements Serializable {
    private static final long serialVersionUID = 4079331750930233610L;

    /**
     * 当前调用接口的用户
     */
    @ApiModelProperty(value = "当前调用接口的用户ID")
    public String loginUserId;

    /**
     * 患者手机号 本人电话号码
     */
    @ApiModelProperty(value = "患者手机号")
    @Length(max = 11, message = "telephone-手机号 长度不能超过11")
    private String telephone;

    /**
     * 患者身份证号 居民身份证号
     */
    @ApiModelProperty(value = "患者身份证号")
    @Length(max = 18, message = "idCard-身份证号 长度不能超过18")
    private String idCard;

    /**
     * 医疗机构所在标准行政区名称省  省名称
     */
    @ApiModelProperty(value = "医疗机构所在标准行政区 省名称")
    @Length(max = 20, message = "provinceName-医疗机构所在标准行政区 省/自治区/直辖市 长度不能超过20")
    private String provinceName;

    /**
     * 医疗机构所在标准行政区名称 市  市名称
     */
    @ApiModelProperty(value = "医疗机构所在标准行政区 市名称")
    @Length(max = 20, message = "cityName-医疗机构所在标准行政区 市 长度不能超过20")
    private String cityName;

    /**
     * 医疗机构所在标准行政区名称 区/县 区/县编码
     */
    @ApiModelProperty(value = "医疗机构所在标准行政区 区/县编码")
    @Length(max = 20, message = "districtCode-医疗机构所在标准行政区 区/县编码 长度不能超过20")
    private String districtCode;

    /**
     * 模糊地址 现住址-详细地址
     */
    @ApiModelProperty(value = "模糊地址")
    @Length(max = 200, message = "addressDetail-模糊地址 长度不能超过200")
    @NotBlank
    private String addressDetail;

    @ApiModelProperty(value = "POI类型", example = "科教文化服务|医疗保健服务")
    private String types;

    @ApiModelProperty(value = "false-存在不刷新；true-不管是否存在，强制刷新")
    private Boolean refresh;
}
