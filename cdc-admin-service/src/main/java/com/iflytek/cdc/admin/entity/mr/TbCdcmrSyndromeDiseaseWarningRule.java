package com.iflytek.cdc.admin.entity.mr;

import java.io.Serializable;

import com.iflytek.cdc.admin.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@ApiModel("症候群预警规则表")
@Data
public class TbCdcmrSyndromeDiseaseWarningRule extends BaseEntity implements Serializable{

    public static final String TABLE_NAME = "tb_cdcmr_syndrome_disease_warning_rule";

    private static final long serialVersionUID = 1L;

    /**
     * 疾病维表id
     */
    @ApiModelProperty(value = "疾病维表id")
    private String diseaseInfoId ;

    /**
     * 风险等级
     */
    @ApiModelProperty(value = "风险等级")
    private String riskLevel ;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String ruleName ;

    /**
     * 预警方法
     */
    @ApiModelProperty(value = "预警方法")
    private String warningMethod ;

    /**
     * 预警周期 REAL_TIME-实时探测
     */
    @ApiModelProperty(value = "预警周期 REAL_TIME-实时探测")
    private String warningPeriod ;

    /**
     * 周期预警配置的时间
     */
    @ApiModelProperty(value = "周期预警配置的时间")
    private Integer warningPeriodTime ;

    /**
     * 指标 发病病例 MEDICAL_CASE 死亡病例 DEATH_CASE
     */
    @ApiModelProperty(value = "指标 发病病例 MEDICAL_CASE 死亡病例 DEATH_CASE")
    private String warningIndicator ;

    /**
     * 病历范围  json: list ["1","2","3"]
     */
    @ApiModelProperty(value = "病例范围")
    private String caseScope ;

    /**
     * 人群范围选项 ALL-全部人群
     */
    @ApiModelProperty(value = "人群范围选项 ALL-全部人群")
    private String populationScope ;

    /**
     * 人群范围条件
     */
    @ApiModelProperty(value = "人群范围条件")
    private String populationScopeParam ;

    /**
     * 空间数量
     */
    @ApiModelProperty(value = "空间数量")
    private Integer areaCount ;

    /**
     * 空间维度层级
     */
    @ApiModelProperty(value = "空间维度层级")
    private Integer areaLevel ;

    /**
     * 算法参数 大Json对象
     */
    @ApiModelProperty(value = "算法参数 大Json对象")
    private String modelParam ;

    /**
     * 预警阈值
     */
    @ApiModelProperty(value = "预警阈值")
    private Double warningThreshold ;

    /**
     * 事件等级
     */
    @ApiModelProperty(value = "事件等级")
    private String eventLevel ;

    /**
     * 事件报告类型
     */
    @ApiModelProperty(value = "事件报告类型")
    private String eventReportType ;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String notes ;

    /**
     * 状态（启用状态 1启用
     */
    @ApiModelProperty(value = "状态（启用状态 1启用")
    private Integer status ;

    /**
     * 地区范围：ALL-全部人群; SPEC-特定地区
     * */
    @ApiModelProperty(value = "地区范围：ALL-全部人群; SPEC-特定地区")
    private String areaScope;

    /**
     * 地区范围参数
     * */
    @ApiModelProperty(value = "地区范围参数")
    private String areaScopeParam;

    /**
     * 首例病例预警标识
     * */
    @ApiModelProperty(value = "首例病例预警标识")
    private String firstCaseWarning;

    /**
     * 首例病例预警层级
     * */
    @ApiModelProperty(value = "首例病例预警层级")
    private String firstCaseAreaLevel;

    /**
     * 关注标识
     */
    @ApiModelProperty(value = "关注标识")
    private String followStatus;

    /**
     * 地址维度类型
     */
    @ApiModelProperty(value = "地址维度类型")
    private String addressType;
}
