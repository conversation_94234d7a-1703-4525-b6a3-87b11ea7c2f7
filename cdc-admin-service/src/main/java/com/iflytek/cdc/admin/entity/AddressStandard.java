package com.iflytek.cdc.admin.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 标准地理信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-16
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tb_cdcmr_address_standard")
public class AddressStandard implements Serializable {


    private static final long serialVersionUID = -279459195182023530L;
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 标准地址所属街道/行政村名称
     */
    private String addressAreaName;

    /**
     * 标准地址所属街道/行政村全局唯一ID(标准化地址唯一主键)
     */
    private String addressAreaCode;

    /**
     * 标准地址经度
     */
    private String addressLongitude;

    /**
     * 标准地址纬度
     */
    private String addressLatitude;

    /**
     * 标准地址-省份
     */
    private String addressProvinceName;

    /**
     * 标准地址-省份编码
     */
    private String addressProvinceCode;

    /**
     * 标准地址-市
     */
    private String addressCityName;

    /**
     * 标准地址-市编码
     */
    private String addressCityCode;

    /**
     * 标准地址-区县
     */
    private String addressDistrictName;

    /**
     * 标准地址-区县编码
     */
    private String addressDistrictCode;

    /**
     * 标准地址-街道名
     */
    private String addressTownName;

    /**
     * 标准地址-街道编码
     */
    private String addressTownCode;

    /**
     * POI类型
     */
    private String poiType;

    /**
     * 坐标系类型种类
     */
    private String coordinatesType;

    /**
     * 地名
     */
    private String addressName;

    /**
     * 地址详细描述
     */
    private String addressDetailDesc;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private String createDatetime;

    /**
     * 更新人
     */
    private String updator;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateDatetime;
}
