package com.iflytek.cdc.admin.entity;

import lombok.Data;
import lombok.Getter;

import java.util.Arrays;

@Data
public class TbCdcmrCustomizedLogicField {
    private String id;
    private String warningType;
    private String name;
    private String desc;
    private String fieldType;//特殊类型
    private boolean groupFlag;
    private String dataSource;//数据来源
    private String medicalType;//病历类型
    private boolean columnFlag;//字段是否是数据库的列
    private String prefix;//查询前缀
    private String fieldName;//字段名
    private String splitter;//分割符，需要将String 类型的拆分
    private Integer sequence;//顺序
    private String constants;//常量值
    @Getter
    public enum FieldType{
        AGE("age", "年龄"),
        DATE("date", "时间"),
        TEXT("text", "普通文本"),
        DICT("dict", "字典"),
        CONSTANTS("constants", "常量"),
        EVENT_GENERATE_NODE("eventGenerateNode", "信号生成节点"),

        ;

        public static FieldType getFieldTypeByCode(String code){
            return Arrays.stream(FieldType.values()).filter(f -> f.getCode().equals(code)).findFirst().orElse(null);
        }

        private String code;
        private String desc;

        FieldType(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    @Getter
    public enum MedicalType{
        INPAT("inpat", "住院"),
        OUTPAT("outpat", "门诊")
        ;
        private String code;
        private String desc;

        MedicalType(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

}
