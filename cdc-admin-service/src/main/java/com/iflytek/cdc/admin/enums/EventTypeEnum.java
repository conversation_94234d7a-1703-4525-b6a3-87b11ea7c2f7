package com.iflytek.cdc.admin.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;

import java.util.Objects;

/**
 * 事件类型枚举
 * */
@Getter
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum EventTypeEnum {

    INFECTED("infected", "传染病","传染病初步诊断"),
    SYNDROME("syndrome", "症候群","症候群初步诊断"),
    FOOD_POISONING("foodPoisoning", "食物中毒","食物中毒事件初步诊断"),
    ACUTE_OCCUPATIONAL_POISONING("acuteOccupationalPoisoning", "急性职业中毒","急性职业中毒初步诊断"),
    OTHER_POISONING("otherPoisoning", "其他中毒","其他中毒初步诊断"),
    AIR_POLLUTION("airPollution", "空气污染","空气污染初步诊断"),
    WATER_POLLUTION("waterPollution", "水污染","水污染初步诊断"),
    SOIL_POLLUTION("soilPollution", "土壤污染","土壤污染初步诊断"),
    MASS_UNEXPLAINED_DISEASE("massUnexplainedDisease", "群体性不明原因疾病","群体性不明原因疾病初步诊断"),
    MASS_VACCINATION_REACTION("massVaccinationReaction", "群体性预防接种反应","群体性预防接种反应初步诊断"),
    MASS_PREVENTIVE_MEDICATION("massPreventiveMedication", "群体预防性服药反应","群体预防性服药反应初步诊断"),
    IATROGENIC_INFECTION("iatrogenicInfection", "医源性感染事件","医源性感染事件初步诊断"),
    RADIATION_ACCIDENT("radiationAccident", "放射事故","放射事故初步诊断"),
    OTHER_RADIATION_EVENT("otherRadiationEvent", "其他放射事件","其他放射事件初步诊断"),
    OTHER_PUBLIC_HEALTH_EVENT("otherPublicHealthEvent", "其他公共卫生事件","其他公共卫生事件初步诊断"),
    HEAT_STROKE("heatStroke", "高温中暑事件","高温中暑事件初步诊断"),

    ;

    private final String code;

    private final String desc;

    private final String value;

    EventTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
        this.value = null;
    }

    EventTypeEnum(String code, String desc, String value) {
        this.code = code;
        this.desc = desc;
        this.value = value;
    }

    public static String getValueByCode(String code){

        EventTypeEnum[] values = EventTypeEnum.values();
        for (EventTypeEnum value : values) {
            if (Objects.equals(code, value.getCode())) {
                return value.value;
            }
        }
        return null;
    }
    public static String getDescByCode(String code){
        EventTypeEnum[] values = EventTypeEnum.values();
        for (EventTypeEnum value : values) {
            if (Objects.equals(code, value.getCode())) {
                return value.desc;
            }
        }
        return null;
    }
}
