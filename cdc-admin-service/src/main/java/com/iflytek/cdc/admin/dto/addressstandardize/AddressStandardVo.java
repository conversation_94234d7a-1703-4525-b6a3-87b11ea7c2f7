package com.iflytek.cdc.admin.dto.addressstandardize;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Objects;

/**
 * @ClassName AddressDetailMapping
 * @Description 模糊地址映射类
 * <AUTHOR> //@TableName("cdc_system.amap_living_addr_detail_mapping")
 * @Date 2021/6/11 13:46
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AddressStandardVo implements Serializable {

    private static final long              serialVersionUID = 2219398343598580515L;
    public static final AddressStandardVo EMPTY_VALUE = AddressStandardVo.builder()
                                                                         .addressAreaCode("unknown")
                                                                         .addressAreaName("unknown")
                                                                         .addressProvinceCode("unknown")
                                                                         .addressProvinceName("unknown")
                                                                         .addressCityCode("unknown")
                                                                         .addressCityName("unknown")
                                                                         .addressDistrictCode("unknown")
                                                                         .addressDistrictName("unknown")
                                                                         .addressTownCode("unknown")
                                                                         .addressTownName("unknown")
                                                                         .addressLatitude("unknown")
                                                                         .addressLongitude("unknown")
                                                                         .build();


    /**
     * 标准化地址
     */
    @ApiModelProperty(value = "模糊地址标准化后的地址", position = 1)
    private String addressAreaName;

    /**
     * 标准化地址 编码
     */
    @ApiModelProperty(value = "标准化地址 地址编码", position = 2)
    private String addressAreaCode;

    /**
     * 标准化地址 省名
     */
    @ApiModelProperty(value = "标准化地址 所在省名", position = 3)
    private String addressProvinceName;

    /**
     * 标准化地址省 编码
     */
    @ApiModelProperty(value = "标准化地址 所在省编码", position = 4)
    private String addressProvinceCode;

    /**
     * 标准化地址 市名
     */
    @ApiModelProperty(value = "标准化地址 所在市名", position = 5)
    private String addressCityName;

    /**
     * 标准化地址市 编码
     */
    @ApiModelProperty(value = "标准化地址 所在市编码", position = 6)
    private String addressCityCode;

    /**
     * 标准化地址 县/区 名
     */
    @ApiModelProperty(value = "标准化地址 所在 县/区 名", position = 7)
    private String addressDistrictName;

    /**
     * 标准化地址 县/区 编码
     */
    @ApiModelProperty(value = "标准化地址 所在 县/区 编码", position = 8)
    private String addressDistrictCode;

    /**
     * 标准化地址 镇/乡/街道 名
     */
    @ApiModelProperty(value = "标准化地址 所在 镇/乡/街道 名", position = 9)
    private String addressTownName;

    /**
     * 标准化地址 镇/乡/街道 编码
     */
    @ApiModelProperty(value = "标准化地址 所在 镇/乡/街道 编码", position = 10)
    private String addressTownCode;


    /**
     * 标准化地址 经度
     */
    @ApiModelProperty(value = "标准化地址 经度", position = 11)
    private String addressLongitude;

    /**
     * 标准化地址 纬度
     */
    @ApiModelProperty(value = "标准化地址 纬度", position = 12)
    private String addressLatitude;

    public static AddressStandardVo emptyValue(){
        return EMPTY_VALUE;
    }

    public static boolean isEmptyValue(AddressStandardVo vo) {
        return vo == null || Objects.equals(vo.getAddressAreaCode(), "unknown") || Objects.equals(vo, EMPTY_VALUE);
    }
}