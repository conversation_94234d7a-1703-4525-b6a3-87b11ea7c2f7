package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.dto.RegionParam;
import com.iflytek.cdc.admin.dto.addressstandardize.RegionQueryDTO;
import com.iflytek.cdc.admin.entity.TbCdcmrRegion;
import com.iflytek.cdc.admin.vo.region.GroupRegionVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface TbCdcmrRegionMapper {

    int softDeleteById(@Param("loginUserId") String loginUserId, @Param("id") String id);

    int insert(TbCdcmrRegion record);

    int insertSelective(TbCdcmrRegion record);

    TbCdcmrRegion selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(TbCdcmrRegion record);

    int updateGroupRegion(TbCdcmrRegion record);

    void enableGroupRegion(@Param("loginUserId") String loginUserId, @Param("id") String id, @Param("isEnable") String isEnable);

    int batchUpsert(@Param("regionList") List<TbCdcmrRegion> regionList);

    int deleteByCodeLevel(@Param("code") String code, @Param("currLevel") int currLevel, @Param("toLevel") int toLevel);

    TbCdcmrRegion selectByCode(@Param("regionCode") String regionCode);

    TbCdcmrRegion selectByParentCodeAndName(@Param("parentCode") String parentCode, @Param("regionName") String regionName);

    List<TbCdcmrRegion> selectByCodeLevelUpdateTime(@Param("code") String code,
                                                    @Param("currLevel") Integer currLevel,
                                                    @Param("toLevel") Integer toLevel,
                                                    @Param("startTime") Date startTime,
                                                    @Param("endTime") Date endTime);

    int batchUpdateAmapInfos(@Param("regionList") List<TbCdcmrRegion> regionList);

    List<TbCdcmrRegion> selectByCodesLevel(@Param("codes") List<String> codes, @Param("level") Integer level);

    List<GroupRegionVO> regionSearch(@Param("param") RegionQueryDTO regionQueryDTO);

    List<TbCdcmrRegion> selectByParam(@Param("regionParam") RegionParam regionParam,
                                      @Param("regionLevel") Integer regionLevel);

    List<TbCdcmrRegion> selectRecurseByParam(@Param("regionParam") RegionParam regionParam,
                                             @Param("fromLevel") Integer fromLevel,
                                             @Param("toLevel") Integer toLevel);


    List<TbCdcmrRegion> selectVillageRegionsByNameInfo(@Param("nameInfos") List<List<String>> nameInfos);
}