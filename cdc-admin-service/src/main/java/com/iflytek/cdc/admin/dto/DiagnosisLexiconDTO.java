package com.iflytek.cdc.admin.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DiagnosisLexiconDTO {
    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private String id;
    /**
     * 诊断编码
     */
    @ApiModelProperty(value = "诊断编码")
    private String diagnosisCode;
    /**
     * 诊断名称
     */
    @ApiModelProperty(value = "诊断名称")
    private String diagnosisName;
    /**
     * 诊断来源
     */
    @ApiModelProperty(value = "诊断来源")
    private String diagnosisSource;
    /**
     * 业务分类
     */
    @ApiModelProperty(value = "业务分类")
    private String businessType;

    /**
     * 启用状态
     */
    @ApiModelProperty(value = "启用状态")
    private String isEnable;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
}
