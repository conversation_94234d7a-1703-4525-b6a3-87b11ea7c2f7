package com.iflytek.cdc.admin.util;

import java.util.Arrays;

/**
 * @ClassName Level5AdministrativeCode
 * @Description 五级行政编码枚举(安徽省内的 省, 市)
 * <AUTHOR>
 * @Date 2021/6/17 10:26
 * @Version 1.0
 */
public enum Level5AdministrativeCode {

    /**
     * 安徽省 行政编码
     */
    PROVINCE_340000("340000", "安徽省"),

    /**
     * 合肥市 行政编码
     */
    CITY_340100("340100", "合肥市"),

    /**
     * 芜湖市 行政编码
     */
    CITY_340200("340200", "芜湖市"),

    /**
     * 蚌埠市 行政编码
     */
    CITY_340300("340300", "蚌埠市"),

    /**
     * 淮南市 行政编码
     */
    CITY_340400("340400", "淮南市"),

    /**
     * 马鞍山市 行政编码
     */
    CITY_340500("340500", "马鞍山市"),

    /**
     * 淮北市 行政编码
     */
    CITY_340600("340600", "淮北市"),

    /**
     * 铜陵市 行政编码
     */
    CITY_340700("340700", "铜陵市"),

    /**
     * 安庆市 行政编码
     */
    CITY_340800("340800", "安庆市"),

    /**
     * 黄山市 行政编码
     */
    CITY_341000("341000", "黄山市"),

    /**
     * 滁州市 行政编码
     */
    CITY_341100("341100", "滁州市"),

    /**
     * 阜阳市 行政编码
     */
    CITY_341200("341200", "阜阳市"),

    /**
     * 宿州市 行政编码
     */
    CITY_341300("341300", "宿州市"),

    /**
     * 六安市 行政编码
     */
    CITY_341500("341500", "六安市"),

    /**
     * 亳州市 行政编码
     */
    CITY_341600("341600", "亳州市"),

    /**
     * 池州市 行政编码
     */
    CITY_341700("341700", "池州市"),

    /**
     * 宣城市 行政编码
     */
    CITY_341800("341800", "宣城市"),

    /**
     * unknown 行政编码
     */
    UNKNOWN_DEFAULT("unknown", "unknown"),

    /**
     * 未收录该行政区 行政编码
     */
    UNKNOWN("", "未收录该行政区");

    private static final String NOT_INCLUDED_CASE = "未收录该行政区";

    /**
     * 行政区编码
     */
    private String code;

    /**
     * 行政区
     */
    private String administrative;


    Level5AdministrativeCode(String code, String administrative) {
        this.code = code;
        this.administrative = administrative;
    }

    public String getCode() {
        return code;
    }

    public String getAdministrative() {
        if (NOT_INCLUDED_CASE.equals(administrative)) {
            return this.code;
        }
        return administrative;
    }

    /**
     * 根据 行政区名 获取 该行政区枚举类
     *
     * @param administrative 行政区名
     * @return
     */
    public static Level5AdministrativeCode getLevel5AdministrativeCodeByAdministrative(String administrative) {

        Level5AdministrativeCode[] level5AdministrativeCodes = Level5AdministrativeCode.values();

        Level5AdministrativeCode result = Arrays.asList(level5AdministrativeCodes).stream()

                .filter(level5AdministrativeCode -> level5AdministrativeCode.getAdministrative().equals(administrative))

                .findFirst().orElse(Level5AdministrativeCode.UNKNOWN);

        return result;

    }

    /**
     * 根据 行政区编码 获取 该行政区枚举类
     *
     * @param code 行政区编码
     * @return
     */
    public static Level5AdministrativeCode getLevel5AdministrativeCodeByCode(String code) {

        Level5AdministrativeCode[] level5AdministrativeCodes = Level5AdministrativeCode.values();

        Level5AdministrativeCode result = Arrays.asList(level5AdministrativeCodes).stream()

                .filter(level5AdministrativeCode -> level5AdministrativeCode.getCode().equals(code))

                .findFirst().orElse(Level5AdministrativeCode.UNKNOWN);

        return result;

    }

    public static void main(String[] args) {
        System.out.println(Level5AdministrativeCode.getLevel5AdministrativeCodeByAdministrative("常州市").getCode());
        System.out.println(Level5AdministrativeCode.getLevel5AdministrativeCodeByAdministrative("滁州市").getCode());
    }
}
