package com.iflytek.cdc.admin.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum MasterDataEnum {

    INFECTED("传染病主数据", "infected", "infectedMasterDataCategory"),

    SYNDROME("症候群主数据", "syndrome", "syndromeMasterDataCategory"),

    SYMPTOM("症状主数据", "symptom", "symptomMasterDataCategory"),

    PATHOGEN("病原主数据", "pathogen", "pathogenMasterDataCategory"),
    
    EMERGING("新发突发传染病主数据", "emerging", "emergingMasterDataCategory"),

    ENDEMIC("地方病主数据", "endemic", "endemicMasterDataCategory"),

    ;

    private final String name;

    private final String code;

    private final String beanName;

    private MasterDataEnum(String name, String code, String beanName){

        this.name = name;
        this.code = code;
        this.beanName = beanName;
    }

    public static String getBeanNameByCode(String code){

        MasterDataEnum[] values = MasterDataEnum.values();
        for (MasterDataEnum value : values) {
            if(Objects.equals(code, value.getCode())){
                return value.getBeanName();
            }
        }
        return null;
    }
}
