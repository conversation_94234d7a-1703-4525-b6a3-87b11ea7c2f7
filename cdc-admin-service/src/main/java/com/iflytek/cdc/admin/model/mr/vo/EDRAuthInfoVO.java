package com.iflytek.cdc.admin.model.mr.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class EDRAuthInfoVO {

    @ApiModelProperty("uuid")
    private String id;

    @ApiModelProperty("权限编码")
    private String authCode;

    @ApiModelProperty("权限名称")
    private String authClass;

    @ApiModelProperty(value = "省编码")
    private String provinceCode ;

    @ApiModelProperty(value = "市编码")
    private String cityCode ;

    @ApiModelProperty(value = "区编码")
    private String districtCode ;

    @ApiModelProperty("地区范围")
    private String areaScope;

    @ApiModelProperty(value = "权限用户")
    private String personInfo ;

    @ApiModelProperty("权限状态")
    private Integer status;

    @ApiModelProperty("最后操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date operatingTime;

}
