package com.iflytek.cdc.admin.entity.mr;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * ;
 * <AUTHOR> dingyuan
 * @date : 2025-3-4
 */
@ApiModel(value = "")
@Data
public class TbCdcmrDiseaseWarning implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id ;

    /**
     * 维表ID
     */
    @ApiModelProperty(value = "维表ID")
    private String diseaseInfoId ;

    /**
     * 疾病编码
     */
    @ApiModelProperty(value = "疾病编码")
    private String diseaseCode ;

    /**
     * 疾病名称
     */
    @ApiModelProperty(value = "疾病名称")
    private String diseaseName ;

    /**
     * 预警类型
     */
    @ApiModelProperty(value = "预警类型")
    private String warningType ;

    /**
     * 预警优先级
     */
    @ApiModelProperty(value = "预警优先级")
    private String warningPriority ;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updater ;

    /**
     * 更新人id
     */
    @ApiModelProperty(value = "更新人id")
    private String updaterId ;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime ;

    /**
     * 状态 1-开启; 0-关闭
     */
    @ApiModelProperty(value = "状态 1-开启; 0-关闭")
    private Integer status;

    /**
     * 删除标识
     */
    @ApiModelProperty(value = "删除标识")
    private String deleteFlag;

}
