package com.iflytek.cdc.admin.job;


import cn.hutool.core.collection.CollUtil;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.entity.TbCdcmrStdTextConfig;
import com.iflytek.cdc.admin.enums.StdConfigRuleTypeEnum;
import com.iflytek.cdc.admin.mapper.TbCdcmrStdTextConfigMapper;
import com.iflytek.cdc.admin.service.StdTextPreProcessService;
import com.iflytek.cdc.admin.util.RedisHelperUtil;
import com.iflytek.medicalboot.core.id.BatchUidService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Component
@EnableScheduling
public class StdWordAutoJob {

    @Resource
    public RedisTemplate<String, Object> redisTemplate;

    @Resource
    private TbCdcmrStdTextConfigMapper tbCdcmrStdTextConfigMapper;

    @Resource
    private StdTextPreProcessService stdTextPreProcessService;

    @Value("${cdc.std.invalidWord.autoCollect.number:20}")
    private int invalidWordAutoCollectNumber;

    @Resource
    BatchUidService batchUidService;

    //@Scheduled(fixedRate = 1440 * 60 * 1000)
    public void autoUpdateTextConfig() {
        log.info("自动收集停用词任务开始！");
        Set<String> keys = RedisHelperUtil.scan(redisTemplate, Constants.REDIS_KEY + Constants.TYPE_STD_WORD + "*");
        if (CollUtil.isEmpty(keys)) {
            log.info("自动收集停用词任务结束，不存在可以收集的停用词！");
            return;
        }
        List<TbCdcmrStdTextConfig> configList = new ArrayList<>();
        List<String> removeKeys = new ArrayList<>();
        keys.forEach(key -> {
            Object redisData = redisTemplate.opsForValue().get(key);
            if (!Objects.isNull(redisData) && Integer.parseInt(String.valueOf(redisData)) > invalidWordAutoCollectNumber) {
                String[] split = key.split(Constants.TYPE_STD_WORD);
                String itemValue = split[1];
                TbCdcmrStdTextConfig tbCdcmrStdTextConfig = new TbCdcmrStdTextConfig();
                tbCdcmrStdTextConfig.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_std_text_config")));
                tbCdcmrStdTextConfig.setStdType(Constants.STD_CATEGORY_COMPANY);
                tbCdcmrStdTextConfig.setItemType(Constants.STD_TYPE_COMMON);
                tbCdcmrStdTextConfig.setItemValue(itemValue);
                tbCdcmrStdTextConfig.setRuleType(StdConfigRuleTypeEnum.IGNORE.getCode());
                tbCdcmrStdTextConfig.setNotes(Constants.STD_TYPE_COMMON+"规则-"+StdConfigRuleTypeEnum.IGNORE.getName()+"-自动添加");
                tbCdcmrStdTextConfig.setCreateTime(new Date());

                TbCdcmrStdTextConfig config = new TbCdcmrStdTextConfig();
                config.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_std_text_config")));
                config.setStdType(Constants.STD_CATEGORY_ADDRESS);
                config.setItemType(Constants.STD_TYPE_COMMON);
                config.setItemValue(itemValue);
                config.setRuleType(StdConfigRuleTypeEnum.IGNORE.getCode());
                config.setNotes(Constants.STD_TYPE_COMMON+"规则-"+StdConfigRuleTypeEnum.IGNORE.getName()+"-自动添加");
                config.setCreateTime(new Date());

                configList.add(tbCdcmrStdTextConfig);
                configList.add(config);
                removeKeys.add(key);
            }
        });
        log.info("高德未能解析的keys: {}", keys);

        if (CollUtil.isNotEmpty(configList)) {
            //插入停用词到数据库
            tbCdcmrStdTextConfigMapper.mergeInfo(configList);
            //更新系统缓存
            stdTextPreProcessService.cacheConfig();
            //将停用词从redis中移除
            removeKeys.forEach(key -> redisTemplate.delete(key));
            log.info("自动收集停用词{}个，keys: {}", removeKeys.size(), removeKeys);
        }
        log.info("自动收集停用词任务结束！");
    }

}
