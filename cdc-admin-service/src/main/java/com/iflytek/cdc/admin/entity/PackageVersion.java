package com.iflytek.cdc.admin.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 客户端版本管理
 * <AUTHOR>
 * @date 17:22 2021/6/23
 **/
@Data
public class PackageVersion {
    /**
    * 主键
    */
    @ApiModelProperty(value="主键")
    private String id;

    /**
    * 区域编码
    */
    @ApiModelProperty(value="区域编码")
    private String areaCode;

    /**
    * 区域名称
    */
    @ApiModelProperty(value="区域名称")
    private String areaName;

    /**
    * 机构id
    */
    @ApiModelProperty(value="机构id")
    private String orgId;

    /**
    * 版本号
    */
    @ApiModelProperty(value="版本号")
    private String versionCode;

    /**
    * 版本类型 1-正式版本 2-测试/灰度版本
    */
    @ApiModelProperty(value="版本类型 1-正式版本 2-测试/灰度版本")
    private String versionType;

    /**
    * 版本子类型 1-增量版本  2-全量版本 3-安装包
    */
    @ApiModelProperty(value="版本子类型 1-增量版本  2-全量版本 3-安装包")
    private String versionSubType;

    /**
    * 描述文件地址
    */
    @ApiModelProperty(value="描述文件地址")
    private String descriptionFileUrl;

    /**
    * 资源包地址
    */
    @ApiModelProperty(value="资源包地址")
    private String resourceFileUrl;

    /**
    * 是否为最新版本
    */
    @ApiModelProperty(value="是否为最新版本")
    private String isNewVersion;

    /**
    * 创建人
    */
    @ApiModelProperty(value="创建人")
    private String creator;

    /**
    * 创建时间
    */
    @ApiModelProperty(value="创建时间")
    private Date createTime;

    /**
    * 更新人
    */
    @ApiModelProperty(value="更新人")
    private String updator;

    /**
    * 更新时间
    */
    @ApiModelProperty(value="更新时间")
    private Date updateTime;
}