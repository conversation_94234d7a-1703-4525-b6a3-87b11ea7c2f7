package com.iflytek.cdc.admin.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
    * uap机构信息表
 * <AUTHOR>
 */
@Data
public class UapOrganization {
    /**
    * 机构信息id
    */
    @ApiModelProperty(value="机构信息id")
    private String id;

    /**
    * 机构名称
    */
    @ApiModelProperty(value="机构名称")
    private String orgName;

    /**
    * 机构编码
    */
    @ApiModelProperty(value="机构编码")
    private String orgCode;

    /**
    * 所属省份
    */
    @ApiModelProperty(value="所属省份")
    private String provinceName;

    /**
    * 所属省份代码
    */
    @ApiModelProperty(value="所属省份代码")
    private String provinceCode;

    /**
    * 所属城市
    */
    @ApiModelProperty(value="所属城市")
    private String cityName;

    /**
    * 所属城市代码
    */
    @ApiModelProperty(value="所属城市代码")
    private String cityCode;

    /**
    * 所属区县
    */
    @ApiModelProperty(value="所属区县")
    private String districtName;

    /**
    * 所属区县代码
    */
    @ApiModelProperty(value="所属区县代码")
    private String districtCode;

    /**
    * 机构简称
    */
    @ApiModelProperty(value="机构简称")
    private String orgShortName;

    /**
    * 机构类型
    */
    @ApiModelProperty(value="机构类型")
    private String orgTypeName;

    /**
    * 机构类型代码
    */
    @ApiModelProperty(value="机构类型代码")
    private String orgTypeCode;

    /**
    * 上级机构id
    */
    @ApiModelProperty(value="上级机构id")
    private String higherOrgId;

    /**
    * 上级机构名称
    */
    @ApiModelProperty(value="上级机构名称")
    private String higherOrgName;

    /**
    * 机构等级
    */
    @ApiModelProperty(value="机构等级")
    private Integer orgLevel;

    /**
    * 机构状态{0停用 1启用}
    */
    @ApiModelProperty(value="机构状态{0停用 1启用}")
    private Integer orgStatus;

    /**
    * 排序
    */
    @ApiModelProperty(value="排序")
    private Integer orgSort;

    /**
    * 备注
    */
    @ApiModelProperty(value="备注")
    private String remark;

    /**
    * 创建时间
    */
    @ApiModelProperty(value="创建时间")
    private Date createTime;

    /**
    * 更新时间
    */
    @ApiModelProperty(value="更新时间")
    private Date updateTime;

    /**
    * 上级机构编码
    */
    @ApiModelProperty(value="上级机构编码")
    private String higherOrgCode;

    /**
    * 街道/镇
    */
    @ApiModelProperty(value="街道/镇")
    private String streetName;

    /**
    * 街道/镇编码
    */
    @ApiModelProperty(value="街道/镇编码")
    private String streetCode;

    /**
    * 所属卫生院编码
    */
    @ApiModelProperty(value="所属卫生院编码")
    private String townshipHospitalSourceKey;

    /**
    * 所属卫生院名称
    */
    @ApiModelProperty(value="所属卫生院名称")
    private String townshipHospitalName;

    /**
    * 经度
    */
    @ApiModelProperty(value="经度")
    private BigDecimal longitude;

    /**
    * 纬度
    */
    @ApiModelProperty(value="纬度")
    private BigDecimal latitude;

    /**
    * geohash
    */
    @ApiModelProperty(value="geohash")
    private String geohash;

    /**
    * 是否删除
    */
    @ApiModelProperty(value="是否删除")
    private String isDeleted;

    /**
     * 是否匹配
     */
    @ApiModelProperty(value = "是否匹配")
    private String isMatch;

    /**
     * 原始机构类型
     */
    @ApiModelProperty(value = "原始机构类型")
    private String sourceOrgTypeCode;
}