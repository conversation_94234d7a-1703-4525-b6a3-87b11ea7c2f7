package com.iflytek.cdc.admin.enums;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum WarningCorrelationEnum {
    JCYL("基层医疗", "2"),
    DJYY("等级医院", "1"),
    XXDW("学校单位", "3"),
    JDQU("街道区域", "4");

    private String name;
    private String code;


    private WarningCorrelationEnum(String name, String code) {
        this.code = code;
        this.name = name;
    }

    public static List<Map<String, String>> getEnumContent() {
        List<Map<String, String>> enumContentList = new ArrayList<>();
        for (WarningCorrelationEnum enumValue : WarningCorrelationEnum.values()) {
            Map<String, String> enumContentMap = new HashMap<>();
            enumContentMap.put("name", enumValue.getName());
            enumContentMap.put("code", enumValue.getCode());
            enumContentList.add(enumContentMap);
        }
        return enumContentList;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
