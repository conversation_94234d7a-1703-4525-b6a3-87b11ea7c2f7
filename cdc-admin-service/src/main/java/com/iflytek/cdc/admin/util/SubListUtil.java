package com.iflytek.cdc.admin.util;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;

import java.util.ArrayList;
import java.util.List;

/**
 * 集合分割工具类
 * <AUTHOR>
 * @date 2021/8/13 11:21
 **/
public class SubListUtil {

    public static  <T>  List<List<T>> subLitList(List<T> sourceList,int size){
        List<List<T>> results=new ArrayList<>();
        if(CollUtil.isNotEmpty(sourceList)){
            results= Lists.partition(sourceList,size);
        }
        return results;
    }

}
