package com.iflytek.cdc.admin.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
    * 厂商机构映射表
    */
@Data
public class OrgMapping  implements Serializable {
    /**
    * 表id
    */
    @ApiModelProperty(value="表id")
    private String id;

    /**
    * 行政区划省代码
    */
    @ApiModelProperty(value="行政区划省代码")
    private String areaProvinceCode;

    /**
    * 行政区划省名称
    */
    @ApiModelProperty(value="行政区划省名称")
    private String areaProvinceName;

    /**
    * 行政区划市代码
    */
    @ApiModelProperty(value="行政区划市代码")
    private String areaCityCode;

    /**
    * 行政区划市名称
    */
    @ApiModelProperty(value="行政区划市名称")
    private String areaCityName;

    /**
    * 行政区划区代码
    */
    @ApiModelProperty(value="行政区划区代码")
    private String areaCountyCode;

    /**
    * 行政区划区名称
    */
    @ApiModelProperty(value="行政区划区名称")
    private String areaCountyName;

    /**
    * 来源代码
    */
    @ApiModelProperty(value="来源代码")
    private String sourceCode;

    /**
    * 来源名称
    */
    @ApiModelProperty(value="来源名称")
    private String sourceName;

    /**
    * 来源机构代码
    */
    @ApiModelProperty(value="来源机构代码")
    private String sourceOrgCode;

    /**
    * 来源机构名称
    */
    @ApiModelProperty(value="来源机构名称")
    private String sourceOrgName;

    /**
    * 系统机构id
    */
    @ApiModelProperty(value="系统机构id")
    private String sysOrgId;

    /**
    * 系统机构名称
    */
    @ApiModelProperty(value="系统机构名称")
    private String sysOrgName;

    /**
    * 是否匹配
    */
    @ApiModelProperty(value="是否匹配")
    private String isMatch;

    /**
    * 是否删除
    */
    @ApiModelProperty(value="是否删除")
    private String isDelete;

    /**
    * 创建人
    */
    @ApiModelProperty(value="创建人")
    private String createUser;

    /**
    * 创建时间
    */
    @ApiModelProperty(value="创建时间")
    private Date createTime;

    /**
    * 修改者
    */
    @ApiModelProperty(value="修改者")
    private String updateUser;

    /**
    * 修改时间
    */
    @ApiModelProperty(value="修改时间")
    private Date updateTime;

    /**
     * uap机构数据表id
     */
    private String uapId;

    /**
     * his机构数据表id
     */
    private String hisId;
}