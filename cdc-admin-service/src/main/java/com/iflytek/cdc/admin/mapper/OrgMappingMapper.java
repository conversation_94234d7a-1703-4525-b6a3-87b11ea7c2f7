package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.dto.SearchHisOrgDTO;
import com.iflytek.cdc.admin.dto.UapOrgMatchDTO;
import com.iflytek.cdc.admin.dto.UpdateOrgDTO;
import com.iflytek.cdc.admin.entity.HisOrg;
import com.iflytek.cdc.admin.entity.OrgMapping;
import com.iflytek.cdc.admin.entity.UapOrganization;
import com.iflytek.cdc.admin.sdk.entity.HisInfoFilter;
import com.iflytek.cdc.admin.sdk.pojo.HisOrgInfo;
import com.iflytek.cdc.admin.sdk.pojo.UapOrgInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/6/30 11:12
 **/
@Mapper
public interface OrgMappingMapper {

    /**
     * 查询his机构列表
     * @param hisOrgDTO
     * @return
     **/
    List<HisOrg> queryHisOrgList(SearchHisOrgDTO hisOrgDTO);

    /**
     * 查询uap机构列表
     * @param orgDTO
     * @return
     **/
    List<UapOrganization>  queryUapOrgList(SearchHisOrgDTO  orgDTO);


    /**
     * 查询匹配的uap机构数据
     * @param id
     * @return
     **/
    List<UapOrganization>  queryMatchUapOrg(@Param("orgId") String  id);


    /**
     * 插入his的机构数据
     * @param record
     * @return
     **/
    int insertHisOrg(HisOrg record);

    /**
     *  通过his机构编码和区县编码和厂商来源id查询his机构数据
     * @param hisOrgCode
     * @param sourceId
     * @return
     **/
    List<HisOrg> findHisOrgByCode(@Param("hisOrgCode")String hisOrgCode,  @Param("sourceId")String sourceId);


    HisOrg  queryHisOrgInfoById(@Param("hisOrgId") String  hisOrgId);

    /**
     * 修改his机构数据
     * @param hisOrg
     * @return
     **/
    int updateHisOrg(HisOrg  hisOrg);

    /**
     * 删除his机构数据
     * @param hisOrgId
     * @return
     **/
    int deleteByOrgId(@Param("hisOrgId") String hisOrgId);

    /**
     * 机构映射数据表新增数据
     * @param orgMappingList
     * @return
     **/
    void insertOrgMapper(List<OrgMapping>  orgMappingList);

    /**
     * 查询uap机构和his机构可以匹配的数据
     * @param ud
     * @return
     **/
     List<OrgMapping>  queryOrgList(UapOrgMatchDTO ud);

    /**
     * 根据id查询机构映射数据
     * @param uapOrgId
     * @return
     **/
     List<OrgMapping>  queryOrgListById(@Param("uapOrgId") String  uapOrgId);


    /**
     * 根据id查询机构映射数据
     * @param hisOrgId
     * @return
     **/
     List<OrgMapping> queryHisOrgListById(@Param("hisOrgId") String hisOrgId);

    /**
     * 更改机构匹配状态
     * @param ud
     * @return
     **/
     int updateHisMatchStatus(UpdateOrgDTO  ud);

    /**
     * 更改uap机构匹配状态
     * @param ud
     * @return
     **/
     int updateUapMatchStatus(UpdateOrgDTO  ud);

    /**
     * 查询与his机构匹配的uap机构id
     * @param hisOrgCode
     * @return
     **/
     String  queryUapIdByHisCode(@Param("hisOrgCode") String  hisOrgCode,@Param("sourceId") String sourceId);

     /**
      * 删除机构映射表的数据
      * @param uapOrgId
      * @return
      **/
     void  deleteByUapOrgId(@Param("uapOrgId") String  uapOrgId);


     UapOrgInfo queryUapByHis(HisInfoFilter  filter);


     HisOrgInfo queryHisByUap(@Param("uapId") String  uapId);


    Integer deleteByCdcSysId(@Param("cdcSysOrgId") String cdcSysOrgId);

    /**
     * 删除机构表的数据
     * @param uapOrgId 机构Id
     */
    void deleteUapOrgById(@Param("uapOrgId") String uapOrgId);

    /**
     * 插入机构表数据
     * @param uapOrganization 机构数据
     */
    void insertUapOrg(UapOrganization uapOrganization);

    /**
     * 更新机构表数据
     * @param uapOrganization 机构数据
     */
    void updateUapOrg(UapOrganization uapOrganization);

    /**
     * 查询需要更新经纬度的机构列表
     * @return
     */
    List<UapOrganization>  queryNeedUpOrgList();

    /**
     * 执行卫生院归属
     */
    void healthCenter(@Param("orgId") String orgId);
    /**
     * 执行功能区域归属
     */
    void functionalArea(@Param("params") Map<String,String> params);

}
