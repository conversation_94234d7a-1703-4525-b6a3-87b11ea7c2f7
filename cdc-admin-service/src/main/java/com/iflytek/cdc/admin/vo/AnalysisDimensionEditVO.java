package com.iflytek.cdc.admin.vo;

import com.iflytek.cdc.admin.dto.AnalysisTargetDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 分析维度信息表-新增编辑对象
 */
@Data
@ApiModel(description = "分析维度信息表-新增编辑对象")
public class AnalysisDimensionEditVO {

    @ApiModelProperty(value = "主键，维度的唯一标识")
    private String id;

    @ApiModelProperty(value = "维度名称")
    private String dimensionName;

    @ApiModelProperty(value = "值域ID")
    private String dataAttrId;

    @ApiModelProperty(value = "维度定义")
    private String dimDefinition;

    @ApiModelProperty(value = "备注")
    private String notes;

    @ApiModelProperty("分析对象")
    private List<AnalysisTargetDto> analysisTargetList;
}
