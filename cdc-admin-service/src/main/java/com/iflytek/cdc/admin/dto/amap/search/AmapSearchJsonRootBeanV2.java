
package com.iflytek.cdc.admin.dto.amap.search;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName KieJsonRootBean
 * @Description 高德地图 搜索服务-关键字查询 返回值解析类
 * <AUTHOR>
 * @Date 2021/6/11 9:34
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AmapSearchJsonRootBeanV2 implements Serializable {

    private static final long serialVersionUID = -292967953993181827L;

    private List<PoisV2> pois;

}