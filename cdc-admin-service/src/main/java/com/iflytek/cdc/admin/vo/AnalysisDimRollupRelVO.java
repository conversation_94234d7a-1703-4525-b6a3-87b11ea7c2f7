package com.iflytek.cdc.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(description = "分析维度上卷关系信息")
public class AnalysisDimRollupRelVO {
    
    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "分析维度主键ID")
    private String analysisDimId;

    @ApiModelProperty(value = "上卷维度主键ID")
    private String rollupDimId;

    @ApiModelProperty(value = "上卷维度名称")
    private String rollupDimName;

    @ApiModelProperty(value = "序号")
    private Integer seqNum;

    @ApiModelProperty(value = "删除标识: 0-未删除，1-已删除")
    private String deleteFlag;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}