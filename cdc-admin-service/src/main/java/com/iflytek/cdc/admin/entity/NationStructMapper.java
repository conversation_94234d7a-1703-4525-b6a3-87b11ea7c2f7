package com.iflytek.cdc.admin.entity;

import com.iflytek.zhyl.uap.usercenter.entity.TUapNation;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

@Mapper
public interface NationStructMapper {

    NationStructMapper INSTANCE = Mappers.getMapper(NationStructMapper.class);

    @Mapping(target = "villageCode", ignore = true)
    @Mapping(target = "streetCode", ignore = true)
    @Mapping(target = "provinceCode", ignore = true)
    @Mapping(target = "name", ignore = true)
    @Mapping(target = "level", ignore = true)
    @Mapping(target = "districtCode", ignore = true)
    @Mapping(target = "cityCode", ignore = true)
    @Mapping(target = "childNations", ignore = true) // childNations 字段不从这里copy"
    UapNation uapToNation(TUapNation tUapNation);

    @Mappings({
            @Mapping(source = "id", target = "id", ignore = true), // id 字段不从这里copy
            @Mapping(source = "code", target = "regionCode"),
            @Mapping(source = "name", target = "regionName"),
            @Mapping(source = "level", target = "regionLevel"),
            @Mapping(source = "parentId", target = "parentRegionCode"),
            @Mapping(source = "province", target = "provinceName"),
            @Mapping(source = "city", target = "cityName"),
            @Mapping(source = "district", target = "districtName"),
            @Mapping(source = "street", target = "streetName"),
            @Mapping(source = "village", target = "villageName"),
            @Mapping(source = "id", target = "sourceId"),
            @Mapping(target = "addressDetail", ignore = true),
            @Mapping(target = "aliasName", ignore = true),
            @Mapping(target = "amapAreaCode", ignore = true),
            @Mapping(target = "amapAreaName", ignore = true),
            @Mapping(target = "createDatetime", ignore = true),
            @Mapping(target = "creator", ignore = true),
            @Mapping(target = "dataSource", ignore = true),
            @Mapping(target = "deleteFlag", ignore = true),
            @Mapping(target = "groupCode", ignore = true),
            @Mapping(target = "groupName", ignore = true),
            @Mapping(target = "isEnable", ignore = true),
            @Mapping(target = "latitude", ignore = true),
            @Mapping(target = "longitude", ignore = true),
            @Mapping(target = "memo", ignore = true),
            @Mapping(target = "updateDatetime", ignore = true),
            @Mapping(target = "updater", ignore = true),
            @Mapping(target = "urTypeCode", ignore = true),
            @Mapping(target = "urTypeName", ignore = true)
    })
    TbCdcmrRegion nationToRegion(UapNation nation);
}
