package com.iflytek.cdc.admin.enums;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.hutool.core.collection.ListUtil;
import lombok.Getter;

/**
 * 分析对象
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@Getter
public enum AnalysisTargetEnum {

    /**
     * 机构
     */
    ORGANIZATION("org", "机构", null),

    /**
     * 病历
     */
    MEDICAL("medical", "病历", ListUtil.of(AnalysisTargetSecondaryEnum.MEDICAL_OUTPATIENT,
            AnalysisTargetSecondaryEnum.MEDICAL_INPATIENT)),

    /**
     * 病例
     */
    PROCESS("process", "病例", ListUtil.of(AnalysisTargetSecondaryEnum.PROCESS_INFECTIOUS,
            AnalysisTargetSecondaryEnum.PROCESS_SYNDROME, AnalysisTargetSecondaryEnum.PROCESS_RESPIRATORY)),

    /**
     * 个人疾病档案
     */
    EDR("edr", "个人疾病档案", null),

    /**
     * 患者
     */
    PATIENT("patient", "患者", null),

    /**
     * 信号
     */
    SIGNAL("signal", "信号", ListUtil.of(AnalysisTargetSecondaryEnum.SIGNAL_INFECTIOUS,
            AnalysisTargetSecondaryEnum.SIGNAL_SYNDROME, AnalysisTargetSecondaryEnum.SIGNAL_KNOWN,
            AnalysisTargetSecondaryEnum.SIGNAL_UNKNOWN, AnalysisTargetSecondaryEnum.SIGNAL_PATHOGEN,
            AnalysisTargetSecondaryEnum.SIGNAL_CLUE)),

    /**
     * 突发公共卫生事件
     */
    EMERGENCY("emergency", "突发公共卫生事件", null);

    private String code;
    private String name;
    private List<AnalysisTargetSecondaryEnum> children;

    AnalysisTargetEnum(String code, String name, List<AnalysisTargetSecondaryEnum> children) {
        this.code = code;
        this.name = name;
        this.children = children;
    }

    public boolean isLeaf() {
        return children == null || children.isEmpty();
    }
}
