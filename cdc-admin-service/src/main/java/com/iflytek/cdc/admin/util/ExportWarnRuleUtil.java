package com.iflytek.cdc.admin.util;

import com.iflytek.cdc.admin.annotation.ExcelColumnName;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.PoisoningWarnRuleExportDataDto;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
@Slf4j
@Component
public class ExportWarnRuleUtil {
    public  static  <T> void  excelExport(List<T> exportContent, HttpServletResponse response, InputStream is, String... headValue ){

        //创建Excel薄
        XSSFWorkbook wb=null;
        OutputStream out = null;
        try {
            if(is !=null){
                wb =new XSSFWorkbook(is);
            }else{
                wb=new XSSFWorkbook();
            }
            //创建sheet页
            XSSFSheet sheet=wb.createSheet();
            //固定行高度
            sheet.setDefaultRowHeight((short) (2 * 256));
            //设置字体样式
            XSSFFont font=wb.createFont();
            font.setFontName("微软雅黑");
            font.setFontHeightInPoints((short) 16);
            //标题样式
            XSSFCellStyle titleStyle = wb.createCellStyle();
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            Font ztFont = wb.createFont();
            // 设置字体不是斜体字
            ztFont.setItalic(false);
            // 设置字体颜色
            ztFont.setColor(Font.COLOR_NORMAL);
            // 将字体大小设置为16px
            ztFont.setFontHeightInPoints((short) 16);
            // 将"宋体"字体应用到当前单元格上
            ztFont.setFontName("宋体");
            //加粗
            ztFont.setBold(true);
            titleStyle.setFont(ztFont);
            //默认获取表头信息
            List<Map<String,Object>> title=new ArrayList<>();
            List<String> fieldName=new ArrayList<>();
            //获取表头内容
            extracted(exportContent, title, fieldName);
            int rowNum = getRowNum(sheet, titleStyle, title, fieldName, headValue);
            //填充表格内容
            extracted(exportContent, sheet, fieldName, rowNum);
            //设置单元格长度根据表格内容扩充
            setColumnSize(sheet,title.size()+1,rowNum);
            out=response.getOutputStream();
            wb.write(out);
            out.flush();
        }catch (Exception e){
            log.error("导出excel异常：{}",e);
            throw new MedicalBusinessException("文件导出异常");
        }finally {
            try {
                wb.close();
                out.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private static  <T>  void extracted(List<T> exportContent, List<Map<String, Object>> title, List<String> fieldName) {
        if(!CollectionUtils.isEmpty(exportContent)){
            T t= (T) exportContent.get(0);
            //获取私有化属性
            Field[] fields=t.getClass().getDeclaredFields();
            for (Field  field:fields) {
                Map<String,Object>  map=new HashMap<>();
                boolean  annotationPresent= field.isAnnotationPresent(ExcelColumnName.class);
                if (annotationPresent){
                    map.put(field.getName(),field.getAnnotation(ExcelColumnName.class).name());
                    fieldName.add(field.getName());
                    title.add(map);
                }
            }
        }
    }
    /**
     * 获取表格合并标题内容
     * @return
     **/
    private static int getRowNum(XSSFSheet sheet, XSSFCellStyle titleStyle, List<Map<String, Object>> title,  List<String> fieldName,String[] headValue) {
        int rowNum= headValue.length !=0 && StringUtils.isNotBlank(headValue[0])?1:0;
        //如果需要填充表格头，则合并单元格，填充数据。
        if(headValue.length !=0&& StringUtils.isNotBlank(headValue[0])){
            String headCellValue= headValue[0];
            XSSFRow headRow= sheet.createRow(0);
            XSSFCell headCell=headRow.createCell(0);
            // 合并单元格CellRangeAddress构造参数依次表示起始行，截至行，起始列， 截至列
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, Math.max(title.size() - 1, 2)));
            headCell.setCellValue(headCellValue);
            headCell.setCellStyle(titleStyle);
        }
        XSSFRow   firstRow= sheet.createRow(rowNum);
        for(int i = 0; i< title.size(); i++){
            XSSFCell cell=firstRow.createCell(i);
            cell.setCellValue(title.get(i).get(fieldName.get(i)).toString());
        }
        return rowNum;
    }

    /**
     *  填充表格内容
     * @return
     **/
    private static <T> void extracted(List<T> exportContent, XSSFSheet sheet, List<String> fieldName, int rowNum) {
        try{
            for (int i = 0; i< exportContent.size(); i++){
                //创建行
                XSSFRow  row= sheet.createRow(i+1+ rowNum);
                //遍历表格内容
                for(int j = 0; j< fieldName.size(); j++){
                    //反射获取字段的值
                    XSSFCell cell=row.createCell(j);
                    StringBuffer  sb=new StringBuffer();
                    sb.append("get").append(fieldName.get(j).substring(0,1).toUpperCase()).append(fieldName.get(j).substring(1));
                    Method method= exportContent.get(i).getClass().getMethod(sb.toString());
                    Object  fieldValue=method.invoke(exportContent.get(i));
                    //填充内容
                    if (fieldValue !=null){
                        cell.setCellValue(fieldValue.toString());
                    }
                }
            }
        }catch (Exception e){
            log.error("获取表格内容异常",e);
            throw  new MedicalBusinessException("excel导出异常");
        }
    }

    public static void setColumnSize(XSSFSheet  sheet,int size,int  headRowNum){
        //遍历单元格
        for (int columnNum = 0; columnNum <size; columnNum++) {
            //获取当前单元格长度
            int columnWidth = sheet.getColumnWidth(columnNum) / 256;
            //遍历行
            for (int rowNum = 0; rowNum <= sheet.getLastRowNum(); rowNum++) {
                XSSFRow currentRow;
                //当前行未被使用过
                if (sheet.getRow(rowNum) == null) {
                    currentRow = sheet.createRow(rowNum);
                } else {
                    currentRow = sheet.getRow(rowNum);
                }
                if (currentRow.getCell(columnNum) != null) {
                    //回去当前单元格
                    XSSFCell currentCell = currentRow.getCell(columnNum);
                    //如果为字符类型
                    if (currentCell.getCellType() == CellType.STRING) {
                        //获取内容长度
                        int length = currentCell.getStringCellValue().getBytes().length;
                        //当有表头的合并单元格
                        if(headRowNum ==1){
                            if(columnNum ==0){
                                if (columnWidth < length && rowNum !=0) {
                                    columnWidth = length;
                                }
                            }else{
                                if (columnWidth < length) {
                                    columnWidth = length;
                                }
                            }
                        }else{
                            if (columnWidth < length) {
                                columnWidth = length;
                            }
                        }

                    }
                }
            }
            if(columnWidth>255){
                columnWidth=255;
            }
            sheet.setColumnWidth(columnNum, columnWidth * 256);
        }
    }
}
