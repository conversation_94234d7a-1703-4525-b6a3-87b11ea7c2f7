package com.iflytek.cdc.admin.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;

/**
    * 症候群信息标准维护表
 * <AUTHOR>
 */
@Data
public class SyndromeInfo {
    /**
    * 主键id
    */
    @ApiModelProperty(value="主键id")
    private String id;

    /**
    * 症候群编码
    */
    @ApiModelProperty(value="症候群编码")
    private String syndromeCode;

    /**
    * 症候群名称
    */
    @ApiModelProperty(value="症候群名称")
    private String syndromeName;

    /**
    * 症状规则
    */
    @ApiModelProperty(value="症状规则")
    @Max(value = 200,message = "症状规则长度不能超过200")
    private String symptomsRule;

    /**
    * 增强规则
    */
    @ApiModelProperty(value="增强规则")
    @Max(value = 200,message = "增强规则长度不能超过200")
    private String heightenRule;

    /**
    * 覆盖事件
    */
    @ApiModelProperty(value="覆盖事件")
    @Max(value = 200,message = "覆盖事件长度不能超过200")
    private String coverEvent;

    /**
    * 启用状态;0-不启用 1-启用
    */
    @ApiModelProperty(value="启用状态;0-不启用 1-启用")
    private String useStatus;

    /**
    * 备注
    */
    @ApiModelProperty(value="备注")
    @Max(value = 200,message = "备注长度不能超过200")
    private String remark;

    /**
    * 创建人
    */
    @ApiModelProperty(value="创建人")
    private String createUser;

    /**
    * 创建时间
    */
    @ApiModelProperty(value="创建时间")
    private Date createTime;

    /**
    * 修改人
    */
    @ApiModelProperty(value="修改人")
    private String updateUser;

    /**
    * 修改时间
    */
    @ApiModelProperty(value="修改时间")
    private Date updateTime;

    /**
    * 数据同步时间
    */
    @ApiModelProperty(value="数据同步时间")
    private Date dataSynchronTime;


    private String parentSyndromeCode;

    private String parentSyndromeName;

    private Integer incubation;

    private Integer hisWindow;

    private Integer forecastWindow;
}