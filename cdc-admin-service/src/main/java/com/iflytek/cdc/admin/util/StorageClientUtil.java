package com.iflytek.cdc.admin.util;

import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.storage.SwiftProperties;
import com.iflytek.storage.StorageClient;
import com.iflytek.storage.model.StorageObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.util.Map;

/**
 * 文件上传工具类
 * <AUTHOR>
 * @Date 2019/4/25 0025
 */
@Component
@Slf4j
public class StorageClientUtil {

    private final StorageClient storageClient;
    private final SwiftProperties swiftProperties;

    @Value("${storage.switch:swift}")
    private String storageSwitch;


    @Autowired(required = false)
    public StorageClientUtil(StorageClient storageClient, SwiftProperties swiftProperties) {
        this.storageClient = storageClient;
        this.swiftProperties = swiftProperties;
    }


    private Boolean checkStorageType() {
        return !org.apache.commons.lang.StringUtils.equals(storageSwitch, "minio");
    }


    /**
     * 存文件
     *
     * @param inputStream
     * @param objectName
     * @param headers
     * @return
     * @throws Exception
     */
    public String putObject(InputStream inputStream, String objectName, Map<String, String> headers) throws Exception {
        String imagePath = "";
        if (checkStorageType()) {
            StorageObject storageObject = storageClient.putObject(inputStream, swiftProperties.getContainer(), objectName, headers);
            if (null != storageObject) {
                log.info(storageObject.getUrl());
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(storageObject.getUrl())) {
                    //截取相对路径
                    URL url = new URL(storageObject.getUrl());
                    log.info(url.getPath());
                    imagePath = url.getPath();
                }
            }
        } else {
            UploadResult uploadResult = new MinioClientUtil().uploadFile(inputStream, "cdc", objectName, inputStream.available());
            if (null != uploadResult) {
                log.info("-----------------fileUrl------------:" + uploadResult.getFullPath());
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(uploadResult.getFullPath())) {
                    //截取相对路径
                    URL url = new URL(uploadResult.getFullPath());
                    log.info("---------------filepath is-------------:" + url.getPath());
                    imagePath =  url.getPath();
                }
            }
        }

        return imagePath;
    }


    /**
     * 存文件
     *
     * @param inputStream
     * @param objectName
     * @param headers
     * @return
     * @throws Exception
     */
    public String putObject(InputStream inputStream, String objectName, Map<String, String> headers, boolean absolutePath) throws Exception {
        String imagePath = "";
        StorageObject storageObject = storageClient.putObject(inputStream, swiftProperties.getContainer(), objectName, headers);
        if (null != storageObject) {
            log.info("-----------------fileUrl------------:" + storageObject.getUrl());
            if (StringUtils.isNotEmpty(storageObject.getUrl())) {
                //截取相对路径
                URL url = new URL(storageObject.getUrl());
                log.info("---------------filepath is-------------:" + url.getPath());
                imagePath = absolutePath ? storageObject.getUrl() : url.getPath();

            }
        }

        return imagePath;
    }

    /**
     * 存文件
     *
     * @param inputStream
     * @param objectName
     * @return
     * @throws Exception
     */
    public String putObject(InputStream inputStream, String objectName) throws Exception {
        String imagePath = "";
        if (checkStorageType()){
            StorageObject storageObject = storageClient.putObject(inputStream, swiftProperties.getContainer(), objectName);
            if (null != storageObject) {
                log.info(storageObject.getUrl());
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(storageObject.getUrl())) {
                    //截取相对路径
                    URL url = new URL(storageObject.getUrl());
                    log.info(url.getPath());
                    imagePath = url.getPath();
                }
            }
        }else {
            UploadResult uploadResult = new MinioClientUtil().uploadFile(inputStream, "cdc", objectName, inputStream.available());
            if (null != uploadResult) {
                log.info("-----------------fileUrl------------:" + uploadResult.getFullPath());
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(uploadResult.getFullPath())) {
                    //截取相对路径
                    URL url = new URL(uploadResult.getFullPath());
                    log.info("---------------filepath is-------------:" + url.getPath());
                    imagePath = url.getPath();
                }
            }
        }
        return imagePath;
    }

    /**
     * 存文件
     *
     * @param bytes
     * @param objectName
     * @return
     * @throws Exception
     */
    public String putObject(byte[] bytes, String objectName, boolean absolutePath) throws Exception {
        String imagePath = "";
        if (checkStorageType()) {
            StorageObject storageObject = storageClient.putObject(bytes, swiftProperties.getContainer(), objectName);
            if (null != storageObject) {
                log.info("-----------------fileUrl------------:" + storageObject.getUrl());
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(storageObject.getUrl())) {
                    //截取相对路径
                    URL url = new URL(storageObject.getUrl());
                    log.info("---------------filepath is-------------:" + url.getPath());
                    imagePath = absolutePath ? storageObject.getUrl() : url.getPath();

                }
            }
        } else {
            InputStream inputStream = new ByteArrayInputStream(bytes);
            UploadResult uploadResult = new MinioClientUtil().uploadFile(inputStream, "cdc", objectName, inputStream.available());
            if (null != uploadResult) {
                log.info("-----------------fileUrl------------:" + uploadResult.getFullPath());
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(uploadResult.getFullPath())) {
                    //截取相对路径
                    URL url = new URL(uploadResult.getFullPath());
                    log.info("---------------filepath is-------------:" + url.getPath());
                    imagePath = absolutePath ? uploadResult.getFullPath() : url.getPath();

                }
            }
        }
        return imagePath;
    }

    public String putObject(File file, String objectName) throws Exception {
        String imagePath = "";
        if (checkStorageType()) {
            StorageObject storageObject = storageClient.putObject(file, swiftProperties.getContainer(), objectName);
            if (null != storageObject) {
                log.info(storageObject.getUrl());
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(storageObject.getUrl())) {
                    //截取相对路径
                    URL url = new URL(storageObject.getUrl());
                    log.info(url.getPath());
                    imagePath = url.getPath();
                }
            }
        } else {
            UploadResult uploadResult;
            try (InputStream inputStream = Files.newInputStream(file.toPath())) {
                uploadResult = new MinioClientUtil().uploadFile(inputStream, "cdc", objectName, inputStream.available());
            } catch (Exception e) {
                log.error(e.getMessage());
                throw new MedicalBusinessException("获取文件流失败!" );
            }
            if (null != uploadResult) {
                log.info(uploadResult.getFullPath());
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(uploadResult.getFullPath())) {
                    //截取相对路径
                    URL url = new URL(uploadResult.getFullPath());
                    log.info(url.getPath());
                    imagePath = url.getPath();
                }
            }
        }
        return imagePath;
    }

    public String putObject(MultipartFile file, boolean absolutePath) throws Exception {
        String imagePath = "";
        if (checkStorageType()) {
            StorageObject storageObject = storageClient.putObject(file.getBytes(), swiftProperties.getContainer(), file.getOriginalFilename());
            if (null != storageObject) {
                log.info(storageObject.getUrl());
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(storageObject.getUrl())) {
                    //截取相对路径
                    URL url = new URL(storageObject.getUrl());
                    log.info("---------------filepath is-------------:" + url.getPath());
                    imagePath = absolutePath ? storageObject.getUrl() : url.getPath();
                }
            }
        } else {
            InputStream inputStream = file.getInputStream();
            UploadResult uploadResult = new MinioClientUtil().uploadFile(inputStream, "cdc", file.getOriginalFilename(), inputStream.available());
            if (null != uploadResult) {
                log.info(uploadResult.getFullPath());
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(uploadResult.getFullPath())) {
                    //截取相对路径
                    URL url = new URL(uploadResult.getFullPath());
                    log.info("---------------filepath is-------------:" + url.getPath());
                    imagePath = absolutePath ? uploadResult.getFullPath() : url.getPath();
                }
            }
        }
        return imagePath;
    }

    public String putObject(MultipartFile file, String objectName, boolean absolutePath) throws Exception {
        String imagePath = "";
        if (checkStorageType()) {
            StorageObject storageObject = storageClient.putObject(file.getBytes(), swiftProperties.getContainer(), objectName);
            if (null != storageObject) {
                log.info("-----------------fileUrl------------:" + storageObject.getUrl());
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(storageObject.getUrl())) {
                    //截取相对路径
                    URL url = new URL(storageObject.getUrl());
                    log.info("---------------filepath is-------------:" + url.getPath());
                    imagePath = absolutePath ? storageObject.getUrl() : url.getPath();
                }
            }
        } else {
            InputStream inputStream = file.getInputStream();
            UploadResult uploadResult = new MinioClientUtil().uploadFile(inputStream, "cdc", objectName, inputStream.available());
            if (null != uploadResult) {
                log.info("-----------------fileUrl------------:" + uploadResult.getFullPath());
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(uploadResult.getFullPath())) {
                    //截取相对路径
                    URL url = new URL(uploadResult.getFullPath());
                    log.info("---------------filepath is-------------:" + url.getPath());
                    imagePath = absolutePath ? uploadResult.getFullPath() : url.getPath();
                }
            }
        }
        return imagePath;
    }

    /**
     * 读取文件
     *
     * @param objectName
     * @return
     */
    public StorageObject getObject(String objectName) {
        StorageObject storageObject = new StorageObject();
        if (checkStorageType()) {
            try {
                storageObject = storageClient.getObject(swiftProperties.getContainer(), objectName);
            } catch (Exception e) {
                log.error("获取文件异常",e);
            }
        } else {
            try {
                objectName = encodeUrlPathComponent(objectName);
                InputStream inputStream = new URL(new MinioClientUtil().getFullPath("cdc", objectName)).openStream();
                storageObject.setInputStream(inputStream);
            } catch (Exception e) {
                log.error("获取文件异常", e);
            }
        }
        return storageObject;
    }

    /**
     * 根据唯一标识+文件名构建对象存储ObjName
     *
     * @param uniqueKey
     * @param fileName
     * @return
     */
    public String getObjectName(String uniqueKey, String fileName) {
        try {
            if (checkStorageType()) {
                return URLEncoder.encode(uniqueKey + "/" + fileName, "UTF-8");
            }
            {
                return uniqueKey + "/" + fileName;
            }
        } catch (UnsupportedEncodingException e) {
            log.error("获取对象名称错误：{}", e);
        }
        return uniqueKey;
    }

    /**
     * 对URL中的路径部分进行安全编码（保留路径分隔符，空格编码为%20）
     */
    public String encodeUrlPathComponent(String input) {
        try {
            // 先使用标准编码
            String encoded = URLEncoder.encode(input,"UTF-8");
            // 将+替换为%20，恢复/等特殊字符
            return encoded
                    .replace("+", "%20")
                    .replace("%2F", "/");  // 恢复路径分隔符
        } catch (Exception e) {
            log.error("URL编码失败: " + input, e);
            return input;
        }
    }

}
