package com.iflytek.cdc.admin.dto;


import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class DataAuthVO {
    private String userId;

    /**
     * 症候群列表
     */
    private List<SymptomNameVO> symptomList = new ArrayList<>();

    /**
     * 传染病列表
     */
    private List<InfectedNameVO> infectedList = new ArrayList<>();

    /**
     * 学校症状列表
     */
    private List<SympNameVO> schoolSymptomList = new ArrayList<>();

    /**
     * 中毒症状列表
     */
    private List<PoisonNameVO> poisonList = new ArrayList<>();

    /**
     * 门诊列表
     */
    private List<OutpatientNameVO> outpatientList = new ArrayList<>();

    /**
     * 不明原因列表
     */
    private List<UnknownReasonNameVO> unknownReasonList = new ArrayList<>();

    /**
     * 联防联控列表
     */
    private List<PreventionControlNameVO> preventionControlList = new ArrayList<>();

    /**
     * 自定义预警列表
     */
    private List<CustomizedNameVO> customizedList = new ArrayList<>();
}
