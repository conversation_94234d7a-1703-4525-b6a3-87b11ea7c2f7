package com.iflytek.cdc.admin.util;

import java.util.Collection;

public class MyCollectionUtil {

    /**
     * 判断集合是否为空并且size>0
     * @param collection
     * @return
     */
    public static Boolean isNotNullAndSizeMoreThanZero(Collection collection){
        Boolean flag = true;
        if (collection == null){
            flag = false;
        }else {
            if (collection.size() <= 0){
                flag = false;
            }
        }
        return flag;
    }
}
