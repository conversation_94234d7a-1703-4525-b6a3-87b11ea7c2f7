package com.iflytek.cdc.admin.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @description tb_cdcmr_population_data_info
 * <AUTHOR>
 * @date 2024-03-28
 */
@Data
public class TbCdcmrPopulationDataInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    public static final String TABLE_NAME = "tb_cdcmr_population_data_info";

    /**
     * id
     */
    private String id;

    /**
     * 区域编码
     */
    private String areaCode;

    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 市名称
     */
    private String cityName;

    /**
     * 功能区编码
     */
    private String districtCode;

    /**
     * 功能区名称
     */
    private String districtName;

    /**
     * 功能区编码
     */
    private String streetCode;

    /**
     * 功能区名称
     */
    private String streetName;

    /**
     * 常住人口
     */
    private Integer residentPopulation;

    /**
     * 户籍人口
     */
    private Integer registeredPopulation;

    /**
     * GDP数据-单位（亿元）
     */
    private BigDecimal gdp;

    /**
     * 城镇居民可支配收入
     */
    private BigDecimal urbanDpi;

    /**
     * 农村居民可支配收入
     */
    private BigDecimal ruralDpi;

    /**
     * 备注
     */
    private String notes;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 删除标识: 0-未删除,1-已删除
     */
    private Integer deleteFlag;

    /**
     * 统计日期
     */
    private Date statDate;

}