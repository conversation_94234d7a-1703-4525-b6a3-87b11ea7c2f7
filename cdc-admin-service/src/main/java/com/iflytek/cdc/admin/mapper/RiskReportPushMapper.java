package com.iflytek.cdc.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.admin.dto.RiskReportPushRecordDto;
import com.iflytek.cdc.admin.dto.RiskReportPushRecordQueryDto;
import com.iflytek.cdc.admin.entity.TbCdcmrRiskReportPushRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RiskReportPushMapper extends BaseMapper<TbCdcmrRiskReportPushRecord> {
    List<RiskReportPushRecordDto> queryList(@Param("loginUserId") String loginUserId, @Param("dto") RiskReportPushRecordQueryDto dto);
}
