package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.entity.TbCdcAttachment;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 附件操作
 *
 * <AUTHOR>
 */
@Mapper
public interface TbCdcAttachmentMapper {

    /**
     * 新增
     *
     * @param record
     * @return
     */
    int insert(TbCdcAttachment record);

    /**
     * 主键查询
     *
     * @param id
     * @return
     */
    TbCdcAttachment selectByPrimaryKey(String id);

    /**
     * 多主键查询
     *
     * @param ids
     * @return
     */
    List<TbCdcAttachment> selectByPrimaryKeys(List<String> ids);


}