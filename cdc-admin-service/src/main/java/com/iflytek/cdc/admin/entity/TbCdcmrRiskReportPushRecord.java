package com.iflytek.cdc.admin.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.iflytek.cdc.admin.dto.RiskReportPushUserInfoDto;
import lombok.Data;

import java.util.Date;

@Data
public class TbCdcmrRiskReportPushRecord {

    private String id;

    private Date pushTime;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private RiskReportPushUserInfoDto recipient;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private RiskReportPushUserInfoDto sender;

    private String taskId;

    private String provinceCode;
    
    private String provinceName;

    private String cityCode;
    
    private String cityName;

    private String districtCode;

    private String districtName;

    private String creatorId;

    private String creator;

    private Date createTime;

    private String updatorId;

    private String updator;

    private Date updateTime;


}
