package com.iflytek.cdc.admin.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 病案文件审批权限表dto
 */
@Data
public class IllnessFileApprovalAuthDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("权限编码")
    private String authCode;

    @ApiModelProperty("权限名称")
    private String authName;

    @ApiModelProperty("审批人id")
    private String approvalUserId;

    @ApiModelProperty("多审批人id")
    @TableField(exist = false)
    private List<String> approvalUserIds;

    @ApiModelProperty("机构id")
    private String orgId;

    @ApiModelProperty("机构名称")
    private String orgName;
}