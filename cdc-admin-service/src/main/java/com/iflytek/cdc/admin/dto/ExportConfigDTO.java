package com.iflytek.cdc.admin.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ExportConfigDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    private String id;//权限配置ID
    private String exportType; // 导出分类也就是导出类型
    private String exportName; // 导出项目名称
    private Boolean approvalRequired; // 审核权限
    private Integer approvalTimeLimit; // 审核时效
    private Integer approvalLevels; // 审核层级
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updaterTime; // 最后操作时间也就是更新时间

    /**
     * 审批责任人
     */
    private String approve;

    /**
     * 机构Id
     */
    private String orgId;

    /**
     * 导出权限机构id
     */
    private String permissionOrgId;
}
