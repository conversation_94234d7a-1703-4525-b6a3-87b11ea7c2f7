package com.iflytek.cdc.admin.enums;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum WarningTypeEnum {
    INFECTION("传染病", "INFECTION"),
    SYNDROME("症候群", "SYNDRO<PERSON>"),
    SYMPTOM("症状", "SYMPTOM"),
    POISON("中毒", "POISON"),
    UNKOWN_REASON("不明原因", "UNKOWN_REASON"),
    OUTPATIENT("发热和肠道门诊", "OUTPATIENT"),
    CUSTOMIZED("自定义", "CUSTOMIZED");

    private String name;
    private String code;


    private WarningTypeEnum(String name, String code) {
        this.code = code;
        this.name = name;
    }

    public static List<Map<String, String>> getEnumContent() {
        List<Map<String, String>> enumContentList = new ArrayList<>();
        for (WarningTypeEnum enumValue : WarningTypeEnum.values()) {
            Map<String, String> enumContentMap = new HashMap<>();
            enumContentMap.put("name", enumValue.getName());
            enumContentMap.put("code", enumValue.getCode());
            enumContentList.add(enumContentMap);
        }
        return enumContentList;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
