package com.iflytek.cdc.admin.dto;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class WarningRiskLevelDTO {


    @ApiModelProperty(value = "id")
    String id;

    @ApiModelProperty(value = "风险等级id")
    String riskLevelId;

    @ApiModelProperty(value = "风险分级描述")
    private String riskLevelDesc;

    @ApiModelProperty(value = "核实时限")
    private Integer checkTimeLimit;

    @ApiModelProperty(value = "现场调查时限")
    private Integer investTimeLimit;

    @ApiModelProperty(value = "发起研判时限")
    private Integer judgeTimeLimit;

    @ApiModelProperty(value = "疾病id")
    private String diseaseId;

    @ApiModelProperty(value = "疾病编码")
    private String diseaseCode;

    @ApiModelProperty(value = "疾病名称")
    private String diseaseName;
}
