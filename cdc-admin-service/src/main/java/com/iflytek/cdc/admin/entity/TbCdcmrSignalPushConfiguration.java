package com.iflytek.cdc.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 信号推送配置表
 */
@Data
@TableName(value = "tb_cdcmr_signal_push_configuration")
public class TbCdcmrSignalPushConfiguration{
    public static final String TABLE_NAME = "tb_cdcmr_signal_push_configuration";
    @TableField(value = "id")
    private String id;

    @TableField(value = "disease_name")
    private String diseaseName;

    @TableField(value = "disease_code")
    private String diseaseCode;

    @TableField(value = "is_repeat")
    private String isRepeat;

    @TableField(value = "repeat_end_time")
    private Date repeatEndTime;

    @TableField(value = "repeat_frequency")
    private String repeatFrequency;

    @TableField(value = "repeat_start_time")
    private Date repeatStartTime;

    @TableField(value = "risk_level_detail_id")
    private String riskLevelDetailId;

    @TableField(value = "warning_charge_person_table_id")
    private String warningChargePersonTableId;

    @TableField(value = "message_config_id")
    private String messageConfigId;

    @TableField(value = "creator_id")
    private String creatorId;

    @TableField(value = "creator")
    private String creator;

    @TableField(value = "create_time")
    private Date createTime;

    @TableField(value = "updater_id")
    private String updaterId;

    @TableField(value = "updater")
    private String updater;

    @TableField(value = "update_time")
    private Date updateTime;
}