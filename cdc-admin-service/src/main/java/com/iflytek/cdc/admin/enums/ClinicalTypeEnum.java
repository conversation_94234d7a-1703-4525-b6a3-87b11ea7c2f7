package com.iflytek.cdc.admin.enums;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum ClinicalTypeEnum {
    Clinical("临床诊断病例", 1),
    DIAGNOSIS("确诊病例", 2),
    Pathogenic("病原携带者", 3),
    POSITIVE("阳性检测", 4);
    private final String name;
    private final Integer code;

    ClinicalTypeEnum(String name, Integer code) {
        this.code = code;
        this.name = name;
    }

    public static List<Map<String, Object>> getAllToList() {
        List<Map<String, Object>> list = new ArrayList<>();
        ClinicalTypeEnum[] values = values();
        for (ClinicalTypeEnum objectEnum : values) {
            Map<String, Object> valuesMap = new HashMap<>();
            valuesMap.put("name", objectEnum.name);
            valuesMap.put("code", objectEnum.code);
            list.add(valuesMap);
        }
        return list;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
