package com.iflytek.cdc.admin.mapper.province;

import com.iflytek.cdc.admin.entity.mr.TbCdcmrInfectedDiseaseWarningRule;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.admin.vo.InfectedWarningRuleDetailVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 传染病预警-传染病预警规则表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-23
 */
public interface TbCdcmrInfectedDiseaseWarningRuleMapper extends BaseMapper<TbCdcmrInfectedDiseaseWarningRule> {

    List<TbCdcmrInfectedDiseaseWarningRule> getListByInfectedDiseaseWarningId(@Param("diseaseInfoId") String diseaseInfoId,
                                                                              @Param("riskLevel") String riskLevel,
                                                                              @Param("warningMethod") String warningMethod,
                                                                              @Param("followStatus") String followStatus);

    /**
     * 统计传染病，各疾病规则数量
     * */
    List<InfectedWarningRuleDetailVO> getRiskRuleCountBy();

    /**
     * 更新 or 插入
     * */
    void upsert(TbCdcmrInfectedDiseaseWarningRule rule);

}
