package com.iflytek.cdc.admin.dto.addressstandardize;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName AddressDetailMapping
 * @Description 模糊地址映射类
 * <AUTHOR>
 * @Date 2021/9/16 13:46
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AddressStandardLatestVo extends AddressStandardVo implements Serializable {

    private static final long serialVersionUID = 4983261891615218891L;

    /**
     * 标准化地址 经度
     */
    @ApiModelProperty(value = "地址名称", position = 13)
    private String addressName;

    /**
     * 标准化地址 纬度
     */
    @ApiModelProperty(value = "地址描述", position = 14)
    private String addressDetailDesc;

}