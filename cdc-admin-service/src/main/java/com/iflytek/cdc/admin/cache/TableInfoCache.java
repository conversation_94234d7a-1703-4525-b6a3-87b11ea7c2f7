package com.iflytek.cdc.admin.cache;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmMetadataTableColumnInfo;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmMetadataTableInfo;
import com.iflytek.cdc.admin.datamodel.mapper.TbCdcdmMetadataTableColumnInfoMapper;
import com.iflytek.cdc.admin.datamodel.mapper.TbCdcdmMetadataTableInfoMapper;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class TableInfoCache {

    @Resource
    private TbCdcdmMetadataTableInfoMapper tableInfoMapper;

    @Resource
    private TbCdcdmMetadataTableColumnInfoMapper columnInfoMapper;

    private final Cache<String, TbCdcdmMetadataTableInfo> tableCache = Caffeine.newBuilder()
                                                                               .expireAfterWrite(1, TimeUnit.DAYS)
                                                                               .build();

    private final Cache<String, TbCdcdmMetadataTableColumnInfo> columnCache = Caffeine.newBuilder()
                                                                                      .expireAfterWrite(1, TimeUnit.DAYS)
                                                                                      .build();

    @PostConstruct
    public void initCache() {
        refreshCache();
    }

    public void refreshCache() {

        //将表信息添加到缓存中
        List<TbCdcdmMetadataTableInfo> tableInfoList = tableInfoMapper.getAllTableInfo();
        Map<String, TbCdcdmMetadataTableInfo> tableInfoMap = tableInfoList.stream()
                                                                          .collect(Collectors.toMap(TbCdcdmMetadataTableInfo::getId, Function.identity()));
        tableCache.putAll(tableInfoMap);

        //将字段信息添加到缓存中
        List<TbCdcdmMetadataTableColumnInfo> columnInfoList = columnInfoMapper.getAllColumnInfo();
        Map<String, TbCdcdmMetadataTableColumnInfo> columnInfoMap = columnInfoList.stream()
                                                                                  .collect(Collectors.toMap(TbCdcdmMetadataTableColumnInfo::getId, Function.identity()));
        columnCache.putAll(columnInfoMap);
    }

    /**
     * 从缓存获取表信息，缓存没有则重新查
     * */
    public TbCdcdmMetadataTableInfo getTableInfoByCache(String tableId){

        return tableCache.get(tableId, res -> getTableInfoByTableId(tableId));
    }

    /**
     * 从缓存获取列信息，缓存没有则重新查
     * */
    public TbCdcdmMetadataTableColumnInfo getColumnInfoByCache(String columnId){

        return columnCache.get(columnId, res -> getColumnInfoByTableId(columnId));
    }

    /**
     * 根据表id获取表信息
     * */
    public TbCdcdmMetadataTableInfo getTableInfoByTableId(String tableId){

        List<TbCdcdmMetadataTableInfo> tableInfoList = tableInfoMapper.getAllTableInfo();
        return tableInfoList.stream().filter(e -> tableId.equals(e.getId())).findFirst().orElse(null);
    }

    /**
     * 根据列id获取列信息
     * */
    public TbCdcdmMetadataTableColumnInfo getColumnInfoByTableId(String columnId){

        List<TbCdcdmMetadataTableColumnInfo> columnInfoList = columnInfoMapper.getAllColumnInfo();
        return columnInfoList.stream().filter(e -> columnId.equals(e.getId())).findFirst().orElse(null);
    }
}
