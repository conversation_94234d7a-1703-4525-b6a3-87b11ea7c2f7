package com.iflytek.cdc.admin.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class ExportTaskDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "任务名称")
    private String taskName;

    @ApiModelProperty(value = "导出任务URL")
    private String taskUrl;

    @ApiModelProperty(value = "导出任务请求参数")
    private String taskParam;

    @ApiModelProperty(value = "数据量")
    private Integer totalCount;

    @ApiModelProperty(value = "导出文件id")
    private String attachmentId;

    @ApiModelProperty(value = "导出文件地址")
    private String attachmentUrl;

    @ApiModelProperty(value = "导出文件大小")
    private String attachmentSize;

    @ApiModelProperty(value = "导出结果信息")
    private String exportResultInfo;

    @ApiModelProperty(value = "备注")
    private String notes;

    @ApiModelProperty(value = "导出状态（启用状态 1进行中;2已完成;3任务中断）")
    private Integer status;

    @ApiModelProperty(value = "删除标识: 0-未删除,1-已删除")
    private String deleteFlag;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "创建人id")
    private String creatorId;

    @ApiModelProperty(value = "创建人姓名")
    private String creator;

    @ApiModelProperty("导出类型")
    private String exportType;
    private String appCode;

    @ApiModelProperty("导出项目")
    private String exportName;

    @ApiModelProperty("文件大小")
    private Integer size;
}
