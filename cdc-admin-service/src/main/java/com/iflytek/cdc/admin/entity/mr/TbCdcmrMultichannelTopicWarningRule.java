package com.iflytek.cdc.admin.entity.mr;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 多渠道专题预警规则表;
 * <AUTHOR> dingyuan
 * @date : 2024-9-14
 */
@ApiModel(value = "多渠道专题预警规则表")
@Data
public class TbCdcmrMultichannelTopicWarningRule implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id ;

    /**
     * 多渠道专题id
     */
    @ApiModelProperty(value = "多渠道专题id")
    private String topicId ;

    /**
     * json参数，同传染病、症候群
     */
    @ApiModelProperty(value = "json参数，同传染病、症候群")
    private String modelParam ;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String notes ;

    /**
     * 状态（启用状态 1启用；0未启用）
     */
    @ApiModelProperty(value = "状态（启用状态 1启用；0未启用）")
    private Integer status ;

    /**
     * 删除标识： 0-未删除，1-已删除
     */
    @ApiModelProperty(value = "删除标识： 0-未删除，1-已删除")
    private String deleteFlag ;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime ;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime ;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    private String creatorId ;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名")
    private String creator ;

    /**
     * 更新人id
     */
    @ApiModelProperty(value = "更新人id")
    private String updaterId ;

    /**
     * 更新人name
     */
    @ApiModelProperty(value = "更新人name")
    private String updater ;

}
