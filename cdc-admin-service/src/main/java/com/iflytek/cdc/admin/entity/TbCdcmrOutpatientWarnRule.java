package com.iflytek.cdc.admin.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * tb_cdcmr_outpatient_warn_rule
 * <AUTHOR>
@Data
public class TbCdcmrOutpatientWarnRule implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 时间跨度(天)
     */
    private Integer timeRange;

    /**
     * 病例环比增长比例(%)
     */
    private Integer growthRate;

    /**
     * 删除标识：0未删除1删除
     */
    private Integer isDeleted;

    /**
     * 开启状态：0禁用1启用
     */
    private Integer status;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 预警id
     */
    private String warnId;

    private static final long serialVersionUID = 1L;
}