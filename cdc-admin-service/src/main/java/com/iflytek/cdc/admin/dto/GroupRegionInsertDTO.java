package com.iflytek.cdc.admin.dto;

import com.iflytek.cdc.admin.vo.LabelValueVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 小区/组 - 区划信息维表
 */
@Data
public class GroupRegionInsertDTO {

    @ApiModelProperty(value = "父级区划代码")
    private LabelValueVO villageLabel;

    /**
     * 小区/组名称
     */
    @ApiModelProperty(value = "小区/组名称")
    private String groupName;


    @ApiModelProperty(value = "小区/组别名")
    private String groupAliasName;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String memo;

}