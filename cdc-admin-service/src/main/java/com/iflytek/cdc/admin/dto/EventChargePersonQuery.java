package com.iflytek.cdc.admin.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("事件负责人配置查询条件")
public class EventChargePersonQuery extends PageInfoDTO {
    /**
     * 事件类型
     */
    @ApiModelProperty(value = "事件类型")
    private String eventType;

    @ApiModelProperty(value = "疾病编码")
    private String diseaseCode;

    @ApiModelProperty(value = "初步诊断")
    private String initDiagnose;

    @ApiModelProperty(value = "疾病编码集合")
    private List<String> diseaseCodes;

    /**
     * 事件严重等级
     */
    @ApiModelProperty(value = "事件严重等级id")
    private String eventLevelId;
}
