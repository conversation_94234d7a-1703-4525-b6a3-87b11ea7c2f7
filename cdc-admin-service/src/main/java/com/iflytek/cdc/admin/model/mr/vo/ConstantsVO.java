package com.iflytek.cdc.admin.model.mr.vo;

import com.iflytek.cdc.admin.enums.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;

@Getter
@ApiModel(value = "常量类")
public class ConstantsVO {

    public static final ConstantsVO instance = new ConstantsVO();

    @ApiModelProperty(value = "状态常量")
    private final Object status = StatusEnum.mapValues();

    @ApiModelProperty(value = "删除标识枚举")
    private final Object deleteFlag = DeleteFlagEnum.mapValues();

    @ApiModelProperty(value = "监测症状分类枚举")
    private final Object manifestClassify = ManifestClassifyEnum.mapValues();

    @ApiModelProperty(value = "疾病表现类型枚举")
    private final Object diseaseManifestation = DiseaseManifestationEnum.mapValues();

    @ApiModelProperty(value = "条件类型枚举")
    private final Object conditionType = ConditionTypeEnum.mapValues();

    @ApiModelProperty(value = "人群分类")
    private final Object personScope = PersonScopeEnum.mapValues();

    @ApiModelProperty(value = "实体模型状态")
    private final Object tableStatus = TableStatusEnum.mapValues();

    @ApiModelProperty(value = "实体表数据类型")
    private final Object tableColumnType = TableColumnTypeEnum.mapValues();
}
