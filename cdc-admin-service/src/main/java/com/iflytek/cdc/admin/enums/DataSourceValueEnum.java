package com.iflytek.cdc.admin.enums;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum DataSourceValueEnum {
    CASE("病历", "2"),
    REPORT_CARD("报卡", "1");

    private String name;
    private String code;


     DataSourceValueEnum(String name, String code) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static List<Map<String,String>> getEnumContent() {
        List<Map<String,String>> enumContentList = new ArrayList<>();
        for (DataSourceValueEnum enumValue : DataSourceValueEnum.values()) {
            Map<String,String> enumContentMap = new HashMap<>();
            enumContentMap.put("name", enumValue.getName());
            enumContentMap.put("code", enumValue.getCode());
            enumContentList.add(enumContentMap);
        }
        return enumContentList;
    }

    public static String getNameByCode(String code) {
        for (DataSourceValueEnum dataSource : DataSourceValueEnum.values()) {
            if (dataSource.getCode().equals(code)) {
                return dataSource.getName();
            }
        }
        return code;
    }
}
