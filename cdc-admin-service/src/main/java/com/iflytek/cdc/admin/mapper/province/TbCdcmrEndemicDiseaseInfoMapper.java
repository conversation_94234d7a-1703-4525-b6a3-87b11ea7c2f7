package com.iflytek.cdc.admin.mapper.province;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrEndemicDiseaseInfo;
import com.iflytek.cdc.admin.model.mr.dto.CommonMasterData;
import com.iflytek.cdc.admin.model.mr.vo.EndemicDiseaseInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbCdcmrEndemicDiseaseInfoMapper extends BaseMapper<TbCdcmrEndemicDiseaseInfo> {
    List<EndemicDiseaseInfoVO> getEndemicTreeInfo();

    List<String> getAllDiseaseCodeBy();

    void insertOrUpdate(List<TbCdcmrEndemicDiseaseInfo> list);

    void updateDeleteFlagById(String id);

    String getEndemicDiseaseCodeByName(String diseaseName);

    List<TbCdcmrEndemicDiseaseInfo> listAll();

    void insertBatch(@Param("list") List<TbCdcmrEndemicDiseaseInfo> list);

    List<TbCdcmrEndemicDiseaseInfo> getEndemicDiseaseInfoByIds(@Param("ids") List<String> ids);

    TbCdcmrEndemicDiseaseInfo getDiseaseInfoById(String id);

    void updateSubDiseaseParent(@Param("info")TbCdcmrEndemicDiseaseInfo info,
                                @Param("loginUserId") String loginUserId,
                                @Param("loginUserName") String loginUserName);

    void updateDeleteFlagByIds(@Param("ids") List<String> ids,
                               @Param("loginUserId") String loginUserId,
                               @Param("loginUserName") String loginUserName);

    CommonMasterData getDiseaseInfoByCode(@Param("masterDataCode") String masterDataCode);
}