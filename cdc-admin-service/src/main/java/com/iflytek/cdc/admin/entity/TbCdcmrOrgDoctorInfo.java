package com.iflytek.cdc.admin.entity;

import java.util.Date;
import lombok.Data;


/**
 * @Description
 * <AUTHOR>
 * @Date 2022/4/21 
 */
 
@Data
public class TbCdcmrOrgDoctorInfo {
    private String id;

    /**
    * 机构Id
    */
    private String orgId;

    /**
    * 机构名称
    */
    private String orgName;

    /**
    * 科室Id
    */
    private String deptId;

    /**
    * 科室名称
    */
    private String deptName;

    /**
    * 医生Id
    */
    private String docId;

    /**
    * 医生姓名
    */
    private String docName;

    /**
    * 联系电话
    */
    private String phone;

    /**
    * 是否禁用
    */
    private Boolean forbidden;

    /**
    * 是否删除
    */
    private Boolean deleted;

    /**
    * 创建日期
    */
    private Date createTime;

    /**
    * 创建人
    */
    private String creator;

    /**
    * 更新日期
    */
    private Date updateTime;

    /**
    * 更新人
    */
    private String updater;
}