package com.iflytek.cdc.admin.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * tb_cdcmr_inspection_standard
 *
 * <AUTHOR>
@Data
public class TbCdcmrInspectionStandard implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 检验项目编码
     */
    private String inspectionItemCode;
    /**
     * 检验项目名称
     */
    private String inspectionItem;
    /**
     * 项目类型
     */
    private String itemType;
    /**
     * 业务分类
     */
    private String businessType;
    /**
     * 启用状态
     */
    private Integer status;
    /**
     * 备注
     */
    private String remark;
    /**
     * 更新人
     */
    private String updateUser;
    /**
     * 更新时间
     */
    private Date updateTime;
}