package com.iflytek.cdc.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "QueryAssociationPageVO",description = "查询关联分页VO")
public class QueryAssociationPageVO {
    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private String id;
    /**
     * 值含义
     */
    @ApiModelProperty(value = "值含义")
    private String name;
    /**
     * 值编码
     */
    @ApiModelProperty(value = "值代码")
    private String code;
    /**
     * 是否关联
     */
    @ApiModelProperty(value = "是否关联", allowableValues="0-未关联,1-已关联")
    private String isAssociation;
}
