package com.iflytek.cdc.admin.model.mr.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 树节点 属性
 * */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TreeNodeProperty {

    @ApiModelProperty(value = "节点排序")
    private Integer orderFlag;

    @ApiModelProperty(value = "节点更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value = "节点更新人")
    private String updater;

    @ApiModelProperty(value = "疾病属性 - 预警规则")
    private List<RiskRuleCountVO> ruleCount;

    @Data
    public static class RiskRuleCountVO{
        @ApiModelProperty(value = "风险等级id")
        private String riskLevel;

        @ApiModelProperty(value = "风险等级数量")
        private Integer riskLevelCount;

    }

    /**
     * 树节点属性处理
     * */
    public static void mergeTreeProperty(List<TreeNode> root, Map<String, List<RiskRuleCountVO>> propertyMap) {

        if(root == null){
            return;
        }
        for (TreeNode node : root) {
            if (propertyMap.get(node.getId()) != null){
                TreeNodeProperty property = TreeNodeProperty.builder()
                                                            .ruleCount(propertyMap.get(node.getId()))
                                                            .build();
                node.setProperty(property);
            }
            mergeTreeProperty(node.getChildren(), propertyMap);
        }
    }

}
