package com.iflytek.cdc.admin.enums;

import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public enum ExportCodeEnum {

    INFECTIOUS_DISEASE_CASE_LIST("INFECTIOUS_DISEASE_CASE_LIST", "传染病病例列表"),
    INFECTIOUS_DISEASE_CASE_DATA_POOL("INFECTIOUS_DISEASE_CASE_DATA_POOL", "传染病病例数据池"),
    SYNDROME_CASE_LIST("SYNDROME_CASE_LIST", "症候群病例列表"),
    SYNDROME_CASE_DATA_POOL("SYNDROME_CASE_DATA_POOL", "症候群病例数据池"),
    MEDICAL_VISIT_DATA_POOL("MEDICAL_VISIT_DATA_POOL", "就诊数据池"),
    PERSONAL_DISEASE_RECORD_POOL("PERSONAL_DISEASE_RECORD_POOL", "个人疾病档案池"),

    CLINIC_DATA_POOL("CLINIC_DATA_POOL","门诊数据池"),
    INPATIENT_DATA_POOL("INPATIENT_DATA_POOL","住院数据池"),
    DISEASE_CASE_LIST("DISEASE_CASE_LIST","病例列表"),
    POSITIVE_CASE_DATA_POOL("POSITIVE_CASE_DATA_POOL","阳性病例数据池"),
    COLLABORATIVE_MONITORING_DATA_POOL("COLLABORATIVE_MONITORING_DATA_POOL","协同监测数据池"),
    PATHOGEN_MONITORING_DATA_POOL("PATHOGEN_MONITORING_DATA_POOL","病原监测数据池")
    ;
    private final String code;
    private final String description;

    ExportCodeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据 code 获取枚举值
     */
    public static ExportCodeEnum getByCode(String code) {
        for (ExportCodeEnum project : ExportCodeEnum.values()) {
            if (project.getCode().equals(code)) {
                return project;
            }
        }
        log.error("无效的导出代码: {}", code);
        throw new MedicalBusinessException("无效的导出代码");
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}