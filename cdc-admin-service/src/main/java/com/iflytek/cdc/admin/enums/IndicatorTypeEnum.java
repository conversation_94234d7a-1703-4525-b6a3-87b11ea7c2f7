package com.iflytek.cdc.admin.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;

/**
 * 指标类型
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@Getter
public enum IndicatorTypeEnum {

    ATOMIC("atomic", "原子指标"),
//    DERIVATIVE("derivative", "衍生指标"),
    COMPOSITE("composite", "复合指标"),

    ;
    private final String name;
    private final String desc;

    IndicatorTypeEnum(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }
}
