package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.dto.InfectedGradeConfigVO;
import com.iflytek.cdc.admin.dto.SearchInfecInfoDTO;
import com.iflytek.cdc.admin.dto.UpdateInfecDTO;
import com.iflytek.cdc.admin.entity.CascadeVO;
import com.iflytek.cdc.admin.entity.InfectiousDiseases;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName InfectiousDiseasesMapper
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/7/29 10:45
 * @Version 1.0
 */
public interface InfectiousDiseasesMapper {
    int deleteByPrimaryKey(String id);

    int insert(InfectiousDiseases record);

    InfectiousDiseases selectByPrimaryKey(String id);
    InfectiousDiseases selectByDiseasesCode(String diseasesCode);
    InfectiousDiseases selectByDiseasesName(String diseasesName);
    int updateByPrimaryKey(InfectiousDiseases record);

    List<InfectiousDiseases> queryInfecInfo(SearchInfecInfoDTO searchInfecInfoDTO);

    List<InfectedGradeConfigVO> queryInfectedInfoByGrade(@Param("diseasesName") String diseasesName, @Param("diseasesType") String diseasesType, @Param("diseasesClassify") String diseasesClassify, @Param("status") Integer status);


    void insertInfectiousDiseases(@Param("infectiousDiseases") List<InfectiousDiseases> infectiousDiseases);

    void updateInfecInfo(UpdateInfecDTO updateInfecDTO);

    void updateInfectiousDiseases(InfectiousDiseases infectiousDiseases);

    InfectiousDiseases queryInfoById(String id);

    void updateCodeName(@Param("updateCode") String code, @Param("updateCodeName") String codeName, @Param("updateUser") String updateUser);

    InfectiousDiseases findInfectiousDiagnosisByInfectedCode(@Param("infectedCode") String infectedCode);

    List<CascadeVO> findByInfectClass(@Param("infectClassCode") String infectClassCode);

    List<InfectiousDiseases> queryInfecInfo2(SearchInfecInfoDTO searchInfecInfoDTO);
}