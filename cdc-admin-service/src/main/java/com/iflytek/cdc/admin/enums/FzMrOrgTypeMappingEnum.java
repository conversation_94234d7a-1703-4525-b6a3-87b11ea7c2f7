package com.iflytek.cdc.admin.enums;

import lombok.Getter;

/**
 * 辅诊与我们的机构类型映射enum(若更改层级结构了，那么其parentOrgType可用，负否则不可用)
 * <AUTHOR>
 * @Date 2021/10/19
 **/
@Getter
public enum FzMrOrgTypeMappingEnum {

    /**
     * 辅诊是卫计委/卫计局类型
     */
    HFPC_ORG_TYPE("530","100","疾控中心","100","疾控中心",false),

    /**
     * 辅诊是社区医院
     */
    SOCIAL_HOS_ORG_TYPE("510","121","卫生院","120","基层医疗",true),

    /**
     * 辅诊是医院
     */
    HOS_ORG_TYPE("520","111","医院","110","等级医院",true),

    /**
     * 辅诊的是卫生室
     */
    CLINIC_ORG_TYPE("550","122","服务站/卫生室","121","卫生院",true);



    /**
     * 辅诊的机构类型
     */
    private String sourceOrgTypeCode;

    /**
     * 映射到我们后管的机构类型
     */
    private String targetOrgTypeCode;

    /**
     * 映射到我们的机构类型名称
     */
    private String targetOrgTypeName;

    /**
     * 映射到我们的机构类型对应的父机构类型
     */
    private String targetParentOrgTypeCode;

    /**
     * 映射到我们的机构类型对应的父机构类型名称
     */
    private String targetParentOrgTypeName;

    /**
     * 映射到我们的机构类型对应的父机构类型能否直接使用
     */
    private Boolean parentCanUseDirect;


    FzMrOrgTypeMappingEnum(String sourceOrgTypeCode, String targetOrgTypeCode, String targetOrgTypeName, String targetParentOrgTypeCode, String targetParentOrgTypeName, Boolean parentCanUseDirect){
        this.sourceOrgTypeCode = sourceOrgTypeCode;
        this.targetOrgTypeCode = targetOrgTypeCode;
        this.targetOrgTypeName = targetOrgTypeName;
        this.targetParentOrgTypeCode = targetParentOrgTypeCode;
        this.targetParentOrgTypeName = targetParentOrgTypeName;
        this.parentCanUseDirect=parentCanUseDirect;
    }

    /**
     * 根据fzOrgTypeCode获取枚举
     * @param fzOrgTypeCode
     * @return
     */
    public static FzMrOrgTypeMappingEnum getFzMrOrgTypeMappingEnumByCode(String fzOrgTypeCode){
        FzMrOrgTypeMappingEnum[] values = FzMrOrgTypeMappingEnum.values();
        for (FzMrOrgTypeMappingEnum value : values) {
            if(value.getSourceOrgTypeCode().equals(fzOrgTypeCode)){
                return value;
            }
        }
        return null;
    }
}
