package com.iflytek.cdc.admin.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * tb_cdcmr_medical_warn
 * <AUTHOR>
@Data
public class TbCdcmrMedicalWarn implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 病种编码
     */
    private String diseaseCode;

    /**
     * 病种名称
     */
    private String diseaseName;

    /**
     * 规则描述
     */
    private String ruleDesc;

    /**
     * 规则状态(是否启用  0:否   1:是)
     */
    private String ruleStatus;

    /**
     * 创建时间
     */
    private Date createDatetime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新时间
     */
    private Date updateDatetime;

    /**
     * 更新人
     */
    private String updator;

    /**
     * 是否删除;0 否 1是
     */
    private String deleteFlag;

    /**
     * 信号生成节点
     */
    private Short eventGenerationNode;

    /**
     * 信号静默时长
     */
    private Short silenceDuration;

    /**
     * 静默时长单位
     */
    private Short silenceDurationUnit;

    /**
     * AI排除开启状态
     */
    private Short aiRemovedState;

    /**
     * AI排除时长
     */
    private Short aiRemovedDuration;

    /**
     * AI排除时长单位
     */
    private Short aiRemovedDurationUnit;

    /**
     * 是否为单病例触发
     */
    private Short isSingleCase;

    private static final long serialVersionUID = 1L;
}