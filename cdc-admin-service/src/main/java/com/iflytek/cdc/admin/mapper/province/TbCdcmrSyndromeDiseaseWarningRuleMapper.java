package com.iflytek.cdc.admin.mapper.province;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrSyndromeDiseaseWarningRule;
import com.iflytek.cdc.admin.vo.SyndromeWarningRuleDetailVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TbCdcmrSyndromeDiseaseWarningRuleMapper extends BaseMapper<TbCdcmrSyndromeDiseaseWarningRule> {

    /**
     * 保存 or 更新规则
     */
    void upsert(TbCdcmrSyndromeDiseaseWarningRule rule);

    List<TbCdcmrSyndromeDiseaseWarningRule> getListBySyndromeDiseaseWarningId(@Param("diseaseInfoId") String diseaseInfoId,
                                                                              @Param("riskLevel") String riskLevel,
                                                                              @Param("warningMethod") String warningMethod,
                                                                              @Param("followStatus") String followStatus);

    List<SyndromeWarningRuleDetailVO> getRiskRuleCountBy();

}
