package com.iflytek.cdc.admin.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

@Getter
public enum StatisticalPeriodEnum {

    DAY("day", "日报"),
    WEEK("week", "周报"),
    MONTH("month", "月报"),
    YEAR("year", "年报")
    ;

    private final String code;
    private final String desc;

    StatisticalPeriodEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public static String getValueByCode(String code) {
        if (StringUtils.isBlank(code)){
            return null;
        }else {
            return Arrays.stream(StatisticalPeriodEnum.values())
                    .filter(item -> item.getCode().equals(code))
                    .findFirst()
                    .map(StatisticalPeriodEnum::getDesc)
                    .orElse(null);
        }
    }

}
