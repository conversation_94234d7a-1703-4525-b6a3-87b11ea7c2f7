package com.iflytek.cdc.admin.mapper.province;

import com.iflytek.cdc.admin.entity.mr.TbCdcmrDiseaseManifestRule;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbCdcmrDiseaseManifestRuleMapper {

    int updateByDiseaseManifestId(@Param("diseaseManifestId") String diseaseManifestId);

    /**
     * 通过manifestId查询数据
     *
     * @param manifestId 主键
     * @return 实例对象
     */
    List<TbCdcmrDiseaseManifestRule> queryByManifestId(@Param("manifestId") String manifestId);

    /**
     * 批量插入/更新数据
     * */
    void insertOrUpdate(List<TbCdcmrDiseaseManifestRule> manifestRules);

    int batchInsert(List<TbCdcmrDiseaseManifestRule> manifestRules);

    /**
     * 通过manifestId查询数据，不传查全部
     *
     * @param manifestId 主键
     * @return 实例对象
     */
    List<TbCdcmrDiseaseManifestRule> selectManifestRuleBy(@Param("manifestId") String manifestId);
}
