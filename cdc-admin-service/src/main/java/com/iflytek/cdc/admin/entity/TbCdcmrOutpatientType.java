package com.iflytek.cdc.admin.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * tb_cdcmr_outpatient_type
 * <AUTHOR>
@Data
public class TbCdcmrOutpatientType implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 门诊类型编码:1.发热门诊；2.肠道门诊
     */
    private String outpatientTypeCode;

    /**
     * 门诊类型名称
     */
    private String outpatientTypeName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 开启状态：0禁用1启用
     */
    private Integer status;

    /**
     * 删除标识：0未删除1删除
     */
    private Integer isDeleted;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updater;

    private static final long serialVersionUID = 1L;
}