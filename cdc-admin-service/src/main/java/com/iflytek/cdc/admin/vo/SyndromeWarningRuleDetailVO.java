package com.iflytek.cdc.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("症候群风险等级规则数量分组统计")
public class SyndromeWarningRuleDetailVO {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "病种code")
    private String diseaseCode;

    @ApiModelProperty(value = "病种name")
    private String diseaseName;

    @ApiModelProperty(value = "风险等级id")
    private String riskLevel;

    @ApiModelProperty("数量")
    private Integer riskLevelCount;

    @ApiModelProperty(value = "风险等级集合")
    private List<RiskLevelVO> riskLevelList;

    @Data
    public static class RiskLevelVO{
        @ApiModelProperty(value = "风险等级id")
        private String riskLevelId;

        @ApiModelProperty(value = "风险等级数量")
        private Integer riskLevelCount;

    }

}
