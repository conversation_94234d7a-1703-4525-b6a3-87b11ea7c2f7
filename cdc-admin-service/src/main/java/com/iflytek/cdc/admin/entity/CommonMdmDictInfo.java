package com.iflytek.cdc.admin.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/8/6 10:10
 **/
@Data
public class CommonMdmDictInfo {

    /**
     * 主键id
     */
    @ApiModelProperty(value="主键id")
    private  String  id;

    /**
     * 字典编码
     */
    @ApiModelProperty(value="字典编码")
    private  String  dictCode;

    /**
     * 字典名称
     */
    @ApiModelProperty(value = "字典名称")
    private  String  dictName;

    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private  String  isEnabled;

    /**
     * 创建人
     */
    @ApiModelProperty(value="创建人")
    private String createUser;

    /**
     * 创建时间
     */
    @ApiModelProperty(value="创建时间")
    private Date createTime;

    /**
     * 修改人
     */
    @ApiModelProperty(value="修改人")
    private String updateUser;

    /**
     * 修改时间
     */
    @ApiModelProperty(value="修改时间")
    private Date updateTime;

    /**
     * 数据同步时间
     */
    @ApiModelProperty(value="数据同步时间")
    private Date dataSyncTime;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof CommonMdmDictInfo)) {
            return false;
        }
        CommonMdmDictInfo dictInfo = (CommonMdmDictInfo) o;
        return dictCode.equals(dictInfo.dictCode);
    }

    @Override
    public int hashCode() {
        return Objects.hash(dictCode);
    }
}
