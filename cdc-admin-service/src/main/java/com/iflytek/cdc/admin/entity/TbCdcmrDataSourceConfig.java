package com.iflytek.cdc.admin.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * tb_cdcmr_data_source_config
 * <AUTHOR>
@Data
public class TbCdcmrDataSourceConfig implements Serializable {
    /**
     * 主键id
     */
     @ApiModelProperty("主键id")
    private String id;

    /**
     * 业务类别
     */
    @ApiModelProperty("业务类别:传染病infected,中毒poisoning")
    private String businessType;

    /**
     * 信号类别
     */
    @ApiModelProperty("信号类别")
    private String signalType;

    /**
     * 更新者姓名
     */
    @ApiModelProperty("更新者姓名")
    private String updateName;

    /**
     * 更新者id
     */
    @ApiModelProperty("更新者id")
    private String updateId;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 数据来源
     */
    @ApiModelProperty("数据来源:1.报卡2.病历")
    private String dataSource;

    /**
     * 病例数据延迟天数
     */
    @ApiModelProperty("病例数据延迟天数")
    private Integer medicalDelayDays;

}