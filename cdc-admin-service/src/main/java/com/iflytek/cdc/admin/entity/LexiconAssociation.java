package com.iflytek.cdc.admin.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("tb_cdcmr_lexicon_association")
public class LexiconAssociation implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId
    private String id;
    /**
     * 左方词库ID
     */
    @TableField("left_lexicon_id")
    private String LeftLexiconId;
    /**
     * 右方词库ID
     */
    @TableField("right_lexicon_id")
    private String RightLexiconId;
    /**
     * 关联目录ID
     */
    @TableField("association_type")
    private String associationType;
    /**
     * 是否关联
     */
    @TableField("is_association")
    private String IsAssociation;
    
    
}
