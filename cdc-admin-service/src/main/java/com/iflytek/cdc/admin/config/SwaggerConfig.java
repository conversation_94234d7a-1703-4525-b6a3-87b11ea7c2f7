package com.iflytek.cdc.admin.config;


import com.google.common.base.Predicates;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * <AUTHOR>
 * swagger相关配置
 */
@Configuration
@EnableSwagger2
public class  SwaggerConfig {

    @Bean
    public Docket createRestApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .select()
                .apis(Predicates.or(
                        RequestHandlerSelectors.basePackage("com.iflytek.cdc.admin.controller"),
                        RequestHandlerSelectors.basePackage("com.iflytek.cdc.admin.capacity.controller"),
                        RequestHandlerSelectors.basePackage("com.iflytek.cdc.admin.datamodel.controller"),
                        RequestHandlerSelectors.basePackage("com.iflytek.cdc.admin.customizedapp.controller"),
                        RequestHandlerSelectors.basePackage("com.iflytek.cdc.admin.workbench.controller"),
                        RequestHandlerSelectors.basePackage("com.iflytek.cdc.admin.outbound.controller"),
                        RequestHandlerSelectors.basePackage("com.iflytek.cdc.admin.expert.controller")
                        )

                )
                .paths(PathSelectors.any())
                .build();
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("CDC基础管理子系统/CDC基础管理子系统服务端接口文档")
                .description("CDC基础管理子系统/CDC基础管理子系统服务端接口详情增强显示")
                .version("1.0")
                .build();
    }
}
