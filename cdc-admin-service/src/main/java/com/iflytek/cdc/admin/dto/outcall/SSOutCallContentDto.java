package com.iflytek.cdc.admin.dto.outcall;

import lombok.Data;

/**
 * @Description 双数短信内容实体
 * <AUTHOR>
 * @Date 2022/4/21
 */
@Data
public class SSOutCallContentDto {

    /**
     * 机构Id
     */
    private String orgId;

    private String orgName;

    /**
     * 病例来源：门诊、住院
     */
    private String source;

    /**
     * 科室
     */
    private String deptId;
    private String deptName;
    /**
     * 患者姓名
     */
    private String patientName;
    /**
     * 家长姓名
     */
    private String parent;
    /**
     * 身份证号
     */
    private String idCardNum;
    /**
     * 性别
     */
    private String sex;
    /**
     * 年龄
     */
    private String age;
    /**
     * 出生日期
     */
    private String birthday;
    /**
     * 工作单位
     */
    private String company;
    /**
     * 联系电话
     */
    private String phone;
    /**
     * 联系地址
     */
    private String address;
    /**
     * 职业
     */
    private String job;
    /**
     * 病例类型
     */
    private String caseType;
    /**
     * 发病日期
     */
    private String onsetDate;
    /**
     * 诊断日期
     */
    private String diagDate;
    /**
     * 诊断内容
     */
    private String diagDetail;
    /**
     * 填报医生
     */
    private String reportDoc;
    /**
     * 填报日期
     */
    private String reportDate;
    /**
     * 备注
     */
    private String remark;

}
