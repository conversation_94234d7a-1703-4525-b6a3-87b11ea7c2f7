package com.iflytek.cdc.admin.model.dm.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 值域查询
 * */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DictValueQueryDTO {

    @ApiModelProperty(value = "值域id")
    private String id;

    @ApiModelProperty(value = "值域ids")
    private List<String> ids;

    @ApiModelProperty(value = "值域code")
    private String code;

    @ApiModelProperty(value = "值域name")
    private String name;

    @ApiModelProperty(value = "模糊搜索关键词")
    private String searchWord;

    private Integer pageIndex;

    private Integer pageSize;
}
