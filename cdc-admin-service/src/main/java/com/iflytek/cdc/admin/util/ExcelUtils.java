package com.iflytek.cdc.admin.util;

import com.iflytek.cdc.admin.annotation.ExcelColumn;
import com.iflytek.cdc.admin.annotation.ExcelColumnName;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.UploadResultVO;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.CharUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.beans.FeatureDescriptor;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.io.ByteArrayOutputStream;
import java.io.Closeable;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public final class ExcelUtils {

    private static final String EXCEL2003 = "xls";

    private static final String EXCEL2007 = "xlsx";

    private static final String POSTFIX_XLS = "^.+\\.(?i)(xls)$";

    private static final String POSTFIX_XLSX = "^.+\\.(?i)(xlsx)$";

    private static final String PATIENT_FIELD_NAME = "patientName";

    private ExcelUtils() {
    }

    public static ByteArrayOutputStream generateOutputStream(Workbook workbook) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            workbook.write(outputStream);
        } catch (IOException e) {
            log.error("文件处理失败:", e);
            throw new MedicalBusinessException("11451108", "文件处理失败");
        } finally {
            //关闭流
            try {
                outputStream.close();
            } catch (IOException e) {
                log.error("关闭输出流失败:", e);
            }

            try {
                workbook.close();
            } catch (IOException e) {
                log.error("关闭Excel输出流失败:", e);
            }
        }
        return outputStream;
    }

    public static void createCellAndSetValue(Row row, String value, int index) {
        Cell cell = row.createCell(index);
        if (StringUtils.isBlank(value)) {
            cell.setCellValue("--");
        } else {
            cell.setCellValue(value);
        }
    }
    public static int getIndex(List<String> stringList, String queryString) {
        return stringList.indexOf(queryString) + 1;
    }

    public static String getStringValue(Cell cell) {

        if (cell == null) {
            return null;
        }

        switch (cell.getCellType()) {
            case STRING:  //字符串类型
                return cell.getStringCellValue();
            case BOOLEAN:   //布尔类型
                return String.valueOf(cell.getBooleanCellValue());
            case NUMERIC:  //数值类型
                return String.format("%.0f", cell.getNumericCellValue());
            default:
                return null;
        }

    }
    /**
     * 读入excel表格
     *
     * @param clazz 类
     * @param file  文件
     * @param <T>   类类型
     * @return 输出
     */
    public static <T> List<T> readExcel(Class<T> clazz, MultipartFile file) throws IOException {
        String fileName = file.getOriginalFilename();
        if (StringUtils.isEmpty(fileName)) {
            fileName = file.getName();
        }
        return readExcel(clazz, fileName, file.getInputStream());
    }

    public static <T> List<T> readDesignatedExcel(Class<T> clazz, MultipartFile file) throws IOException {
        String fileName = file.getOriginalFilename();
        if (StringUtils.isEmpty(fileName)) {
            fileName = file.getName();
        }
        return readDesignatedExcel(clazz, fileName, file.getInputStream());
    }

    public static <T> UploadResultVO getUploadResult(Class<T> clazz, MultipartFile file) throws IOException {
        String fileName = file.getOriginalFilename();
        if (StringUtils.isEmpty(fileName)) {
            fileName = file.getName();
        }
        return getUploadResult(clazz, fileName, file.getInputStream());
    }

    public static int getExcelRowNum(String fileName, InputStream inputStream) {
        int result;
        try (Workbook workbook = createWorkbook(fileName, inputStream)) {
            Sheet sheet = workbook.getSheetAt(0);
            result = sheet.getLastRowNum();
        } catch (Exception e) {
            log.error("解析 Excel 时发生异常", e);
            throw new MedicalBusinessException(e.getMessage());
        }
        return result;
    }

    /**
     * 读入excel表格
     *
     * @param clazz    类
     * @param fileName 文件
     * @param <T>      类类型
     * @return 输出
     */
    public static <T> List<T> readExcel(Class<T> clazz, String fileName, InputStream inputStream) {
        List<T> results = new ArrayList<>();

        try (Workbook workbook = createWorkbook(fileName, inputStream)) {

            Sheet sheet = workbook.getSheetAt(0);
            final int headerRowIndex = sheet.getFirstRowNum();
            // 读取头部行
            List<DataHeader> dataHeaders = readDataHeader(clazz, sheet.getRow(headerRowIndex));
            // 读取数据行
            for (int i = headerRowIndex + 1, end = sheet.getLastRowNum(); i <= end; i++) {
                Row row = sheet.getRow(i);
                // 跳过空白行
                if (isBlankRow(row)) {
                    continue;
                }
                readDataRow(clazz, results, dataHeaders, row);
            }
        } catch (Exception e) {
            log.error("解析 Excel 时发生异常", e);
            throw new MedicalBusinessException(e.getMessage());
        }
        return results;
    }

    /**
     * 读入excel表格
     *
     * @param clazz    类
     * @param fileName 文件
     * @param <T>      类类型
     * @return 输出
     */
    public static <T> List<T> readDesignatedExcel(Class<T> clazz, String fileName, InputStream inputStream) {
        List<T> results = new ArrayList<>();

        try (Workbook workbook = createWorkbook(fileName, inputStream)) {

            Sheet sheet = workbook.getSheetAt(0);
            final int headerRowIndex = sheet.getFirstRowNum();
            // 读取头部行
            List<DataHeader> dataHeaders = readDataHeader(clazz, sheet.getRow(headerRowIndex));
            // 读取数据行
            for (int i = headerRowIndex + 1, end = sheet.getLastRowNum(); i <= end; i++) {
                Row row = sheet.getRow(i);
                // 跳过空白行
                if (isBlankRow(row)) {
                    continue;
                }
                readDesignatedDataRow(clazz, results, dataHeaders, row);
            }
        } catch (Exception e) {
            log.error("解析 Excel 时发生异常", e);
            throw new MedicalBusinessException(e.getMessage());
        }
        return results;
    }

    /**
     * 读入excel表格
     *
     * @param clazz    类
     * @param fileName 文件
     * @param <T>      类类型
     * @return 输出
     */
    public static <T> UploadResultVO getUploadResult(Class<T> clazz, String fileName, InputStream inputStream) {
        UploadResultVO uploadResultVO = new UploadResultVO();
        int successCount = 0;
        int failedCount = 0;
        int totalCount = 0;

        try (Workbook workbook = createWorkbook(fileName, inputStream)) {

            Sheet sheet = workbook.getSheetAt(0);
            final int headerRowIndex = sheet.getFirstRowNum();
            // 读取头部行
            List<DataHeader> dataHeaders = readDataHeader(clazz, sheet.getRow(headerRowIndex));
            // 读取数据行
            for (int i = headerRowIndex + 1, end = sheet.getLastRowNum(); i <= end; i++) {
                Row row = sheet.getRow(i);
                // 跳过空白行
                if (isBlankRow(row)) {
                    continue;
                }
                totalCount++;
                if (checkValidRow(clazz, dataHeaders, row)) {
                    successCount++;
                } else {
                    failedCount++;
                }
            }
            uploadResultVO.setTotalCount(totalCount);
            uploadResultVO.setSuccessCount(successCount);
            uploadResultVO.setFailedCount(failedCount);
        } catch (Exception e) {
            log.error("解析 Excel 时发生异常", e);
            throw new MedicalBusinessException(e.getMessage());
        }
        return uploadResultVO;
    }


    private static Workbook createWorkbook(String fileName, InputStream is) throws IOException {
        try {
            fileName = fileName.toLowerCase();
            if (!fileName.matches(POSTFIX_XLS) && !fileName.matches(POSTFIX_XLSX)) {
                log.error("上传文件格式不正确");
            }
            Workbook workbook = null;
            if (fileName.endsWith(EXCEL2007)) {
                workbook = new XSSFWorkbook(is);
            }
            if (fileName.endsWith(EXCEL2003)) {
                workbook = new HSSFWorkbook(is);
            }
            if (workbook == null) {
                throw new MedicalBusinessException("11459001", "Excel 文件读取失败");
            }
            return workbook;
        } catch (Throwable e) {
            throw new MedicalBusinessException("文件内容过大");
        }

    }


    private static <T> List<DataHeader> readDataHeader(Class<T> clazz, Row row) {
        // Excel列对应的模板类属性信息
        Map<String, List<PropertyMeta>> propertyMetaMap = loadClassProperties(clazz).stream()
                .collect(Collectors.groupingBy(PropertyMeta::getColumnName));

        final int columnStartIndex = row.getFirstCellNum();
        final int columnEndIndex = row.getLastCellNum();
        List<DataHeader> headers = new ArrayList<>(columnEndIndex - columnStartIndex + 1);
        // 按列顺序记录列头信息
        for (int i = columnStartIndex; i <= columnEndIndex; i++) {
            Cell cell = row.getCell(i);
            // 列名
            String columnName = getCellValue(cell);
            if (!propertyMetaMap.containsKey(columnName)) {
                continue;
            }
            DataHeader header = new DataHeader();
            header.setColumnName(columnName);
            header.setProperties(propertyMetaMap.get(columnName));
            headers.add(header);
        }
        if (propertyMetaMap.size() != headers.size()) {
            throw new MedicalBusinessException("11459002", "导入的表格与模板不符,请检查!");
        }
        return headers;
    }

    private static <T> void readDataRow(Class<T> clazz, List<T> results, List<DataHeader> dataHeaders, Row row) {
        try {
            T t = clazz.getDeclaredConstructor().newInstance();
            // 遍历列
            final int start = 0;
            for (int i = start, end = row.getLastCellNum(), headerSize = dataHeaders.size(); i <= end; i++) {
                int offset = i - start;
                // 超过列头定义范围则停止解析
                if (offset >= headerSize) {
                    break;
                }
                DataHeader header = dataHeaders.get(offset);
                String fieldValue = getCellValue(row.getCell(i));
                for (PropertyMeta propertyMeta : header.getProperties()) {
                    // 值校验
                    checkField(propertyMeta, fieldValue, results);
                    // 值处理
                    fieldValue = processField(propertyMeta, fieldValue);

                    if (StringUtils.isBlank(fieldValue)) {
                        continue;
                    }
                    handleField(t, fieldValue, propertyMeta);
                }
            }
            results.add(t);
        } catch (Exception e) {
            throw new MedicalBusinessException("11459003", String.format("第[%s]行校验失败：%s", row.getRowNum(), e.getMessage()));
        }
    }

    private static <T> void readDesignatedDataRow(Class<T> clazz, List<T> results, List<DataHeader> dataHeaders, Row row) {
        try {
            T t = clazz.getDeclaredConstructor().newInstance();
            // 遍历列
            final int start = 0;
            for (int i = start, end = row.getLastCellNum(), headerSize = dataHeaders.size(); i <= end; i++) {
                int offset = i - start;
                // 超过列头定义范围则停止解析
                if (offset >= headerSize) {
                    break;
                }
                DataHeader header = dataHeaders.get(offset);
                String fieldValue = getCellValue(row.getCell(i));
                for (PropertyMeta propertyMeta : header.getProperties()) {
                    if (checkRequiredField(propertyMeta)) {
                        handleField(t, fieldValue, propertyMeta);
                    }
                }
            }
            results.add(t);
        } catch (Exception e) {
            throw new MedicalBusinessException("11459003", String.format("第[%s]行校验失败：%s", row.getRowNum(), e.getMessage()));
        }
    }

    private static <T> boolean checkValidRow(Class<T> clazz, List<DataHeader> dataHeaders, Row row) {

        try {
            T t = clazz.getDeclaredConstructor().newInstance();
            // 遍历列
            final int start = 0;
            for (int i = start, end = row.getLastCellNum(), headerSize = dataHeaders.size(); i <= end; i++) {
                int offset = i - start;
                // 超过列头定义范围则停止解析
                if (offset >= headerSize) {
                    break;
                }
                DataHeader header = dataHeaders.get(offset);
                String fieldValue = getCellValue(row.getCell(i));
                for (PropertyMeta propertyMeta : header.getProperties()) {
                    if (checkRequiredField(propertyMeta)) {
                        if (StringUtils.isEmpty(fieldValue)) {
                            return false;
                        }
                    }
                }
            }
        } catch (Exception e) {
            throw new MedicalBusinessException("11459003", String.format("第[%s]行校验失败：%s", row.getRowNum(), e.getMessage()));
        }
        return true;
    }

    private static boolean isBlankRow(Row row) {
        for (int i = row.getFirstCellNum(), end = row.getLastCellNum(); i <= end; i++) {
            if (StringUtils.isNotBlank(getCellValue(row.getCell(i)))) {
                return false;
            }
        }
        return true;
    }

    private static String processField(PropertyMeta propertyMeta, String fieldValue) {
        if (fieldValue != null) {
            ExcelColumn excelColumn = propertyMeta.getExcelColumn();
            if (excelColumn != null) {
                String[] stripValues = excelColumn.strip();
                if (stripValues != null) {
                    for (String stripValue : stripValues) {
                        fieldValue = StringUtils.strip(fieldValue, stripValue);
                    }
                }
            }
        }

        return fieldValue;
    }

    private static <T> void checkField(PropertyMeta propertyMeta, String fieldValue, List<T> results)
            throws InvocationTargetException, IllegalAccessException {
        ExcelColumn excelColumn = propertyMeta.getExcelColumn();
        String colName = excelColumn.name();
        // 非空
        if (!excelColumn.blank() && StringUtils.isBlank(fieldValue)) {
            throw new MedicalBusinessException("11459004", String.format("[%s]列值不允许为空，请检查!", colName));
        }
        // 长度
        checkFieldLength(fieldValue, excelColumn, colName);
        // 正则
        String pattern = excelColumn.pattern();
        if (!"*".equals(pattern) && !fieldValue.matches(pattern)) {
            throw new MedicalBusinessException("11459005", String.format("[%s]列值不符合正则约束(%s),请检查!", colName, pattern));
        }
        // 唯一
        if (excelColumn.unique()) {
            for (T result : results) {
                Object resultValue = propertyMeta.getPropertyDescriptor().getReadMethod().invoke(result);
                if (fieldValue.equals(resultValue)) {
                    throw new MedicalBusinessException("11459006", String.format("[%s]列值重复,请检查!", colName));
                }
            }
        }
    }

    private static <T> boolean checkRequiredField(PropertyMeta propertyMeta) {
        ExcelColumn excelColumn = propertyMeta.getExcelColumn();
        // 是否要求填写
        return excelColumn.required();
    }

    private static void checkFieldLength(String fieldValue, ExcelColumn excelColumn, String colName) {
        int fieldSize = fieldValue.length();
        int maxLength = excelColumn.maxLength();
        int minLength = excelColumn.minLength();
        int limitLength = excelColumn.limitLength();
        if (limitLength <= 0 && maxLength > 0 && fieldSize > maxLength) {
            throw new MedicalBusinessException("11459007", String.format("[%s]列值超过最大长度%s,请检查!", colName, maxLength));
        }
        if (limitLength <= 0 && minLength > 0 && fieldSize < minLength) {
            throw new MedicalBusinessException("11459008", String.format("[%s]列值未及最小长度%s,请检查!", colName, minLength));
        }
        if (limitLength > 0 && limitLength != fieldSize) {
            throw new MedicalBusinessException("11459009", String.format("[%s]列值不等于限定长度%s,请检查!", colName, limitLength));
        }
    }

    private static <T> void handleField(T t, String value, PropertyMeta propertyMeta) throws InvocationTargetException,
            IllegalAccessException, NoSuchMethodException, InstantiationException {
        // 使用访问方法来写入数据
        PropertyDescriptor descriptor = propertyMeta.getPropertyDescriptor();
        Class<?> type = descriptor.getPropertyType();
        if (type == null || type == void.class) {
            return;
        }
        Method writeMethod = descriptor.getWriteMethod();
        if (type.getSuperclass() == null) {
            // 基础类型
            handlePrimitiveField(t, value, type, writeMethod);
            return;
        }
        if (type.getSuperclass() == Number.class) {
            // 数字类型
            handleNumberField(t, value, type, writeMethod);
            return;
        }
        // 其他类型
        if (type == Object.class) {
            writeMethod.invoke(t, value);

        } else if (type == Boolean.class) {
            writeMethod.invoke(t, BooleanUtils.toBoolean(value));

        } else if (type == Date.class) {
            writeMethod.invoke(t, value);

        } else if (type == String.class) {
            writeMethod.invoke(t, value);

        } else {
            Constructor<?> constructor = type.getConstructor(String.class);
            writeMethod.invoke(t, constructor.newInstance(value));
        }
    }

    public static void changeAnnotation(Class clazz) {
        try {
            Field[] fields = clazz.getFields();
            for (int i = 0; i < fields.length; i++) {
                Field field = fields[i];
                ExcelColumn annotation = field.getAnnotation(ExcelColumn.class);
                InvocationHandler invocationHandler = Proxy.getInvocationHandler(annotation);
                Field hField = invocationHandler.getClass().getDeclaredField("memberValues");
                hField.setAccessible(true);
                Map map = (Map) hField.get(invocationHandler);
                int j = i + 1;
                map.put("column", j);
            }
        } catch (Exception e) {
            log.error("处理Excel错误：{}", e);
        }
    }

    private static <T> void handlePrimitiveField(T t, String value, Class<?> type, Method writeMethod)
            throws IllegalAccessException, InvocationTargetException {
        if (type == int.class) {
            writeMethod.invoke(t, NumberUtils.toInt(value));
        } else if (type == long.class) {
            writeMethod.invoke(t, NumberUtils.toLong(value));
        } else if (type == byte.class) {
            writeMethod.invoke(t, NumberUtils.toByte(value));
        } else if (type == short.class) {
            writeMethod.invoke(t, NumberUtils.toShort(value));
        } else if (type == double.class) {
            writeMethod.invoke(t, NumberUtils.toDouble(value));
        } else if (type == float.class) {
            writeMethod.invoke(t, NumberUtils.toFloat(value));
        } else if (type == char.class) {
            writeMethod.invoke(t, CharUtils.toChar(value));
        } else if (type == boolean.class) {
            writeMethod.invoke(t, BooleanUtils.toBoolean(value));
        }
    }

    private static <T> void handleNumberField(T t, String value, Class<?> type, Method writeMethod)
            throws IllegalAccessException, InvocationTargetException {
        if (type == Integer.class) {
            writeMethod.invoke(t, NumberUtils.toInt(value));
        } else if (type == Long.class) {
            writeMethod.invoke(t, NumberUtils.toLong(value));
        } else if (type == Byte.class) {
            writeMethod.invoke(t, NumberUtils.toByte(value));
        } else if (type == Short.class) {
            writeMethod.invoke(t, NumberUtils.toShort(value));
        } else if (type == Double.class) {
            writeMethod.invoke(t, NumberUtils.toDouble(value));
        } else if (type == Float.class) {
            writeMethod.invoke(t, NumberUtils.toFloat(value));
        } else if (type == Character.class) {
            writeMethod.invoke(t, CharUtils.toChar(value));
        } else if (type == BigDecimal.class) {
            writeMethod.invoke(t, new BigDecimal(value));
        }
    }

    /**
     * 获取单元格数据，不会返回 null
     *
     * @param cell 单元格
     * @return 数据
     */
    private static String getCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }
        switch (cell.getCellType()) {
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return DateUtil.getJavaDate(cell.getNumericCellValue()).toString();
                } else {
                    return BigDecimal.valueOf(cell.getNumericCellValue()).toString();
                }
            case STRING:
                return StringUtils.trimToEmpty(cell.getStringCellValue());
            case FORMULA:
                return StringUtils.trimToEmpty(cell.getCellFormula());
            case BLANK:
                return "";
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case ERROR:
                return "ERROR";
            default:
                return cell.toString().trim();
        }
    }

    /**
     * 根据数据库查询结果转换成文件流
     * @param dataList 外呼结果集
     * @param c 结果集CLASS类型
     * @param returnHeaderOnceNoData 如果没有数据时，是否输出文件头
     * @return 文件流
     */
    public static <T> ResponseEntity<byte[]> exportExcel(List<T> dataList, Class<T> c, boolean returnHeaderOnceNoData, String fileName) {

        return exportExcelByFormConfig(dataList, c, returnHeaderOnceNoData, fileName, ExcelUtils.getPropertyMetaListByConfigDesc(c, null));
    }

    /**
     * 根据数据库查询结果转换成文件流
     * @param dataList 外呼结果集
     * @param c 结果集CLASS类型
     * @param returnHeaderOnceNoData 如果没有数据时，是否输出文件头
     * @param propertyMetaList 导出表格的表头
     * @return 文件流
     */
    public static <T> ResponseEntity<byte[]> exportExcelByFormConfig(List<T> dataList, Class<T> c, boolean returnHeaderOnceNoData, String fileName, List<ExcelUtils.PropertyMeta> propertyMetaList) {
        ByteArrayOutputStream outputStream;
        if (returnHeaderOnceNoData || org.apache.commons.collections4.CollectionUtils.isNotEmpty(dataList)) {
            //解析数据为excel格式，返回流
            outputStream = ExcelUtils.writeExcelStream(dataList, c, propertyMetaList);
            HttpHeaders httpHeaders = FileUtils.getHttpHeaders(fileName);
            return new ResponseEntity<>(outputStream.toByteArray(), httpHeaders, HttpStatus.CREATED);
        }
        return new ResponseEntity<>(null, null, HttpStatus.NO_CONTENT);
    }


    public static <T> Workbook writeExcel(List<T> dataList, Class<T> clazz, List<PropertyMeta> propertyMetaList) {

        Workbook wb = new SXSSFWorkbook();
        CellStyle cellStyle = wb.createCellStyle();
        Sheet sheet = wb.createSheet("Sheet1");
        AtomicInteger rowIndex = new AtomicInteger();
        // 初始化表头
        writeDataHeader(propertyMetaList, wb, sheet, rowIndex);
        // 写入数据
        writeDataRows(dataList, propertyMetaList, sheet, rowIndex, cellStyle);
        //冻结窗格
        wb.getSheet("Sheet1").createFreezePane(0, 1, 0, 1);

        return wb;
    }

    public static <T> ByteArrayOutputStream writeExcelStream(List<T> list, Class<T> clazz, List<PropertyMeta> propertyMetaList) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        Workbook workbook = writeExcel(list, clazz, propertyMetaList);
        try {
            workbook.write(outputStream);
        } catch (IOException e) {
            log.error("excel工作本写入流失败:", e);
            throw new MedicalBusinessException("11459010", "excel工作本写入流失败");
        } finally {
            //关闭流
            try {
                outputStream.close();
            } catch (IOException e) {
                log.error("关闭输出流失败:", e);
            }

            try {
                workbook.close();
            } catch (IOException e) {
                log.error("关闭Excel输出流失败:", e);
            }
        }
        return outputStream;
    }

    private static void writeDataHeader(List<PropertyMeta> properties, Workbook wb, Sheet sheet,
                                        AtomicInteger rowIndex) {
        Row row = sheet.createRow(rowIndex.getAndIncrement());
        AtomicInteger columnIndex = new AtomicInteger();

        CellStyle cellStyle = wb.createCellStyle();
//        cellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
//        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
//        cellStyle.setAlignment(HorizontalAlignment.CENTER);
//
//        Font font = wb.createFont();
//        font.setBold(true);
//        cellStyle.setFont(font);

        // 写入头部
        properties.forEach(propertyMeta -> {
            Cell cell = row.createCell(columnIndex.getAndIncrement());
            cell.setCellStyle(cellStyle);
            cell.setCellValue(propertyMeta.getColumnName());
        });
    }

    private static <T> void writeDataRows(List<T> data, List<PropertyMeta> properties, Sheet sheet,
                                          AtomicInteger rowIndex) {
        if (data == null || data.isEmpty()) {
            return;
        }
        AtomicInteger columnIndex = new AtomicInteger();
        data.forEach(t -> {
            Row row1 = sheet.createRow(rowIndex.getAndIncrement());
            properties.forEach(propertyMeta -> {
                Object value = "";
                try {
                    value = propertyMeta.getPropertyDescriptor().getReadMethod().invoke(t);
                } catch (Exception e) {
                    log.info(e.getMessage());
                }
                Cell cell = row1.createCell(columnIndex.getAndIncrement());
                if (value != null) {
                    cell.setCellValue(value.toString());
                }
            });
            columnIndex.set(0);
        });
    }

    private static <T> void writeDataRows(List<T> data, List<PropertyMeta> properties, Sheet sheet,
                                          AtomicInteger rowIndex, CellStyle style) {
        if (data == null || data.isEmpty()) {
            return;
        }
        AtomicInteger columnIndex = new AtomicInteger();
        data.forEach(t -> {
            Row row1 = sheet.createRow(rowIndex.getAndIncrement());
            properties.forEach(propertyMeta -> {
                Object value = "";
                try {
                    value = propertyMeta.getPropertyDescriptor().getReadMethod().invoke(t);
                } catch (Exception e) {
                    log.info(e.getMessage());
                }
                Cell cell = row1.createCell(columnIndex.getAndIncrement());
                if (value != null) {
                    cell.setCellValue(value.toString());

                    if (value.equals(Constants.EXCEL_DATA_FAIL_REASON)) {
                        style.setFillForegroundColor(IndexedColors.YELLOW.index);
                        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                        cell.setCellStyle(style);
                    }
                }

            });
            columnIndex.set(0);
        });
    }

    private static <T> List<PropertyMeta> loadClassAllProperties(Class<T> clazz) {
        List<PropertyMeta> propertyMetaList = new ArrayList<>();
        try {
            // 所有模板类属性的访问对象
            Map<String, PropertyDescriptor> properties = Arrays.stream(
                    Introspector.getBeanInfo(clazz).getPropertyDescriptors())
                    .collect(Collectors.toMap(FeatureDescriptor::getName, Function.identity()));
            // Excel列对应的模板类属性信息
            for (Field field : clazz.getFields()) {
                ExcelColumn annotation = field.getAnnotation(ExcelColumn.class);
                if (annotation == null || annotation.name().length() == 0) {
                    continue;
                }
                String propertyName = field.getName();
                PropertyDescriptor descriptor = properties.get(propertyName);
                if (descriptor == null) {
                    throw new MedicalBusinessException("11459011", "模板类属性 [" + propertyName + "] 未定义访问方法");
                }
                PropertyMeta meta = new PropertyMeta();
                meta.setExcelColumn(annotation);
                meta.setPropertyName(propertyName);
                meta.setPropertyDescriptor(descriptor);

                propertyMetaList.add(meta);
            }
        } catch (IntrospectionException e) {
            throw new MedicalBusinessException("11459012", "解析模板类失败，" + e.getMessage());
        }
        return propertyMetaList;
    }

    public static <T> Workbook writeExcel2(List<T> dataList, Class<T> clazz) {
        // 属性列表
        List<PropertyMeta> propertyMetaList = loadClassAllProperties(clazz).stream()
                .filter(PropertyMeta::isIndexed)
                .sorted(Comparator.comparingInt(PropertyMeta::getColumnIndex))
                .collect(Collectors.toList());

        Workbook wb = new XSSFWorkbook();
        Sheet sheet = wb.createSheet("Sheet1");
        AtomicInteger rowIndex = new AtomicInteger();
        // 初始化表头
        writeDataHeader(propertyMetaList, wb, sheet, rowIndex);
        // 写入数据
        writeDataRows(dataList, propertyMetaList, wb, sheet, rowIndex);
        //冻结窗格
        wb.getSheet("Sheet1").createFreezePane(0, 1, 0, 1);

        return wb;
    }

    private static <T> void writeDataRows(List<T> data, List<PropertyMeta> properties, Workbook wb, Sheet sheet, AtomicInteger rowIndex) {
        if (data == null || data.isEmpty()) {
            return;
        }

        data.forEach(t -> {
            Row row1 = sheet.createRow(rowIndex.getAndIncrement());
            AtomicInteger columnIndex = new AtomicInteger();
            CellStyle cellStyle = wb.createCellStyle();
            DataFormat df = wb.createDataFormat();
            properties.forEach(propertyMeta -> {
                Object value = "";
                try {
                    value = propertyMeta.getPropertyDescriptor().getReadMethod().invoke(t);
                } catch (Exception e) {
                    log.info(e.getMessage());
                }
                Cell cell = row1.createCell(columnIndex.getAndIncrement());
                if (value != null) {
                    //判断data是否为数值型
                    boolean isNum = value.toString().matches("^(-?\\d+)(\\.\\d+)?$");
                    //判断data是否为整数（小数部分是否为0）
                    boolean isInteger = value.toString().matches("^[-\\+]?[\\d]*$");
                    //判断data是否为百分数（是否包含“%”）
                    boolean isPercent = value.toString().contains("%");

                    //如果单元格内容是数值类型，涉及到金钱（金额、本、利），则设置cell的类型为数值型，设置data的类型为数值类型
                    if (isNum && !isPercent) {
                        if (isInteger) {
                            cellStyle.setDataFormat(df.getFormat("#,#0"));//数据格式只显示整数
                        } else {
                            cellStyle.setDataFormat(df.getFormat("#,##0.00"));//保留两位小数点
                        }
                        // 设置单元格格式
                        cell.setCellStyle(cellStyle);
                        // 设置单元格内容为double类型
                        cell.setCellValue(Double.parseDouble(value.toString()));
                    } else {
                        cell.setCellStyle(cellStyle);
                        // 设置单元格内容为字符型
                        cell.setCellValue(value.toString());
                    }
                }
            });
        });
    }

    private static <T> List<PropertyMeta> loadClassProperties(Class<T> clazz) {
        List<PropertyMeta> propertyMetaList = new ArrayList<>();
        try {
            // 所有模板类属性的访问对象
            Map<String, PropertyDescriptor> properties = Arrays.stream(Introspector.getBeanInfo(clazz).getPropertyDescriptors())
                                                               .collect(Collectors.toMap(FeatureDescriptor::getName, Function.identity()));
            // Excel列对应的模板类属性信息
            for (Field field : clazz.getDeclaredFields()) {
                ExcelColumn annotation = field.getAnnotation(ExcelColumn.class);
                if (annotation == null || annotation.name().length() == 0) {
                    continue;
                }
                String propertyName = field.getName();
                PropertyDescriptor descriptor = properties.get(propertyName);
                if (descriptor == null) {
                    throw new MedicalBusinessException("11459011", "模板类属性 [" + propertyName + "] 未定义访问方法");
                }
                PropertyMeta meta = new PropertyMeta();
                meta.setExcelColumn(annotation);
                meta.setPropertyName(propertyName);
                meta.setPropertyDescriptor(descriptor);

                propertyMetaList.add(meta);
            }
        } catch (IntrospectionException e) {
            throw new MedicalBusinessException("11459012", "解析模板类失败，" + e.getMessage());
        }
        return propertyMetaList;
    }

    private static <T> List<PropertyMeta> loadClassPropertiesByConfigList(Class<T> clazz, List<String> configList) {
        List<PropertyMeta> propertyMetaList = new ArrayList<>();
        try {
            // 所有模板类属性的访问对象
            Map<String, PropertyDescriptor> properties = Arrays.stream(Introspector.getBeanInfo(clazz).getPropertyDescriptors())
                                                               .collect(Collectors.toMap(FeatureDescriptor::getName, Function.identity()));

            configList.forEach(config -> {
                // Excel列对应的模板类属性信息
                for (Field field : clazz.getDeclaredFields()) {
                    ExcelColumn annotation = field.getAnnotation(ExcelColumn.class);
                    if (annotation == null || annotation.name().length() == 0) {
                        continue;
                    }
                    String propertyName = field.getName();
                    if(Objects.equals(config, propertyName)){
                        PropertyDescriptor descriptor = properties.get(propertyName);
                        if (descriptor == null) {
                            throw new MedicalBusinessException("11459011", "模板类属性 [" + propertyName + "] 未定义访问方法");
                        }
                        PropertyMeta meta = new PropertyMeta();
                        meta.setExcelColumn(annotation);
                        meta.setPropertyName(propertyName);
                        meta.setPropertyDescriptor(descriptor);

                        propertyMetaList.add(meta);
                    }
                }
            });
        } catch (IntrospectionException e) {
            throw new MedicalBusinessException("11459012", "解析模板类失败，" + e.getMessage());
        }
        return propertyMetaList;
    }

    /**
     * 导出病例列表时，补充姓名字段
     * */
    public static <T> PropertyMeta getPropertyMetaByFieldName(Class<T> clazz, String fieldName){

        try{
            Map<String, PropertyDescriptor> properties = Arrays.stream(Introspector.getBeanInfo(clazz).getPropertyDescriptors())
                                                               .collect(Collectors.toMap(FeatureDescriptor::getName, Function.identity()));
            for (Field field : clazz.getDeclaredFields()) {
                ExcelColumn annotation = field.getAnnotation(ExcelColumn.class);
                if (annotation == null || annotation.name().length() == 0) {
                    continue;
                }
                String propertyName = field.getName();
                if(Objects.equals(fieldName, propertyName)){
                    PropertyDescriptor descriptor = properties.get(propertyName);
                    if (descriptor == null) {
                        throw new MedicalBusinessException("11459011", "模板类属性 [" + propertyName + "] 未定义访问方法");
                    }
                    PropertyMeta meta = new PropertyMeta();
                    meta.setExcelColumn(annotation);
                    meta.setPropertyName(propertyName);
                    meta.setPropertyDescriptor(descriptor);
                    return meta;
                }
            }
        }catch (IntrospectionException e) {
            throw new MedicalBusinessException("11459012", "解析模板类失败，" + e.getMessage());
        }
        return null;
    }

    /**
     * 通过用户配置的病例列表的表头字段以及顺序  获取导出表头的样式
     * */
    public static <T> List<PropertyMeta> getPropertyMetaListByConfigDesc(Class<T> clazz, String configDesc){

        // 属性列表
        List<PropertyMeta> propertyMetaList;
        if(configDesc == null){
            propertyMetaList = loadClassProperties(clazz).stream()
                                                         .filter(PropertyMeta::isIndexed)
                                                         .sorted(Comparator.comparingInt(PropertyMeta::getColumnIndex))
                                                         .collect(Collectors.toList());
        }else{
            List<String> configList = Arrays.asList(configDesc.split(","));
            propertyMetaList = loadClassPropertiesByConfigList(clazz, configList).stream()
                                                                                 .filter(PropertyMeta::isIndexed)
                                                                                 .collect(Collectors.toList());
            //补充病例列表姓名字段
            propertyMetaList.add(0, getPropertyMetaByFieldName(clazz, PATIENT_FIELD_NAME));
        }
        return propertyMetaList;
    }

    public static <T> BigWriter<T> newBigWriter(Class<T> clazz) {
        return new BigWriter<>(clazz);
    }

    @Data
    private static class DataHeader {

        /**
         * 列名
         */
        private String columnName;

        /**
         * 列规则定义
         */
        private List<PropertyMeta> properties;
    }

    @Data
    public static class PropertyMeta {

        /**
         * 列规则定义
         */
        private ExcelColumn excelColumn;

        /**
         * 属性名
         */
        private String propertyName;

        /**
         * 属性访问对象
         */
        private PropertyDescriptor propertyDescriptor;

        public String getColumnName() {
            return excelColumn.name();
        }

        public int getColumnIndex() {
            return excelColumn.column();
        }

        public boolean isIndexed() {
            return getColumnIndex() > 0;
        }
    }

    public static class BigWriter<E> implements Closeable {

        private final List<PropertyMeta> propertyMetaList;
        AtomicInteger currentRow = new AtomicInteger(0);
        private boolean isClosed;
        private boolean isFlushed;
        private SXSSFWorkbook workbook;
        private Sheet sheet;

        public BigWriter(Class<E> clazz) {
            // 属性列表
            propertyMetaList = loadClassProperties(clazz).stream()
                                                         .filter(PropertyMeta::isIndexed)
                                                         .sorted(Comparator.comparingInt(PropertyMeta::getColumnIndex))
                                                         .collect(Collectors.toList());

            workbook = new SXSSFWorkbook();
            sheet = workbook.createSheet("Sheet1");
            // 初始化表头
            writeDataHeader(propertyMetaList, workbook, sheet, currentRow);
        }

        /**
         * @return 数据行数
         */
        public Integer getRowCount() {
            return currentRow.get();
        }

        public void append(List<E> data) {
            // 写入数据
            writeDataRows(data, propertyMetaList, sheet, currentRow);
        }

        public void flush(OutputStream out) throws IOException {
            if (isClosed) {
                throw new MedicalBusinessException("11459013", "流已关闭");
            }
            if (isFlushed) {
                return;
            }
            workbook.write(out);
            isFlushed = true;
        }

        @Override
        public void close() throws IOException {
            workbook.dispose();
            workbook.close();
            this.sheet = null;
            this.workbook = null;
            isClosed = true;
        }
    }






    public  static  <T> void  excelExport(List<T> exportContent, HttpServletResponse response, InputStream is, String... headValue ){
        response.setContentType("multipart/form-data");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-Disposition", "attachment; filename=" + Constants.FAIL_EXCEL_NAME);
        //创建Excel薄
        XSSFWorkbook   wb=null;
        OutputStream out = null;
        try {
            if(is !=null){
                wb =new XSSFWorkbook(is);
            }else{
                wb=new XSSFWorkbook();
            }
            //创建sheet页
            XSSFSheet sheet=wb.createSheet();
            //固定行高度
            sheet.setDefaultRowHeight((short) (2 * 256));
            //设置字体样式
            XSSFFont font=wb.createFont();
            font.setFontName("微软雅黑");
            font.setFontHeightInPoints((short) 16);
            //标题样式
            XSSFCellStyle titleStyle = wb.createCellStyle();
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            Font ztFont = wb.createFont();
            // 设置字体不是斜体字
            ztFont.setItalic(false);
            // 设置字体颜色
            ztFont.setColor(Font.COLOR_NORMAL);
            // 将字体大小设置为16px
            ztFont.setFontHeightInPoints((short) 16);
            // 将"宋体"字体应用到当前单元格上
            ztFont.setFontName("宋体");
            //加粗
            ztFont.setBold(true);
            titleStyle.setFont(ztFont);
            //默认获取表头信息
            List<Map<String,Object>> title=new ArrayList<>();
            List<String> fieldName=new ArrayList<>();
            //获取表头内容
            extracted(exportContent, title, fieldName);
            int rowNum = getRowNum(sheet, titleStyle, title, fieldName, headValue);
            //填充表格内容
            extracted(exportContent, sheet, fieldName, rowNum);
            //设置单元格长度根据表格内容扩充
            setColumnSize(sheet,title.size()+1,rowNum);
            out=response.getOutputStream();
            wb.write(out);
            out.flush();
        }catch (Exception e){
            log.error("导出excel异常：{}",e);
            throw new MedicalBusinessException("文件导出异常");
        }finally {
            try {
                wb.close();
                out.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


    /**
     * 根据注解获取表头内容
     * <AUTHOR>
     * @date 10:13 2021/6/7
     * @return
     **/
    private static  <T>  void extracted(List<T> exportContent, List<Map<String, Object>> title, List<String> fieldName) {
        if(!CollectionUtils.isEmpty(exportContent)){
            T t= (T) exportContent.get(0);
            //获取私有化属性
            Field[] fields=t.getClass().getDeclaredFields();
            for (Field  field:fields) {
                Map<String,Object>  map=new HashMap<>();
                boolean  annotationPresent= field.isAnnotationPresent(ExcelColumnName.class);
                if (annotationPresent){
                    map.put(field.getName(),field.getAnnotation(ExcelColumnName.class).name());
                    fieldName.add(field.getName());
                    title.add(map);
                }
            }
        }
    }
    /**
     * 获取表格合并标题内容
     * <AUTHOR>
     * @date 10:06 2021/6/7
     * @return
     **/
    private static int getRowNum(XSSFSheet sheet, XSSFCellStyle titleStyle, List<Map<String, Object>> title,  List<String> fieldName,String[] headValue) {
        int rowNum= headValue.length !=0 && StringUtils.isNotBlank(headValue[0])?1:0;
        //如果需要填充表格头，则合并单元格，填充数据。
        if(headValue.length !=0&& StringUtils.isNotBlank(headValue[0])){
            String headCellValue= headValue[0];
            XSSFRow headRow= sheet.createRow(0);
            XSSFCell headCell=headRow.createCell(0);
            // 合并单元格CellRangeAddress构造参数依次表示起始行，截至行，起始列， 截至列
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, title.size() - 1));
            headCell.setCellValue(headCellValue);
            headCell.setCellStyle(titleStyle);
        }
        XSSFRow firstRow = sheet.createRow(rowNum);
        for(int i = 0; i< title.size(); i++){
            XSSFCell cell=firstRow.createCell(i);
            cell.setCellValue(title.get(i).get(fieldName.get(i)).toString());
        }
        return rowNum;
    }

    /**
     *  填充表格内容
     * <AUTHOR>
     * @date 10:03 2021/6/7
     * @return
     **/
    private static <T> void extracted(List<T> exportContent, XSSFSheet sheet, List<String> fieldName, int rowNum) {
        try{
            for (int i = 0; i< exportContent.size(); i++){
                //创建行
                XSSFRow  row= sheet.createRow(i+1+ rowNum);
                //遍历表格内容
                for(int j = 0; j< fieldName.size(); j++){
                    //反射获取字段的值
                    XSSFCell cell=row.createCell(j);
                    StringBuffer  sb=new StringBuffer();
                    sb.append("get").append(fieldName.get(j).substring(0,1).toUpperCase()).append(fieldName.get(j).substring(1));
                    Method method= exportContent.get(i).getClass().getMethod(sb.toString());
                    Object  fieldValue=method.invoke(exportContent.get(i));
                    //填充内容
                    if (fieldValue !=null){
                        cell.setCellValue(fieldValue.toString());
                    }
                }
            }
        }catch (Exception e){
            log.error("获取表格内容异常",e);
            throw  new MedicalBusinessException("excel导出异常");
        }
    }

    public static void setColumnSize(XSSFSheet  sheet,int size,int  headRowNum){
        //遍历单元格
        for (int columnNum = 0; columnNum <size; columnNum++) {
            //获取当前单元格长度
            int columnWidth = sheet.getColumnWidth(columnNum) / 256;
            //遍历行
            for (int rowNum = 0; rowNum <= sheet.getLastRowNum(); rowNum++) {
                XSSFRow currentRow;
                //当前行未被使用过
                if (sheet.getRow(rowNum) == null) {
                    currentRow = sheet.createRow(rowNum);
                } else {
                    currentRow = sheet.getRow(rowNum);
                }
                if (currentRow.getCell(columnNum) != null) {
                    //回去当前单元格
                    XSSFCell currentCell = currentRow.getCell(columnNum);
                    //如果为字符类型
                    if (currentCell.getCellType() == CellType.STRING) {
                        //获取内容长度
                        int length = currentCell.getStringCellValue().getBytes().length;
                        //当有表头的合并单元格
                        if(headRowNum ==1){
                            if(columnNum ==0){
                                if (columnWidth < length && rowNum !=0) {
                                    columnWidth = length;
                                }
                            }else{
                                if (columnWidth < length) {
                                    columnWidth = length;
                                }
                            }
                        }else{
                            if (columnWidth < length) {
                                columnWidth = length;
                            }
                        }

                    }
                }
            }
            if(columnWidth>255){
                columnWidth=255;
            }
            sheet.setColumnWidth(columnNum, columnWidth * 256);
        }
    }
}
