package com.iflytek.cdc.admin.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class InfectedWarningRuleDTO {

    @ApiModelProperty(value = "01. 法定;  09.其他(包含自定义的)")
    private String infectedClassCode;


    @ApiModelProperty(value = "疾病编码")
    private List<String> diseaseCodeList;

    @ApiModelProperty(value = "预警方法")
    private String warningMethod;

    @ApiModelProperty(value = "风险分级")
    private String riskLevelId;
}
