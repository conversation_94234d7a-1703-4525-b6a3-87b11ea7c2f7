package com.iflytek.cdc.admin.mapper.province;

import com.iflytek.cdc.admin.entity.dm.TbCdcdmDataDict;
import com.iflytek.cdc.admin.model.dm.vo.DataDictListVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbCdcdmDataDictMapper {

    /**
     * 查询值域列表
     * */
    List<DataDictListVO> getDataDictList(@Param("id") String id,
                                         @Param("name") String name,
                                         @Param("attrCount") Integer attrCount);

    /**
     * 编辑值域列表
     * */
    void editValueDomain(TbCdcdmDataDict tbCdcdmDataDict);

    /**
     * 更新值域个数
     * */
    void updateDataDictCount(@Param("dictId") String dictId);

    TbCdcdmDataDict selectById(String id);

    /**
     * 记数 根据名称查询和根据ID排除
     **/
    int countByNameNorId(String name, String excludeId);

    /**
     * 根据值域名称查询值域id
     * */
    String getDictIdBy(@Param("name") String name);

}
