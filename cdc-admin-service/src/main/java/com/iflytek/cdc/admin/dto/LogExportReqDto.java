package com.iflytek.cdc.admin.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 * @date :2023/8/3 16:52
 * @description:LogExportReqDto
 */
@Data
public class LogExportReqDto {

    /**
     * 请求路径
     */
    @ApiModelProperty(value="请求路径")
    private String queryUrl;

    /**
     * 查询参数
     */
    @ApiModelProperty(value="请求参数")
    private String queryParam;

    /**
     * id
     */
    @ApiModelProperty(value="id")
    private String id;

    /**
     * 文件大小
     */
    @ApiModelProperty(value="文件大小")
    private Long fileSize;

    @ApiModelProperty(value="用户id")
    private String loginUserId;

    /**
     * 请求是否成功 1成功
     */
    @ApiModelProperty(value="请求是否成功 1成功")
    private Integer status;

}
