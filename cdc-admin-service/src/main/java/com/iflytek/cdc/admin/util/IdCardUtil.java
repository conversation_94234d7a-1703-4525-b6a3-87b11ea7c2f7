package com.iflytek.cdc.admin.util;

import org.apache.commons.lang3.StringUtils;


/**
 * @ClassName IdCardUtil
 * @Description 身份证相关工具包
 * <AUTHOR>
 * @Date 2021/6/17 9:30
 * @Version 1.0
 */
public class IdCardUtil {

    private static final int ID_CARD_PROVINCE_ACTIVE_LENGTH = 2;
    private static final int ID_CARD_CITY_ACTIVE_LENGTH = 4;

    /**
     * 将行政编码转为标准的12位
     *
     * @param administrativeCode
     * @return
     */
    private static String standardizeAdministrativeCode(String administrativeCode) {
        return StringUtils.rightPad(administrativeCode, 6, "0");
    }

    public static String getIdCardProvinceCode(String idCard) {
        if (idCard == null || "".equals(idCard) || idCard.length() < ID_CARD_PROVINCE_ACTIVE_LENGTH) {
            return "";
        }
        return IdCardUtil.standardizeAdministrativeCode(idCard.substring(0, 2));
    }

    public static String getIdCardCityCode(String idCard) {
        if (idCard == null || "".equals(idCard) || idCard.length() < ID_CARD_CITY_ACTIVE_LENGTH) {
            return "";
        }
        return IdCardUtil.standardizeAdministrativeCode(idCard.substring(0, 4));
    }

    /**
     * 使用方式样例
     *
     * @param args
     */
    private static void main(String[] args) {
        System.out.println(standardizeAdministrativeCode("34"));
    }

}
