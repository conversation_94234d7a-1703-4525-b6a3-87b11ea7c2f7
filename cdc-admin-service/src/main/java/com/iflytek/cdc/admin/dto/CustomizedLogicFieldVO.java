package com.iflytek.cdc.admin.dto;

import com.iflytek.cdc.admin.entity.TbCdcmrCustomizedLogicField;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.Objects;

@Data
public class CustomizedLogicFieldVO {
    private String warningType;
    private String name;
    private String desc;
    private String fieldType;//特殊类型
    private boolean groupFlag;
    private String dataSource;//数据来源
    private String medicalType;//病历类型
    private Object constants;//常量值
    private List<FieldSetting> fieldSettings;
    private Integer sequence;//顺序

    public static CustomizedLogicFieldVO of(TbCdcmrCustomizedLogicField logicField){
        CustomizedLogicFieldVO fieldVO = new CustomizedLogicFieldVO();
        BeanUtils.copyProperties(logicField, fieldVO);
        return fieldVO;
    }

    @Data
    public static class FieldSetting{
        private boolean columnFlag;//字段是否是数据库的列
        private String prefix;//查询前缀
        private String fieldName;//字段名
        private String splitter;//分割符，需要将String 类型的拆分
    }
}
