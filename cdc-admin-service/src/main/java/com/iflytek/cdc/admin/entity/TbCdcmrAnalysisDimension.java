package com.iflytek.cdc.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 分析维度信息表
 */
@Data
public class TbCdcmrAnalysisDimension {
    /**
     * 主键，维度的唯一标识
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 维度代码
     */
    @TableField(value = "dim_code")
    private String dimCode;

    /**
     * 维度名称
     */
    @TableField(value = "dimension_name")
    private String dimensionName;

    /**
     * 值域ID
     */
    @TableField(value = "data_attr_id")
    private String dataAttrId;

    /**
     * 维度定义
     */
    @TableField(value = "dim_definition")
    private String dimDefinition;

    /**
     * 备注
     */
    @TableField(value = "notes")
    private String notes;

    /**
     * 状态（启用状态 1启用；0未启用）
     */
    @TableField(value = "\"status\"")
    private Integer status;

    /**
     * 删除标识: 0-未删除，1-已删除
     */
    @TableField(value = "delete_flag")
    private String deleteFlag;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 创建者ID
     */
    @TableField(value = "creator_id")
    private String creatorId;

    /**
     * 创建者名称
     */
    @TableField(value = "creator")
    private String creator;

    /**
     * 更新者ID
     */
    @TableField(value = "updater_id")
    private String updaterId;

    /**
     * 更新者名称
     */
    @TableField(value = "updater")
    private String updater;

    /**
     * 序号（流水号）
     */
    @TableField(value = "seq_num")
    private Integer seqNum;


    /**
     * 指标类型
     */
    @TableField(value = "analysis_target")
    private String analysisTargetStr;
}
