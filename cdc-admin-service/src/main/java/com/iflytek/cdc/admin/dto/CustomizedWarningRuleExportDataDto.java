package com.iflytek.cdc.admin.dto;

import com.iflytek.cdc.admin.annotation.ExcelColumnName;
import lombok.Data;

@Data
public class CustomizedWarningRuleExportDataDto {
    @ExcelColumnName(name = "自定义名称")
    private String name;
    @ExcelColumnName(name = "潜伏期")
    private String incubation;
    @ExcelColumnName(name = "信号最大生命周期")
    private String maxLifeCycle;
    @ExcelColumnName(name = "监测对象")
    private String monitorObject;
    @ExcelColumnName(name = "时间范围")
    private String timeRange;
    private String timeRangeUnit;
    @ExcelColumnName(name = "病例数量")
    private String medicalCount;
    @ExcelColumnName(name = "病例属性")
    private String medicalAttribute;
    @ExcelColumnName(name = "警示类型")
    private String warnType;
}
