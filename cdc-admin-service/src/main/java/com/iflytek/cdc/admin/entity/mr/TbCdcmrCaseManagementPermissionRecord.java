package com.iflytek.cdc.admin.entity.mr;

import com.baomidou.mybatisplus.annotation.TableName;
import com.iflytek.cdc.admin.model.mr.dto.AuthCommonDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel("病例管理记录表")
@TableName(value = "tb_cdcmr_case_management_permission_record")
public class TbCdcmrCaseManagementPermissionRecord extends AuthCommonDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "权限编码")
    private String authCode;

    @ApiModelProperty(value = "权限名称")
    private String authName;

    @ApiModelProperty(value = "病例id")
    private String caseId;

    @ApiModelProperty(value = "申请原因")
    private String applyReason;

    @ApiModelProperty(value = "机构id")
    private String orgId;

    @ApiModelProperty("有效期开始时间")
    private Date validStartTime;

    @ApiModelProperty("有效期结束时间")
    private Date validEndTime;

    @ApiModelProperty(value = "状态(1审批通过;0待审批;-1审批驳回)")
    private String status;

    @ApiModelProperty(value = "拒绝原因")
    private String rejectReason;

    @ApiModelProperty(value = "删除标识")
    private String deleteFlag;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人ID")
    private String creatorId;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "更新人")
    private String updater;

    @ApiModelProperty(value = "更新人ID")
    private String updaterId;

    @ApiModelProperty(value = "模块类型")
    private String moduleType;
}