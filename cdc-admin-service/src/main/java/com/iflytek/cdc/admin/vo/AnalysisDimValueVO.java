package com.iflytek.cdc.admin.vo;

import com.iflytek.cdc.admin.constant.Constants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 分析维度详细信息
 */
@Data
@ApiModel(description = "分析维度详细信息")
public class AnalysisDimValueVO {

    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 分析维度ID
     */
    @ApiModelProperty(value = "分析维度ID", required = true)
    private String analysisDimId;

    /**
     * 分析维度值域值ID
     */
    @ApiModelProperty(value = "分析维度值域值ID", required = true)
    private String dataAttrValueId;

    /**
     * 分析标志，默认值为1
     */
    @ApiModelProperty(value = "分析标志，默认值为1", example = "1")
    private String analysisFlag = Constants.YES_USE;

//    @ApiModelProperty(value = "上卷维度值关联")
//    List<AnalysisDimRollupValueRelVO> rollupValueRel;

}