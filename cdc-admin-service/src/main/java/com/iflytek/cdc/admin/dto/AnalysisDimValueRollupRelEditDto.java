package com.iflytek.cdc.admin.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 分析维度-上卷维度值-编辑对象
 */
@Data
@ApiModel(description = "分析维度-上卷维度值-编辑对象")
public class AnalysisDimValueRollupRelEditDto {

    /**
     * 分析维度ID
     */
    @ApiModelProperty(value = "分析维度ID", required = true)
    private String analysisDimId;

    /**
     * 分析维度值ID
     */
    @ApiModelProperty(value = "分析维度值ID", required = true)
    private String dataAttrValueId;

    /**
     * 维度值上卷关联
     */
    @ApiModelProperty(value = "维度值上卷关联", required = true)
    private List<AnalysisDimValueRollupRelDto> dimValueRollupRelDtos;
}
