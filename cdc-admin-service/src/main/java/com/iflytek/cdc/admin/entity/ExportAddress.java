package com.iflytek.cdc.admin.entity;

import com.iflytek.cdc.admin.annotation.UploadExcelColumn;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/8/27 16:16
 **/
@Data
public class ExportAddress {

    @UploadExcelColumn(name = "addressName",sort = 0)
    private  String  addressName;

    @UploadExcelColumn(name = "addressDetailDesc",sort = 1)
    private  String  addressDetailDesc;

    @UploadExcelColumn(name = "level",sort = 2)
    private  String  level="40001";

    @UploadExcelColumn(name = "cityCode",sort = 3)
    private  String  cityCode;

    @UploadExcelColumn(name = "addressDistrictCode",sort = 4)
    private  String  addressDistrictCode;

    @UploadExcelColumn(name = "addressLongitude",sort = 5)
    private  String  addressLongitude;

    @UploadExcelColumn(name = "addressLatitude",sort = 6)
    private  String  addressLatitude;

    @UploadExcelColumn(name = "geoText",sort = 7)
    private  String  geoText;

    @UploadExcelColumn(name = "addressProvinceName",sort = 8)
    private  String  addressProvinceName;

    @UploadExcelColumn(name = "addressCityName",sort = 9)
    private  String  addressCityName;

    @UploadExcelColumn(name = "addressDistrictName",sort = 10)
    private  String  addressDistrictName;

    @UploadExcelColumn(name = "addressTownName",sort = 11)
    private  String  addressTownName;

    @UploadExcelColumn(name = "addressTownName",sort = 12)
    private  String  village;

    @UploadExcelColumn(name = "addressTownName",sort = 13)
    private  String  street;

    @UploadExcelColumn(name = "addressTownName",sort = 14)
    private  String  community;

    @UploadExcelColumn(name = "geoType",sort = 15)
    private  String  geoType="1";

}
