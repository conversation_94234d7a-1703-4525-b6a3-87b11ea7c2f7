package com.iflytek.cdc.admin.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
public class CdcmrCdcFictitiousOrganization implements Serializable {
    /**
    * 表id
    */
    private String id;

    /**
    * 省份代码
    */
    private String provinceCode;

    /**
    * 所属城市代码
    */
    private String cityCode;

    /**
    * 所属区县代码
    */
    private String districtCode;

    /**
    * 机构信息id(我们的虚拟节点所存的uap id)
    */
    private String orgId;

    /**
    * 机构名称
    */
    private String orgName;

    /**
    * 机构编码
    */
    private String orgCode;

    /**
    * 机构类型
    */
    private String orgType;

    /**
    * 机构类型代码
    */
    private String orgTypeCode;

    /**
     * 辅诊uap所存的机构id
     */
    private String higherOrg;

    /**
    * 创建人
    */
    private String createUser;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 修改者
    */
    private String updateUser;

    /**
    * 修改时间
    */
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}