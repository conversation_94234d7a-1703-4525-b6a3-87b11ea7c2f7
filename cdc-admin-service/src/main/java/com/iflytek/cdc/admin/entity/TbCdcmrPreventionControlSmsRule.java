package com.iflytek.cdc.admin.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * tb_cdcmr_prevention_control_sms_rule
 * <AUTHOR>
@Data
public class TbCdcmrPreventionControlSmsRule implements Serializable {
    private String id;

    private String name;

    private String userType;

    private String phone;

    private String ruleType;

    private String ruleNum;

    private String preventionControlName;

    private String preventionControlCode;

    private String provinceName;

    private String provinceCode;

    private String cityName;

    private String cityCode;

    private String districtName;

    private String districtCode;
    private String creator;

    private Date createTime;

    private String updater;

    private Date updateTime;

    private Integer deleted;

    private String loginUserId;

    private static final long serialVersionUID = 1L;
}