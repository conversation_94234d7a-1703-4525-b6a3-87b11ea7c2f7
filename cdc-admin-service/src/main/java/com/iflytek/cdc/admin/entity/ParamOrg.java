package com.iflytek.cdc.admin.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
    *
     * 机构维度参数配置关联表
     * <AUTHOR>
     * @date 14:32 2021/7/6
     * @param
     * @return
     **/
@Data
public class ParamOrg implements Serializable {
    /**
    * 主键
    */
    @ApiModelProperty(value="主键")
    private String id;

    /**
    * 配置项代码
    */
    @ApiModelProperty(value="配置项代码")
    private String configCode;

    /**
    * 机构id（all 为默认值，代表全部机构编码）
    */
    @ApiModelProperty(value="机构id")
    private String orgId;


    @ApiModelProperty(value ="机构编号" )
    private String orgCode;

    /**
    * 所属机构名称
    */
    @ApiModelProperty(value="所属机构名称")
    private String orgName;

    /**
    * 医疗卫生服务机构科室主键（all 为默认值，代表全部科室编码）
    */
    @ApiModelProperty(value="医疗卫生服务机构科室主键")
    private String deptId;

    /**
    * 医疗卫生服务机构科室名称
    */
    @ApiModelProperty(value="医疗卫生服务机构科室名称")
    private String deptName;

    /**
    * 创建人
    */
    @ApiModelProperty(value="创建人")
    private String createUser;

    /**
    * 更新人
    */
    @ApiModelProperty(value="更新人")
    private String updateUser;

    /**
    * 信息更新日期时间
    */
    @ApiModelProperty(value="信息创建日期时间")
    private Date createTime;

    /**
    * 信息创建日期时间
    */
    @ApiModelProperty(value="信息更新日期时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTime;


    @ApiModelProperty(value = "参数数量")
    private Integer paramNum;

    /**
     * 是否删除
     */
    @ApiModelProperty(value="是否删除")
    private String isDelete;

    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    @ApiModelProperty(value = "特有参数值")
    private String orgConfigValue;

}