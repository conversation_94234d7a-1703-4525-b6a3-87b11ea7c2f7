package com.iflytek.cdc.admin.enums;

/**
 * <AUTHOR>
 */

public enum StdConfigRuleTypeEnum {
    IGNORE("1", "停用词", "不使用的文本"),
    REPLACE("2", "替换词", "需要替换的文本"),
    SUBSTR("3","截取","截取文本");

    private String code;
    private String name;
    private String desc;

    private StdConfigRuleTypeEnum(String code, String name, String desc){
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }
}
