package com.iflytek.cdc.admin.config;

import io.netty.util.concurrent.ThreadPerTaskExecutor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 异步方法使用自定义线程池
 * <AUTHOR>
 * @date 2021/9/23 19:56
 **/
@Configuration
public class AsyncThreadPoolConfig {

    @Bean("asyncExecutor")
    public Executor   asyncThreadPool(){
//        ThreadPoolTaskExecutor  taskExecutor=new ThreadPoolTaskExecutor();
//        //核心线程数
//        taskExecutor.setCorePoolSize(10);
//        //最大线程数
//        taskExecutor.setMaxPoolSize(200);
//        //阻塞队列的容量
//        taskExecutor.setQueueCapacity(5);
//        //线程空闲时间
//        taskExecutor.setKeepAliveSeconds(200);
//        //拒绝策略 直接丢弃任务，抛出异常
//        taskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
//        // 等待所有任务都完成再继续销毁其他的Bean
//        taskExecutor.setWaitForTasksToCompleteOnShutdown(true);
//        // 线程池中任务的等待时间，如果超过这个时候还没有销毁就强制销毁，以确保应用最后能够被关闭，而不是阻塞住
//        taskExecutor.setAwaitTerminationSeconds(60);
//        taskExecutor.initialize();
//        return taskExecutor;
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setKeepAliveSeconds(200);
        executor.setThreadNamePrefix("async-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 等待所有任务都完成再继续销毁其他的Bean
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 线程池中任务的等待时间，如果超过这个时候还没有销毁就强制销毁，以确保应用最后能够被关闭，而不是阻塞住
        executor.setAwaitTerminationSeconds(60);
        executor.initialize();
        return executor;

    }

}