package com.iflytek.cdc.admin.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName AddressEntireMapping
 * @Description  模糊地址完全映射 包括映射 和 标准地址
 * <AUTHOR>
 * @Date 2021/7/16 16:57
 * @Version 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AmapAddressDetailMapping implements Serializable {

    private static final long serialVersionUID = 1071334225633927086L;

    /**
     * 详细地址
     */
    private String addressDetail;

    /**
     * 标准地址所属街道/行政村名称
     */
    private String addressAreaName;

    /**
     * 标准地址所属街道/行政村全局唯一ID(标准化地址唯一主键)
     */
    private String addressAreaCode;

    /**
     * 标准地址经度
     */
    private String addressLongitude;

    /**
     * 标准地址纬度
     */
    private String addressLatitude;

    /**
     * 标准地址-省份
     */
    private String addressProvinceName;

    /**
     * 标准地址-省份编码
     */
    private String addressProvinceCode;

    /**
     * 标准地址-市
     */
    private String addressCityName;

    /**
     * 标准地址-市编码
     */
    private String addressCityCode;

    /**
     * 标准地址-区县
     */
    private String addressDistrictName;

    /**
     * 标准地址-区县编码
     */
    private String addressDistrictCode;

    /**
     * 标准地址-街道名
     */
    private String addressTownName;

    /**
     * 标准地址-街道编码
     */
    private String addressTownCode;

    /**
     * 患者手机 标准行政区(市) 名
     */
    private String telephoneCityName;

    /**
     * 患者手机 标准行政区(市) 编码
     */
    private String telephoneCityCode;

    /**
     * 患者身份证 标准行政区(市) 名
     */
    private String idCardCityName;

    /**
     * 患者身份证 标准行政区(市) 编码
     */
    private String idCardCityCode;

    /**
     * 医疗机构 标准行政区(市) 名
     */
    private String orgCityName;

    /**
     * 医疗机构 标准行政区(市) 编码
     */
    private String orgCityCode;

    /**
     * POI类型
     */
    private String poiType;

}
