package com.iflytek.cdc.admin.model.mr.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class CommonQuery {

    @ApiModelProperty(
            value = "页码 从1开始",
            example = "1"
    )
    public Integer pageIndex;
    @ApiModelProperty(
            value = "每页条数",
            example = "10"
    )
    public Integer pageSize;
    @ApiModelProperty( "所在省")
    private String provinceCode;
    @ApiModelProperty( "所在市")
    private String cityCode;
    @ApiModelProperty( "所在县区")
    private String districtCode;
    @ApiModelProperty( "所在街道")
    private String streetCode;

    @ApiModelProperty( "排序字段")
    private String property;
    @ApiModelProperty( "排序顺序")
    private String direction;

    private List<String> provinceCodes;
    private List<String> cityCodes;
    private List<String> districtCodes;
    private List<String> streetCodes;
    private Integer areaLevel;
    private String areaCode;

}
