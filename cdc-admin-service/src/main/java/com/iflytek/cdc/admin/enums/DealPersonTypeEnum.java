package com.iflytek.cdc.admin.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum DealPersonTypeEnum {
    EMERGENCY("1", "应急值守人员"),
    DEPARTMENT("2", "指定科室人员"),
    ;

    private String code;
    private String desc;

    DealPersonTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Map<String, Object> mapValues() {
        return Arrays.stream(DealPersonTypeEnum.values()).collect(Collectors.toMap(DealPersonTypeEnum::name, s -> s));
    }

}
