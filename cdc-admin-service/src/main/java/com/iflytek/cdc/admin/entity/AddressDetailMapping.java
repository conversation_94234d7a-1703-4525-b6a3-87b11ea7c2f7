package com.iflytek.cdc.admin.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 模糊地址与标准地址映射表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-16
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tb_cdcmr_address_detail_mapping")
public class AddressDetailMapping implements Serializable {


    private static final long serialVersionUID = -4328682066361742181L;

    /**
     * 模糊地址与标准地址映射表 主键ID(雪花分片)
     */
    private String id;

    /**
     * 详细地址
     */
    @TableId(value = "address_detail", type = IdType.INPUT)
    private String addressDetail;

    /**
     * 标准地理信息表 主键ID
     */
    private String addressStandardId;

    /**
     * 患者手机 标准行政区(市) 名
     */
    @TableId(value = "telephone_city_name", type = IdType.INPUT)
    private String telephoneCityName;

    /**
     * 患者手机 标准行政区(市) 编码
     */
    @TableId(value = "telephone_city_code", type = IdType.INPUT)
    private String telephoneCityCode;

    /**
     * 患者身份证 标准行政区(市) 名
     */
    @TableId(value = "id_card_city_name", type = IdType.INPUT)
    private String idCardCityName;

    /**
     * 患者身份证 标准行政区(市) 编码
     */
    @TableId(value = "id_card_city_code", type = IdType.INPUT)
    private String idCardCityCode;

    /**
     * 医疗机构 标准行政区(市) 名
     */
    @TableId(value = "org_city_name", type = IdType.INPUT)
    private String orgCityName;

    /**
     * 医疗机构 标准行政区(市) 编码
     */
    @TableId(value = "org_city_code", type = IdType.INPUT)
    private String orgCityCode;

    /**
     * POI类型
     */
    @TableId(value = "poi_type", type = IdType.INPUT)
    private String poiType;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private String createDatetime;

    /**
     * 更新人
     */
    private String updator;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateDatetime;

    /**
     * 相似度
     */
    private Double similarity;

    /**
     * 状态
     */
    private Integer status;

}
