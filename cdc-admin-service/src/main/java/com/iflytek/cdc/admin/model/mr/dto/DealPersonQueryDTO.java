package com.iflytek.cdc.admin.model.mr.dto;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DealPersonQueryDTO {

    @ApiModelProperty(value = "疾病code")
    private String diseaseCode;

    @ApiModelProperty(value = "风险等级id")
    private String riskLevelId;

    @ApiModelProperty(value = "风险等级详情id")
    private String riskLevelDetailId;

    @ApiModelProperty(value = "预警类型")
    private String warningType;
}
