package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.dto.ClientUpgradeParamDto;
import com.iflytek.cdc.admin.dto.ClientUpgradeUrlDto;
import com.iflytek.cdc.admin.entity.ClientUpgradeUrl;
import io.lettuce.core.dynamic.annotation.Param;

import java.util.List;

/**
 * @ClassName ClientUpgradeUrlMapper
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/6/30 14:16
 * @Version 1.0
 */
public interface ClientUpgradeUrlMapper {

    int updateClientUpgradeUrlDto(ClientUpgradeUrlDto updateClientUpgradeUrlDto);

    List<ClientUpgradeUrlDto> queryClientUpgradeUrlDto(ClientUpgradeParamDto clientUpgradeParamDto);

    int addClientUpgradeUrlDto(ClientUpgradeUrlDto updateClientUpgradeUrlDto);

    List<ClientUpgradeUrlDto> getPackageFileUrlByAreaCode(@Param("areaCode") String areaCode);
}