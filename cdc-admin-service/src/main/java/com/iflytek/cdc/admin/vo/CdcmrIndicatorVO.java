package com.iflytek.cdc.admin.vo;

import com.iflytek.cdc.admin.dto.AnalysisTargetDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 */
@Data
public class CdcmrIndicatorVO implements Serializable {
    private static final long serialVersionUID = 1L;
    public static final String TABLE_NAME = "tb_cdcmr_indicator";
    /**
     * id
     */
    @ApiModelProperty("主键")
    private String id;

    /**
     * 标签
     */
    @ApiModelProperty("指标ID")
    private String indicatorLabel;

    /**
     * 指标名称
     */
    @ApiModelProperty("指标名称")
    private String indicatorName;

    /**
     * 指标别名
     */
    @ApiModelProperty("指标别名")
    private List<String> indicatorAliasNames;

    /**
     * 指标定义
     */
    @ApiModelProperty("指标定义")
    private String indicatorDefinition;


    @ApiModelProperty("分析对象")
    private List<AnalysisTargetDto> analysisTargetList;


    @ApiModelProperty("适用疾病")
    private ApplicableDisease applicableDisease;

    @Data
    public static class ApplicableDisease {
        private String type;
        private String diseaseId;
        private String diseaseName;
    }

    /**
     * 指标类型
     */
    @ApiModelProperty("指标类型")
    private String indicatorType;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private Date updateTime;

    /**
     * 创建人id
     */
    @ApiModelProperty("创建人id")
    private String creator;

    /**
     * 创建人名称
     */
    @ApiModelProperty("创建人名称")
    private String creatorName;

    /**
     * 修改人id
     */
    @ApiModelProperty("修改人id")
    private String updater;

    /**
     * 修改人名称
     */
    @ApiModelProperty("修改人名称")
    private String updaterName;

    /**
     * 删除标记
     */
    @ApiModelProperty("删除标记")
    private Integer deleteFlag;

    /**
     * 当前序号
     */
    @ApiModelProperty("指标序号")
    private Integer num;

    /**
     * 计算公式
     */
    @ApiModelProperty("计算公式")
    private String calculationFormula;


    /**
     * 业务过程
     */
    @ApiModelProperty("业务过程")
    private String businessProcess;

}