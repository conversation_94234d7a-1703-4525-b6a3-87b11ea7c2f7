package com.iflytek.cdc.admin.enums;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum UnknownReasonMonitorObjectEnum {

    HOSPITAL("等级医院", "1"),

    PRIMARY("基层医疗", "2"),

    ORG("学校/单位", "3"),

    STREET("街道区域", "4");

    private final String name;

    private final String code;

    UnknownReasonMonitorObjectEnum(String name, String code) {
        this.code = code;
        this.name = name;
    }
    public String getCode() {
        return code;
    }
    public String getName() {
        return name;
    }

    public static List<Map<String, Object>> getAllToList() {
        List<Map<String, Object>> list = new ArrayList<>();
        UnknownReasonMonitorObjectEnum[] values = values();
        for (UnknownReasonMonitorObjectEnum objectEnum : values) {
            Map<String, Object> valuesMap = new HashMap<>();
            valuesMap.put("name", objectEnum.name);
            valuesMap.put("code", objectEnum.code);
            list.add(valuesMap);
        }
        return list;
    }
}
