package com.iflytek.cdc.admin.enums;

import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum TableColumnTypeEnum implements BaseEnum{

    VARCHAR("VARCHAR", "字符串"),
    TEXT("TEXT", "文本"),
    NUMERIC("NUMERIC", "小数"),
    INT("INT", "整数"),
    DATE("DATE", "日期"),
    TIMESTAMP("TIMESTAMP", "日期时间"),

    ;

    private final String code;
    private final String name;

    TableColumnTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    
    public static String getCodeByName(String name){
        return Arrays.stream(TableColumnTypeEnum.values()).filter(d -> d.getName().equals(name)).findFirst().orElseThrow(
                () -> new MedicalBusinessException("未找到对应的数据类型")).getCode();
    }

    public static Map<String, Object> mapValues() {
        return Arrays.stream(values()).collect(Collectors.toMap(TableColumnTypeEnum::getCode, s -> s));
    }

    @Override
    public String getDesc() {
        return getName();
    }
}
