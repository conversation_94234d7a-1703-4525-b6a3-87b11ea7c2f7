package com.iflytek.cdc.admin.entity;

import lombok.*;

import java.util.Date;

@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class Area {
    /**
    * id
    */
    private Long id;

    /**
    * 地区代码
    */
    private String code;

    /**
    * 省份
    */
    private String province;

    /**
    * 城市
    */
    private String city;

    /**
    * 父级行政区划id
    */
    private Long parentId;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 县/区
    */
    private String district;

    /**
    * 最后修改时间
    */
    private Date lastUpdateTime;

    /**
    * 操作人
    */
    private Long operator;

    /**
    * 操作人ip
    */
    private String operatorIp;

    /**
    * initial
    */
    private String initial;

    /**
    * 区号
    */
    private String areacode;

    /**
    * 街道/镇
    */
    private String street;
}