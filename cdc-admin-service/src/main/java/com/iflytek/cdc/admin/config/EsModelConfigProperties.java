package com.iflytek.cdc.admin.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * ElasticSearch 模型检索 配置属性
 */
@Data
@ConfigurationProperties(prefix = EsModelConfigProperties.PREFIX)
public class EsModelConfigProperties {

    /**
     * Prefix of {@link EsModelConfigProperties}.
     */
    public static final String PREFIX = "cdc.elasticsearch";

    /**
     * Prefix of {@link EsModelConfigProperties}.
     */
    public static final String PROP_ENABLED = PREFIX + ".enabled";

    /**
     * es 检索地址前缀，给工具使用的
     */
    private String esHost;

    /**
     * 是否启用 es 检索
     */
    private boolean enabled = false;

    /**
     * 联想词索引的模板配置
     */
    private Template template = new Template();

    /**
     * 联想词索引的配置
     */
    private Suggest suggest = new Suggest();

    @Data
    public static class Template {

        /**
         * 是否保存原始文档，默认 true 保存原始文档，以后再优化
         */
        private boolean sourceEnabled = true;

        /**
         * 自动滚动的生命周期配置，可选。如果不配置，则创建索引模板时不绑定
         */
        private String lifecyclePolicy = "cdc_model_policy";

        /**
         * 添加自定义字段用的管道，参与检索，必须配置
         */
        private String defaultPipeline = "cdc_model_pipeline";

        /**
         * 映射类型，默认为动态映射：
         * <ul>
         * <li>动态映射（dynamic：true）</li>
         * <li>静态映射（dynamic：false）</li>
         * <li>严格模式（dynamic：strict）</li>
         * </ul>
         */
        private String dynamic = "true";

        /**
         * 分片数，默认 1 ，需要按找项目 es 部署实际情况调整
         */
        private int numberOfShards = 1;

        /**
         * 副本数，默认 0 ，需要按找项目 es 部署实际情况调整
         */
        private int numberOfReplicas = 0;

        /**
         * 刷新时间，单位秒，默认 30
         */
        private int refreshInterval = 30;

        /**
         * 模型转索引是否强制用嵌套对象（不强制时会根据是否重复来判断），默认 true
         */
        private boolean forceNested = true;

        /**
         * 索引的字段总数限制(es 内部默认 1000)，部分模型太大，需要调高此值，因此改为默认 10000
         */
        private int totalFieldsLimit = 10000;

        /**
         * 索引的嵌套字段限制(es 内部默认 50)，部分模型太大，需要调高此值，因此改为默认 500
         */
        private int nestedFieldsLimit = 500;

        /**
         * 索引的嵌套对象限制(es 内部默认 10000)，部分模型太大，需要调高此值，因此改为默认 1000000
         */
        private int nestedObjectsLimit = 1000000;
    }

    @Data
    public static class Suggest {

        /**
         * 联想词索引的名称，需要和数据湖中配置的建索引脚本一致
         */
        private String index = "cdc-suggest";
    }
}
