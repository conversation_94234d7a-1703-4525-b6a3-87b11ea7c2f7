package com.iflytek.cdc.admin.entity.mr;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 新发突发传染病主数据
 */
@Data
@ApiModel(value = "新发突发传染病主数据")
@EqualsAndHashCode(callSuper = false)
@TableName(value = "tb_cdcmr_emerging_disease_info")
public class TbCdcmrEmergingDiseaseInfo implements Serializable {

    @ApiModelProperty(value = "id")
    @TableId(value = "id")
    private String id;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time")
    private Date createTime;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "creator")
    private String creator;

    @ApiModelProperty(value = "创建人id")
    @TableField(value = "creator_id")
    private String creatorId;

    @ApiModelProperty(value = "删除标记")
    @TableField(value = "delete_flag")
    private String deleteFlag;

    @ApiModelProperty(value = "疾病编码")
    @TableField(value = "disease_code")
    private String diseaseCode;

    @ApiModelProperty(value = "疾病名称")
    @TableField(value = "disease_name")
    private String diseaseName;

    @ApiModelProperty(value = "疾病父id")
    @TableField(value = "disease_parent_id")
    private String diseaseParentId;

    @ApiModelProperty(value = "备注")
    @TableField(value = "notes")
    private String notes;

    @ApiModelProperty(value = "排序")
    @TableField(value = "order_flag")
    private Integer orderFlag;

    @ApiModelProperty(value = "状态")
    @TableField(value = "\"status\"")
    private Integer status;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_time")
    private Date updateTime;

    @ApiModelProperty(value = "更新人")
    @TableField(value = "updater")
    private String updater;

    @ApiModelProperty(value = "更新人id")
    @TableField(value = "updater_id")
    private String updaterId;

}