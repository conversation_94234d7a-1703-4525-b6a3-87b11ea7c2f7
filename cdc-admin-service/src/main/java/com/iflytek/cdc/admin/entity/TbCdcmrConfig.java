package com.iflytek.cdc.admin.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * tb_cdcmr_config
 * <AUTHOR>
@Data
public class TbCdcmrConfig implements Serializable {
    private String id;

    /**
     * 参数类型
     */
    private Integer configType;

    /**
     * 参数类型名称
     */
    private String configTypeName;

    /**
     * 参数名称
     */
    private String configKey;

    /**
     * 参数值
     */
    private String configValue;

    /**
     * 参数描述
     */
    private String configKeyDesc;

    /**
     * 操作人员姓名
     */
    private String updaterName;

}