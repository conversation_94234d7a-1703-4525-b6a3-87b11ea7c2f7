package com.iflytek.cdc.admin.vo.region;

import com.iflytek.cdc.admin.annotation.ExcelColumn;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 小区/组 - 区划信息维表
 */
@Data
public class GroupRegionFileVO {

    @ExcelColumn(name = "省", column = 1, required = true)
    private String provinceName;

    @ExcelColumn(name = "市", column = 2, required = true)
    private String cityName;

    @ExcelColumn(name = "区/县", column = 3, required = true)
    private String districtName;

    @ExcelColumn(name = "乡镇/街道", column = 4, required = true)
    private String streetName;

    @ExcelColumn(name = "社区/村", column = 5, required = true)
    private String villageName;

    @ExcelColumn(name = "小区/组", column = 6, required = true)
    private String groupName;

    @ExcelColumn(name = "小区/组别名", column = 7)
    private String groupAliasName;


}