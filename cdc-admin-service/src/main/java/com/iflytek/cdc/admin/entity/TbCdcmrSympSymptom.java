package com.iflytek.cdc.admin.entity;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * tb_cdcmr_symp_symptom
 * <AUTHOR>
@Data
public class TbCdcmrSympSymptom implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 症状编码
     */
    private String symptomCode;

    /**
     * 症状名称
     */
    private String symptomName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 开启状态：0禁用1启用
     */
    private Integer status;

    /**
     * 删除标识：0未删除1删除
     */
    private Integer isDeleted;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updater;

    private static final long serialVersionUID = 1L;
}