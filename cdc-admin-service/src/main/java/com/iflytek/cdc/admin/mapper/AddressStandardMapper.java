package com.iflytek.cdc.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.admin.entity.AddressStandard;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-16
 */
public interface AddressStandardMapper extends BaseMapper<AddressStandard> {

    /**
     * 存在就更新 不存在就新增
     *
     * @param addressStandard 标准地址表实体类
     */
    void saveOrUpdateAddressStandard(AddressStandard addressStandard);

    List<AddressStandard> selectByIds(@Param("list") List<String> addressStandardIds);
}
