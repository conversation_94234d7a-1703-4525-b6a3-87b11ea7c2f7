package com.iflytek.cdc.admin.dto;

import com.iflytek.cdc.admin.entity.TbCdcmrWarningGradeConfig;
import com.iflytek.cdc.admin.entity.TbCdcmrWarningGradeConfigRule;
import com.iflytek.cdc.admin.entity.TbCdcmrWarningGradeEmergencyPlan;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class WarningGradeConfigVO {
    @ApiModelProperty(value = "配置")
    TbCdcmrWarningGradeConfig config;
    @ApiModelProperty(value = "规则列表")
    List<TbCdcmrWarningGradeConfigRule> ruleList;
    @ApiModelProperty(value = "计划列表")
    List<TbCdcmrWarningGradeEmergencyPlan> planList;

}
