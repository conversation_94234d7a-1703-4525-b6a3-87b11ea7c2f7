package com.iflytek.cdc.admin.util.brief;

import com.baomidou.mybatisplus.extension.api.IErrorCode;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * api结果枚举
 *
 * <AUTHOR>
 * @date 2024/12/30
 */
@Getter
@AllArgsConstructor
public enum ApiResultEnum implements IErrorCode {
    /**
     * 失败
     */
    FAILED(500, "操作失败"),
    /**
     * 业务失败
     */
    OPERATE_FAILED(400, "操作校验不通过"),
    /**
     * 成功
     */
    SUCCESS(200, "执行成功");

    private final long code;
    private final String msg;


    public static ApiResultEnum fromCode(long code) {
        ApiResultEnum[] ecs = ApiResultEnum.values();
        for (ApiResultEnum ec : ecs) {
            if (ec.getCode() == code) {
                return ec;
            }
        }
        return SUCCESS;
    }
}