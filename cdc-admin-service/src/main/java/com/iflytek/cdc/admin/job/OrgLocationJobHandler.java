package com.iflytek.cdc.admin.job;

import cn.hutool.core.util.StrUtil;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.entity.UapOrganization;
import com.iflytek.cdc.admin.mapper.OrgMappingMapper;
import com.iflytek.cdc.admin.service.AmapService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description 机构经纬度定时任务
 * @date 2021/11/23 9:20
 */
@Component
@Slf4j
@Deprecated
public class OrgLocationJobHandler {

    @Resource
    private OrgMappingMapper orgMappingMapper;
    @Resource
    private AmapService amapService;

    @XxlJob(value = "orgLocationJobHandler")
    public ReturnT<String> execute(String param) throws Exception {
        log.info("开始执行更新机构经纬度定时任务---start");
        List<UapOrganization> organizationList = orgMappingMapper.queryNeedUpOrgList();
        organizationList.parallelStream().forEach(this::updataOrg);
        log.info("更新机构经纬度定时任务结束---end");
        return ReturnT.SUCCESS;
    }

    /**
     * 更新单个机构的经纬度
     * @param org
     */
    public void updataOrg(UapOrganization org){
        if(StrUtil.isNotBlank(org.getDistrictName())){
            //调用高德地图接口获取经纬度
            String location = amapService.search(org.getOrgName(), org.getDistrictName());
            if(StrUtil.contains(location, Constants.SPLIT_VAR)){
                BigDecimal longitude = new BigDecimal(location.split(Constants.SPLIT_VAR)[0]);
                BigDecimal latitude = new BigDecimal(location.split(Constants.SPLIT_VAR)[1]);
                org.setLatitude(latitude);
                org.setLongitude(longitude);
                orgMappingMapper.updateUapOrg(org);
            }

        }

    }





}
