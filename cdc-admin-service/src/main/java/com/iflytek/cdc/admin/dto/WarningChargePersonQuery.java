package com.iflytek.cdc.admin.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 信号负责人配置查询
 */
@Data
@ApiModel("信号负责人配置查询条件")
public class WarningChargePersonQuery {

    @ApiModelProperty(value = "疾病编码")
    private String diseaseCode;

    @ApiModelProperty(value = "疾病编码集合")
    private List<String> diseaseCodes;

    @ApiModelProperty(value = "风险等级详情id")
    private String riskLevelDetailId;

    @ApiModelProperty(value = "风险等级详情id")
    private String riskLevelId;

    @ApiModelProperty(value = "疾病类型")
    private String warningType;

    @ApiModelProperty(value = "专题id")
    private String topicId;

    /**
     * 处理时限预警类型
     */
    private String timeLimitType;

}
