package com.iflytek.cdc.admin.util;

import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.nio.charset.StandardCharsets;

/**
 * 文件处理工具类
 * <AUTHOR>
@Slf4j
public class FileUtils {

    public static HttpHeaders getHttpHeaders(String fullFileName){
        HttpHeaders httpHeaders = new HttpHeaders();
        String fileName = new String(fullFileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1);
        httpHeaders.setContentDispositionFormData("attachment", fileName);
        httpHeaders.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        return httpHeaders;
    }

    /**
     * 判断上传文件后缀是否满足置顶上传文件规则
     * */
    public static boolean isSatisfyRule(MultipartFile file, String extNameStr){

        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.isEmpty()) {
            return false;
        }

        String[] fileNameArray = originalFilename.split(Constants.SPLIT_POINT);
        String fileExtension = fileNameArray[fileNameArray.length - 1].toLowerCase();

        return extNameStr.toLowerCase().contains(fileExtension);
    }

    /**
     * 创建目录
     *
     * @param directoryPath
     */
    public static void mkdirFile(String directoryPath) {
        try {
            if (directoryPath == null) {
                return;
            }
            File directory = new File(directoryPath);
            if (!directory.exists()) {
                boolean isCreated = directory.mkdirs();
                if (isCreated) {
                    log.info("目录创建成功： {}", directoryPath);
                } else {
                    throw new MedicalBusinessException("目录创建失败： " + directoryPath);
                }
            } else {
                log.info("目录已创建： {}", directoryPath);
            }
        } catch (MedicalBusinessException e) {
            log.error("目录创建失败：{}", directoryPath, e);
            throw new MedicalBusinessException("目录创建失败： " + directoryPath);
        }
    }
}
