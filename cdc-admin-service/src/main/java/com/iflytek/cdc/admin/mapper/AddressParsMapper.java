package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.dto.ExportAddressDTO;
import com.iflytek.cdc.admin.dto.SearchAddressDTO;
import com.iflytek.cdc.admin.entity.ExportAddress;
import com.iflytek.cdc.admin.entity.RelationAddress;
import com.iflytek.cdc.admin.entity.StandardAddress;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/27 14:24
 **/
public interface AddressParsMapper {

    /**
     * 查询标准地址列表
     * @param sd
     * @return
     **/
    List<StandardAddress> queryStandardAddressList(SearchAddressDTO  sd);

    /**
     * 查询关联地址列表
     * @param sd
     * @return
     **/
    List<RelationAddress> queryRelationAddressList(SearchAddressDTO  sd);

    /**
     * 根据id删除映射地址
     * @param id
     * @return
     **/
    void  deleteRelationAddress(@Param("id") String id);

    /**
     *
     * @param ed
     * @return
     **/
    List<ExportAddress>  queryExportList(ExportAddressDTO  ed);



}
