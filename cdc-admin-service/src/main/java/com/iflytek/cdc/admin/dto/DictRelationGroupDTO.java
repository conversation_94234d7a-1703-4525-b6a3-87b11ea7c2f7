package com.iflytek.cdc.admin.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/8/25 11:12
 **/
@Data
@ApiModel(description = "mdm字典目录返回实体")
public class DictRelationGroupDTO {

    /**
     * 字典分组
     **/
    @ApiModelProperty(value="字典分组名称")
    private  String directoryLabel;

    /**
     * 字典分组代码
     **/
    @ApiModelProperty(value="字典分组代码")
    private  String  directoryValue;

    /**
     * 字典目录
     **/
    @ApiModelProperty(value="字典目录")
    private List<DictRelationDirectoryDTO>  children;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof DictRelationGroupDTO)){
            return false;
        }
        DictRelationGroupDTO that = (DictRelationGroupDTO) o;
        return Objects.equals(directoryValue, that.directoryValue);
    }

    @Override
    public int hashCode() {
        return Objects.hash(directoryValue);
    }
}
