package com.iflytek.cdc.admin.model.mr.dto;

import com.iflytek.cdc.admin.entity.mr.TbCdcmrInfectedDiseaseMonitor;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class MasterDataEditRecordDTO extends CommonMasterData {

    @ApiModelProperty(value = "主数据类型")
    @NotNull
    private String masterDataType;

    /*****************************************传染病主数据修改**********************************************/
    //传染病基础数据
    @ApiModelProperty(value = "传染病类型代码")
    private String infectedClassCode;

    @ApiModelProperty(value = "传染病类型名称")
    private String infectedClassName;

    @ApiModelProperty(value = "法定报告传染病分类code")
    private String diseaseTypeCode;

    @ApiModelProperty(value = "法定报告传染病分类名称")
    private String diseaseTypeName;

    @ApiModelProperty(value = "法定传染病管理分类code")
    private String managementTypeCode;

    @ApiModelProperty(value = "法定传染病管理分类名称")
    private String managementTypeName;

    @ApiModelProperty(value = "传播途径分类code")
    private String transmissionTypeCode;

    @ApiModelProperty(value = "传播途径分类名称")
    private String transmissionTypeName;

    //配置列表
    @ApiModelProperty(value = "传染病配置列表")
    private List<TbCdcmrInfectedDiseaseMonitor> infectedConfigList;

    //共有模块
    @ApiModelProperty(value = "排序标识")
    private Integer orderFlag;

    @ApiModelProperty(value = "备注")
    private String notes;

    @ApiModelProperty(value = "启用状态")
    private Integer status;

    @ApiModelProperty(value = "删除标识")
    private String deleteFlag;

}
