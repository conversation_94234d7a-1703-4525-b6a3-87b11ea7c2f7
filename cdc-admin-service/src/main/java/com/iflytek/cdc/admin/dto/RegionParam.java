package com.iflytek.cdc.admin.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class RegionParam {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;

    /**
     * 区划代码
     */
    private String regionCode;

    /**
     * 父级区划代码
     */
    @ApiModelProperty(value = "父级区划代码")
    private String parentRegionCode;

    /**
     * 省份编码
     */
    @ApiModelProperty(value = "省份编码")
    private String provinceCode;

    /**
     * 城市编码
     */
    @ApiModelProperty(value = "城市编码")
    private String cityCode;

    /**
     * 区县编码
     */
    @ApiModelProperty(value = "区县编码")
    private String districtCode;

    /**
     * 街道编码
     */
    @ApiModelProperty(value = "街道编码")
    private String streetCode;

    /**
     * 社区/村编码
     */
    @ApiModelProperty(value = "社区/村编码")
    private String villageCode;

    /**
     * 小区/组编码
     */
    @ApiModelProperty(value = "小区/组编码")
    private String groupCode;

}
