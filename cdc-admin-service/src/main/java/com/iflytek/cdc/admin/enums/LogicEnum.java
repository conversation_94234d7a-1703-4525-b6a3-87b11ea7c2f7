package com.iflytek.cdc.admin.enums;

public enum LogicEnum {

    AND(" and ", "且", "&&"),

    OR(" or ", "或", "||"),

    NONE("NONE", "NONE", "");

    private final String code;
    private final String name;

    private final String symbol;

    LogicEnum(String code, String name, String symbol) {
        this.code = code;
        this.name = name;
        this.symbol = symbol;
    }

    public static LogicEnum getByName(String name) {
        for (LogicEnum value : LogicEnum.values()) {
            if (value.name.equalsIgnoreCase(name)) {
                return value;
            }
        }
        return NONE;
    }

    //默认与
    public static String getCodeByName(String name) {
        for (LogicEnum value : LogicEnum.values()) {
            if (value.name.equalsIgnoreCase(name)) {
                return value.getCode();
            }
        }
        return AND.getCode();
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getSymbol() {
        return symbol;
    }
}