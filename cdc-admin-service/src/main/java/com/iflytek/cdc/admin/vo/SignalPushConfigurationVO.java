package com.iflytek.cdc.admin.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.Date;

@Data
public class SignalPushConfigurationVO {
    /**
     * id
     */
    private String id;
    /**
     * 疾病名称
     */
    private String diseaseName;
    /**
     * 疾病编码
     */
    private String diseaseCode;
    /**
     * 是否重复
     */
    private String isRepeat;
    /**
     * 重复开始时间（时分秒）
     */
    private String repeatEndTime;
    /**
     * 重复结束时间（时分秒）
     */
    private String repeatStartTime;
    /**
     * 重复频率（15min/30min/1h）
     */
    private String repeatFrequency;
    /**
     * 风险等级详情id
     */
    private String riskLevelDetailId;

    private String warningChargePersonTableId;
}
