package com.iflytek.cdc.admin.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * his机构模板数据导入结果
 * <AUTHOR>
 * @date 2021/7/1 10:00
 **/
@Data
public class ExcelUploadResult {

    /**
     * 导入成功数据条数
     **/
    @ApiModelProperty(value="导入成功数据条数")
    private  Integer  successNum;

    /**
     * 导入失败数据条数
     **/
    @ApiModelProperty(value="导入失败数据条数")
    private  Integer  failNum;

    /**
     * 导入总数据条数
     **/
    @ApiModelProperty(value="导入总数据条数")
    private  Integer  allNum;

    /**
     * 导入失败数据excel的URL
     **/
    @ApiModelProperty(value="失败数据的excel数据")
    private List<HisOrg> hisOrgFailList;
}
