package com.iflytek.cdc.admin.mapper.province;

import com.iflytek.cdc.admin.entity.TbCdcmrWarningRiskLevel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * <p>
 * 传染病预警-风险等级配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-23
 */
public interface TbCdcmrWarningRiskLevelMapper extends BaseMapper<TbCdcmrWarningRiskLevel> {

    List<TbCdcmrWarningRiskLevel> getWarningRiskLevelList();

}
