package com.iflytek.cdc.admin.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 附件类
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AttachmentDTO {
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 附件名称
     */
    @ApiModelProperty(value = "附件名称")
    private String attachmentName;

    /**
     * 附件存储地址
     */
    @ApiModelProperty(value = "附件存储地址")
    private String attachmentPath;

    /**
     * 创建时间
     */
    @JsonIgnore
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 修改时间
     */
    @JsonIgnore
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 状态，0-不可用；1-可用
     */
    @JsonIgnore
    @ApiModelProperty(value = "状态，0-不可用；1-可用")
    private Integer status;
}