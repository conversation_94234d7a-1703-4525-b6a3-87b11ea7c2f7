package com.iflytek.cdc.admin.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("tb_cdcmr_disease_lexicon")
public class DisLexicon implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId
    @ApiModelProperty(value = "id")
    private String id;
    /**
     * 疾病病种编码
     */
    @TableField("diseases_code")
    @ApiModelProperty(value = "疾病病种编码")
    private String diseasesCode;
    /**
     * 病种名称
     */
    @ApiModelProperty(value = "病种名称")
    @TableField("diseases_name")
    private String diseasesName;
    /**
     * 父级病种名称
     */
    @ApiModelProperty(value = "父级病种名称")
    @TableField("parent_diseases_name")
    private String parentDiseasesName;
    /**
     * 病种类型
     */
    @ApiModelProperty(value = "病种类型")
    @TableField("diseases_type")
    private String diseasesType;
    /**
     * 业务分类
     */
    @ApiModelProperty(value = "业务分类")
    @TableField("business_type")
    private String businessType;
    /**
     * 启用状态
     */
    @ApiModelProperty(value = "启用状态")
    @TableField("is_enable")
    private String isEnable;
    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除")
    @TableField("is_delete")
    private String isDelete;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;
    /**
     * 创建用户
     */
    @TableField("create_user")
    private String createUser;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 更新用户
     */
    @TableField("update_user")
    private String updateUser;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;
}
