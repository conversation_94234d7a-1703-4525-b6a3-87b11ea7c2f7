package com.iflytek.cdc.admin.mapper.province;

import com.iflytek.cdc.admin.entity.mr.TbCdcmrMultichannelTopicConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbCdcmrMultichannelTopicConfigMapper {

    /**
     * 根据专题id查询配置
     * */
    List<TbCdcmrMultichannelTopicConfig> queryByTopicId(@Param("topicId") String topicId,
                                                        @Param("channelType") String channelType);

    /**
     * 通过专题id 更新该专题所有配置
     * */
    void updateByTopicId(@Param("topicId") String topicId);

    /**
     * 批量新增数据
     *
     * @param entities List<TbCdcmrMultichannelTopicConfig> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<TbCdcmrMultichannelTopicConfig> entities);
}
