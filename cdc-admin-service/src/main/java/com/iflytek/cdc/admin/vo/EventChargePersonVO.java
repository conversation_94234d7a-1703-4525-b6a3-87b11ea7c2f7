package com.iflytek.cdc.admin.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
@Data
public class EventChargePersonVO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty(name = "主键")
    private String id;
    /**
     * 事件类型
     */
    @ApiModelProperty(value = "事件类型")
    private String eventType;
    /**
     * 事件类型名称
     */
    @ApiModelProperty(name = "事件类型名称")
    private String eventTypeName;
    /**
     * 疾病code
     */
    @ApiModelProperty(name = "疾病id")
    private String diseaseId;

    /**
     * 疾病code
     */
    @ApiModelProperty(name = "疾病code")
    private String diseaseCode;

    /**
     * 疾病name
     */
    @ApiModelProperty(name = "疾病name")
    private String diseaseName;

    /**
     * 事件严重等级
     */
    @ApiModelProperty(value = "事件严重等级")
    private String eventLevel;
    /**
     * 事件严重等级id
     */
    @ApiModelProperty(name = "事件严重等级id")
    private String eventLevelId;

    /**
     * 处理责任人类别
     */
    @ApiModelProperty(name = "处理责任人类别")
    private String dealPersonType;

    /**
     * 处理人id
     */
    @ApiModelProperty(name = "处理人信息")
    private String dealPersonInfo;

    /**
     * 是否启用
     */
    @ApiModelProperty("是否启用")
    private String isEnable;
}

