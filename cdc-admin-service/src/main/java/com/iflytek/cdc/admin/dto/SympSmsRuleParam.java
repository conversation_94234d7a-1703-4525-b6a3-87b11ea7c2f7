package com.iflytek.cdc.admin.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class SympSmsRuleParam {
    /**
     * 姓名
     */
    private String name;

    /**
     * 人员类型
     */
    private String userType;

    /**
     * 手机
     */
    private String phone;

    /**
     * 规则类型
     */
    private String ruleType;

    /**
     * 规则参数
     */
    private String ruleNum;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 市名称
     */
    private String cityName;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 区县名称
     */
    private String districtName;

    /**
     * 区县编码
     */
    private String districtCode;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private String updater;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标识
     */
    private Short deleted;

    /**
     * 业务人员ID
     */
    private String businessPersonId;

    /**
     * 症状列表
     */
    @ApiModelProperty("症状列表")
    private List<SympNameVO> schoolSymptomList;

}
