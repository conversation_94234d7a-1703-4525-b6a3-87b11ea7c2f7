package com.iflytek.cdc.admin.entity.brief;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.brief.ApplicableDiseaseDto;
import com.iflytek.cdc.admin.enums.DeleteFlagEnum;

import lombok.Data;

/**
 * ${comments}
 *
 * <AUTHOR>
 * @date 2024-12-30 14:52:02
 */
@Data
@TableName("tb_cdcbr_template")
public class TemplateEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * $column.comments
     */
    private String id;
    /**
     * 统计周期
     */
    private String statisticsCycle;
    /**
     * 模板标题
     */
    private String title;
    /**
     * 分析对象
     */
    private String analysisObject;

    /**
     * 内容
     */
    private String content;
    /**
     * 内容标题
     */
    private String contentTitle;
    /**
     * 附件标题
     */
    private String attachmentTitle;
    /**
     * 原始模板内容
     */
    private String originalContent;
    /**
     * 业务类型：传染病；XX症候群
     */
    private String businessType;
    /**
     * 分析对象名称
     */
    private String analysisObjectName;
    /**
     * 修改者ID
     */
    private String creatorId;
    /**
     * 修改者
     */
    private String creatorName;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 修改者ID
     */
    private String updatorId;
    /**
     * 修改者
     */
    private String updatorName;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    /**
     * 删除标识
     */
    private String deleteFlag = DeleteFlagEnum.NO.getCode();
    /**
     * 附件标识
     */
    private String attachmentFlag = Constants.BriefReport.ATTACHMENT_FLAG_YES;

    /**
     * 适用疾病对象
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private ApplicableDiseaseDto applicableDisease;
}
