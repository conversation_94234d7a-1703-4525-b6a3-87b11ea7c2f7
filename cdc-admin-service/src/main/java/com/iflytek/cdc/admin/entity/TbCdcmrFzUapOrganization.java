package com.iflytek.cdc.admin.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
public class TbCdcmrFzUapOrganization implements Serializable {
    /**
    * 机构信息idi
    */
    private String id;

    /**
    * 机构名称
    */
    private String name;

    /**
    * 机构编码
    */
    private String code;

    /**
    * 所属省份
    */
    private String province;

    /**
    * 所属省份代码
    */
    private String provinceCode;

    /**
    * 所属城市
    */
    private String city;

    /**
    * 所属城市代码
    */
    private String cityCode;

    /**
    * 所属区县
    */
    private String district;

    /**
    * 所属区县代码
    */
    private String districtCode;

    /**
    * 机构简称
    */
    private String shortName;

    /**
    * 机构类型
    */
    private String orgType;

    /**
    * 机构类型代码
    */
    private String orgTypeCode;

    /**
    * 上级机构id
    */
    private String higherOrg;

    /**
    * 上级机构name
    */
    private String higherName;

    private Integer level;

    /**
    * 机构状态{0停用 1启用}
    */
    private Integer status;

    /**
    * 排序
    */
    private Integer sort;

    /**
    * 备注
    */
    private String remark;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 更新时间
    */
    private Date updateTime;

    /**
    * 上级机构编码
    */
    private String higherOrgCode;

    /**
    * 街道/镇
    */
    private String street;

    /**
    * 街道/镇编码
    */
    private String streetCode;

    /**
    * 处理状态（0未处理 1处理）
    */
    private Integer flag;

    private static final long serialVersionUID = 1L;
}