package com.iflytek.cdc.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName(value = "tb_cdcmr_signal_push_config_message_relation")
public class TbCdcmrSignalPushConfigMessageRelation {
    public static final String TABLE_NAME = "tb_cdcmr_signal_push_config_message_relation";
    @TableId(value = "id")
    private String id;

    @TableField(value = "app_code")
    private String appCode;

    @TableField(value = "message_content")
    private String messageContent;

    @TableField(value = "message_name")
    private String messageName;

    @TableField(value = "message_type")
    private Integer messageType;

    @TableField(value = "receiver")
    private String receiver;

    @TableField(value = "receiver_id")
    private String receiverId;

    @TableField(value = "receiver_login_name")
    private String receiverLoginName;

    @TableField(value = "request_param")
    private String requestParam;

    @TableField(value = "sender")
    private String sender;

    @TableField(value = "sender_id")
    private String senderId;

    @TableField(value = "sender_login_name")
    private String senderLoginName;

    @TableField(value = "signal_push_config_id")
    private String signalPushConfigId;

    @TableField(value = "source_system_code")
    private String sourceSystemCode;

    @TableField(value = "source_system_name")
    private String sourceSystemName;

    @TableField(value = "system_relative_path")
    private String systemRelativePath;

    @TableField(value = "task_id")
    private String taskId;
    @TableField(value = "create_time")
    private Date createTime;
    @TableField(value = "status")
    private Integer status;
    @TableField(value = "message_config_id")
    private String messageConfigId;
}