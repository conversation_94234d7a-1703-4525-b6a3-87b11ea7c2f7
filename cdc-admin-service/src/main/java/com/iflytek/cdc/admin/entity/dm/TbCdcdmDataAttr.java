package com.iflytek.cdc.admin.entity.dm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * ;
 * <AUTHOR> dingyuan
 * @date : 2024-4-7
 */
@ApiModel(value = "值域列表")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TbCdcdmDataAttr implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(name = "主键")
    private String id ;

    /**
     * 名称
     */
    @ApiModelProperty(name = "名称")
    private String name ;

    /**
     * 层级数
     */
    @ApiModelProperty(name = "层级数")
    private Integer attrCount ;

    /**
     * 值域个数
     */
    @ApiModelProperty(name = "值域个数")
    private Integer attrValueCount ;

    /**
     * 类型：1. 值域-单值；2. 值域-代码-值；3-接口
     */
    @ApiModelProperty(name = "类型：1. 值域-单值；2. 值域-代码-值；3-接口")
    private String type ;

    /**
     * 接口url
     */
    @ApiModelProperty(name = "接口url")
    private String dataUrl ;

    /**
     * 数据字典编码
     */
    @ApiModelProperty(name = "数据字典编码")
    private String dataDictCode ;

    /**
     * 备注
     */
    @ApiModelProperty(name = "备注")
    private String note ;

    /**
     * 是否删除
     */
    @ApiModelProperty(name = "是否删除")
    private Integer isDeleted ;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "创建时间")
    private Date createTime ;

    /**
     * 创建人
     */
    @ApiModelProperty(name = "创建人")
    private String creator ;

    /**
     * 修改时间
     */
    @ApiModelProperty(name = "修改时间")
    private Date updateTime ;

    /**
     * 修改人
     */
    @ApiModelProperty(name = "修改人")
    private String updater ;

}
