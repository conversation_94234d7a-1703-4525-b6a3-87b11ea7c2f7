package com.iflytek.cdc.admin.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MedicalWarnEditDto {

    @ApiModelProperty(value = "主键")
    String id ;
    /**
     * 信号生成节点
     */
    @ApiModelProperty(value = "信号生成节点:1、报卡弹报 2、医生提交 3、防保科审核 4、上报大疫情网")
    private Integer eventGenerationNode;

    /**
     * 信号静默时长
     */
    @ApiModelProperty(value = "信号静默时长")
    private Integer silenceDuration;

    /**
     * 静默时长单位
     */
    @ApiModelProperty(value = "静默时长单位：1=分，2=时")
    private Integer silenceDurationUnit;

    /**
     * AI排除开启状态
     */
    @ApiModelProperty(value = "AI排除开启状态:1=是,0=否")
    private Integer aiRemovedState;

    /**
     * AI排除时长
     */
    @ApiModelProperty(value = "AI排除时长单位:1=天，2=周，3=月")
    private Integer aiRemovedDuration;

    /**
     * AI排除时长单位
     */
    @ApiModelProperty(value = "是否为单病例触发:1=是,0=否")
    private Integer aiRemovedDurationUnit;

    /**
     * 是否为单病例触发
     */
    @ApiModelProperty(value = "是否为单病例触发")
    private Integer isSingleCase;

    /**
     * 传染病编码
     */
    @ApiModelProperty(value = "传染病编码")
    private String diseaseCode;

    private String updator;

    /**
     * 预警短信发送类型编码
     */
    private String smsSendTypeCode;

    /**
     * 预警短信发送类型描述
     */
    private String smsSendTypeDesc;


}
