package com.iflytek.cdc.admin.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel("风险等级详情查询条件")
@Data
public class RiskLevelDetailQueryDTO {
    @ApiModelProperty("预警类型")
    private String warningType;

    @ApiModelProperty("疾病数据")
    private List<String> diseaseIds;

    @ApiModelProperty("详情id")
    private List<String> detailIds;
}
