package com.iflytek.cdc.admin.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * tb_cdcmr_warning_grade
 * <AUTHOR>
@Data
public class TbCdcmrWarningGrade implements Serializable {

    /**
     * 分级编码
     */
    @ApiModelProperty(value = "分级编码")
    private String gradeCode;

    /**
     * 分级名称
     */
    @ApiModelProperty(value = "分级名称")
    private String gradeName;

    /**
     * 颜色
     */
    @ApiModelProperty(value = "颜色")
    private String color;

    /**
     * 启用标识
     */
    @ApiModelProperty(value = "启用标识")
    private Integer status;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 更新用户
     */
    @ApiModelProperty(value = "更新用户")
    private String updateUser;

    /**
     * 删除标识
     */
    @ApiModelProperty(value = "删除标识")
    private Integer isDeleted;

    private static final long serialVersionUID = 1L;
}