package com.iflytek.cdc.admin.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;

/**
 * 指标类型
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@Getter
public enum ApplicableDiseaseEnum {

    UNLIMITED("unlimited", "不限"),
    INFECTED("infected", "传染病"),
    //    PATIENT("patient", "患者"),
    NON_INFECTED("non_infected", "非传染病");

    private String name;
    private String desc;

    ApplicableDiseaseEnum(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }
}
