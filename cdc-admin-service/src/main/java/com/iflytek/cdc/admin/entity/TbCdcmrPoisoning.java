package com.iflytek.cdc.admin.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * tb_cdcmr_poisoning
 * <AUTHOR>
@Data
public class TbCdcmrPoisoning implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 中毒编码
     */
    private String poisoningCode;

    /**
     * 中毒名称
     */
    private String poisoningName;

    /**
     * 父类编码
     */
    private String parentCode;

    /**
     * 父类名称
     */
    private String parentName;

    /**
     * 中毒分类编码 1.食物和饮用水中毒；2.职业中毒；3.其他
     */
    private String poisoningTypeCode;

    /**
     * 中毒分类名称
     */
    private String poisoningTypeName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 开启状态：0禁用1启用
     */
    private Integer status;

    /**
     * 删除标识：0未删除1删除
     */
    private Integer isDeleted;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updater;

    private static final long serialVersionUID = 1L;
}