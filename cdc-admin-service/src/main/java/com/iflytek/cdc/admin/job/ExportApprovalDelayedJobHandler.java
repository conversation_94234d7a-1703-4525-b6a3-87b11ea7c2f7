package com.iflytek.cdc.admin.job;

import cn.hutool.core.date.SystemClock;
import com.iflytek.cdc.admin.mapper.TbCdcmrExportApprovalMapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 延期的导出申请自动审批
 */
@Component
@Slf4j
public class ExportApprovalDelayedJobHandler extends IJobHandler {

    @Resource
    private TbCdcmrExportApprovalMapper exportApprovalMapper;

    @Override
    @XxlJob("exportApprovalDelayedJobHandler")
    public ReturnT<String> execute(String param) {
        long startTime = SystemClock.now();
        log.info("开始自动审批延期的导出申请");
        int rowCount = exportApprovalMapper.autoPassDelayedApprovals();
        log.info("结束自动审批延期的导出申请 {} 条，耗时：{}", rowCount, SystemClock.now() - startTime);
        return ReturnT.SUCCESS;
    }
}
