package com.iflytek.cdc.admin.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * tb_cdcmr_warning_grade_config_rule
 * <AUTHOR>
@Data
public class TbCdcmrWarningGradeConfigRule implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 配置id
     */
    @ApiModelProperty(value = "配置id")
    private String configId;

    /**
     * 分级依据
     */
    @ApiModelProperty(value = "分级依据, 病例数量：1; 病例增长率：2; 死亡数量：3; 死亡增长率：4;")
    private Integer gradeType;

    /**
     * 分级名称
     */
    @ApiModelProperty(value = "分级名称")
    private String gradeName;

    /**
     * 分级编码
     */
    @ApiModelProperty(value = "分级编码")
    private String gradeCode;

    /**
     * 警戒值
     */
    @ApiModelProperty(value = "警戒值")
    private Integer alertValue;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 更新用户
     */
    @ApiModelProperty(value = "更新用户")
    private String updateUser;

    /**
     * 删除标识
     */
    @ApiModelProperty(value = "删除标识")
    private Integer isDeleted;

    private static final long serialVersionUID = 1L;
}