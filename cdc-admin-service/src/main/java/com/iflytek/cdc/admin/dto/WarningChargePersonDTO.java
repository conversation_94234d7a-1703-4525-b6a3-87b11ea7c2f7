package com.iflytek.cdc.admin.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel("信号处理责任人配置前端传参")
@Data
public class WarningChargePersonDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 预警类型
     */
    @ApiModelProperty(value = "疾病类型")
    private String warningType;

    /**
     * 疾病id
     */
    @ApiModelProperty(value = "疾病id")
    private String diseaseId;


    /**
     * 疾病code
     */
    @ApiModelProperty(value = "疾病code")
    private String diseaseCode;

    /**
     * 疾病name
     */
    @ApiModelProperty(value = "疾病name")
    private String diseaseName;

    /**
     * 风险等级id
     */
    @ApiModelProperty(value = "风险等级id")
    private String riskLevelDetailId;

    /**
     * 处理责任人类别
     */
    @ApiModelProperty(value = "处理责任人类别")
    private String dealPersonType;

    /**
     * 处理人id
     */
    @ApiModelProperty(value = "处理人信息")
    private List<DealUser> dealPersonInfo;

    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private String isEnabled;


    @Data
    @ApiModel(value = "处理人")
    public static class DealUser{
        @ApiModelProperty("用户id")
        private String id;

        @ApiModelProperty("用户姓名")
        private String name;

        @ApiModelProperty("省编码")
        private String provinceCode;

        @ApiModelProperty("省")
        private String provinceName;

        @ApiModelProperty("市编码")
        private String cityCode;

        @ApiModelProperty("市")
        private String cityName;

        @ApiModelProperty("区编码")
        private String districtCode;

        @ApiModelProperty("区名称")
        private String districtName;

        @ApiModelProperty("机构id")
        private String orgId;

        @ApiModelProperty("机构名称")
        private String orgName;

        @ApiModelProperty("手机号")
        private String phone;
    }

}