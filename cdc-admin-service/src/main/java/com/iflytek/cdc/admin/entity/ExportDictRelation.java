package com.iflytek.cdc.admin.entity;

import com.iflytek.cdc.admin.annotation.ExcelColumnName;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/8/26 16:58
 **/
@Data
public class ExportDictRelation {

    @ExcelColumnName(name = "关联目录")
    private String  directoryName;

    @ExcelColumnName(name = "业务字典名称")
    private  String  originDictName;

    @ExcelColumnName(name = "业务字典代码")
    private  String  originDictCode;

    @ExcelColumnName(name = "关联字典名称")
    private  String  targetDictName;

    @ExcelColumnName(name = "关联字典代码")
    private  String  targetDictCode;

    @ExcelColumnName(name = "值代码")
    private  String  originDictValueCode;

    @ExcelColumnName(name = "值含义")
    private  String  originDictValueName;

    @ExcelColumnName(name = "关联值代码")
    private  String  targetDictValueCode;

    @ExcelColumnName(name = "关联值含义")
    private  String  targetDictValueName;

}
