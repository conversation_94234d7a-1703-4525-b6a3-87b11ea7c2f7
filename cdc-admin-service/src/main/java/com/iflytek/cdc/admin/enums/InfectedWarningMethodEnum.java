package com.iflytek.cdc.admin.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;

@Getter
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum InfectedWarningMethodEnum {

    SCAM("SCAM", "单病例预警法", "固定阈值法"),
    EAT("EAT", "聚集性疫情法", "固定阈值法"),

    MPM("MPM", "移动百分位数法", "时间预警"),
    CUSUM("CUSUM", "累积和控制图法", "时间预警"),
    EWMA("EWMA", "指数加权移动平均法", "时间预警"),
    MA("MA", "移动平均数法", "时间预警"),

    ASSOCIATE("ASSOCIATE", "关联聚集", "空间预警"),

    EWMA_AND_ASSOCIATE("EWMA_AND_ASSOCIATE", "移动百分位数法+关联聚集", "时间+空间预警"),
    CUSUM_AND_ASSOCIATE("CUSUM_AND_ASSOCIATE", "累积和控制图法+关联聚集", "时间+空间预警"),
    MPM_AND_ASSOCIATE("MPM_AND_ASSOCIATE", "指数加权移动平均法+关联聚集", "时间+空间预警"),
    MA_AND_ASSOCIATE("MA_AND_ASSOCIATE", "移动平均数法+关联聚集", "时间+空间预警"),

    ;

    private final String value;
    private final String label;
    private final String category;


    InfectedWarningMethodEnum(String value, String label, String category) {
        this.value = value;
        this.label = label;
        this.category = category;
    }

}
