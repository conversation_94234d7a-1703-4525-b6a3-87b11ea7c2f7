package com.iflytek.cdc.admin.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/5 17:25
 **/
@Data
public class SearchParamDTO  extends  PageInfoDTO{

    /**
     * 参数分类
     **/
    @ApiModelProperty(value = "参数分类")
    private  String  configGroup;

    /**
     * 参数编号
     **/
    @ApiModelProperty(value = "参数编号")
    private  String  configCode;

    /**
     * 参数名称
     **/
    @ApiModelProperty(value = "参数名称")
    private  String  configName;

    /**
     * 机构编码
     **/
    @ApiModelProperty(value = "机构编码")
    private  String  orgCode;

    /**
     * 机构名称
     **/
    @ApiModelProperty(value = "机构名称")
    private  String  orgName;

    /**
     * 机构id集合
     **/
    @ApiModelProperty(value = "机构id集合")
    private List<String> orgIds;

    /**
     * 是否删除
     */
    @ApiModelProperty(value="是否删除")
    private String isDelete;


    @ApiModelProperty(value="机构id")
    private String orgId;



}
