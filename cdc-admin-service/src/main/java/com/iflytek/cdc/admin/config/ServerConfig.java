package com.iflytek.cdc.admin.config;

import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.context.WebServerInitializedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.net.Inet6Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.Enumeration;

/**
 * 获取项目的IP和端口
 *
 * <AUTHOR>
 */


@Component
@Slf4j
public class ServerConfig implements ApplicationListener<WebServerInitializedEvent> {

    /**
     * 单网卡名称
     */
    private static final String NETWORK_CARD = "eth0";

    /**
     * 绑定网卡名称
     */
    private static final String NETWORK_CARD_BAND = "bond0";

    private int serverPort;

    @Override
    public void onApplicationEvent(WebServerInitializedEvent event) {
        serverPort = event.getWebServer().getPort();
    }

    /**
     * Description: linux下获得本机IPv4 IP<br>
     *
     * @return String
     */
    public String  getUrl(Boolean isNeedPort) {
        String ip = "";
        try {
            Enumeration<NetworkInterface> e1 = NetworkInterface.getNetworkInterfaces();
            while (e1.hasMoreElements()) {
                NetworkInterface ni = e1.nextElement();

                //单网卡或者绑定双网卡
                if ((NETWORK_CARD.equals(ni.getName()))
                        || (NETWORK_CARD_BAND.equals(ni.getName()))) {
                    Enumeration<InetAddress> e2 = ni.getInetAddresses();
                    while (e2.hasMoreElements()) {
                        InetAddress ia = e2.nextElement();
                        if (ia instanceof Inet6Address) {
                            continue;
                        }
                        ip = ia.getHostAddress();
                    }
                    break;
                } else {
                    ip = InetAddress.getLocalHost().getHostAddress();
                }
            }
        } catch (Exception e) {
            log.error("获取IP出现异常！异常信息：", e);
            throw new MedicalBusinessException("获取IP出现异常！");
        }

        String urlCombine = "";

        if (isNeedPort) {
            urlCombine = "http://" + ip + ":" + this.serverPort + "/";
        } else {
            urlCombine = "http://" + ip + "/";
        }

        return urlCombine;

    }


}
