package com.iflytek.cdc.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.admin.dto.ApproverDTO;
import com.iflytek.cdc.admin.entity.TbCdcmrOrgApprove;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbCdcmrOrgApproveMapper extends BaseMapper<TbCdcmrOrgApprove> {

    List<ApproverDTO> selectApproveByPermissionOrgId(@Param("permissionOrgId") String permissionOrgId);

    void saveOrgApprove(List<TbCdcmrOrgApprove> tbCdcmrOrgApproves);
}
