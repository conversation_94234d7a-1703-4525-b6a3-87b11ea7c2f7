package com.iflytek.cdc.admin.entity.mr;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 病案文件审批权限表实体类
 */
@Data
@ApiModel("病案文件审批权限表")
@TableName(value = "tb_cdcmr_illness_file_approval_auth")
public class TbCdcmrIllnessFileApprovalAuth implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private String id;

    @ApiModelProperty("权限编码")
    private String authCode;

    @ApiModelProperty("权限分类(档案调阅/档案维护/档案注销)")
    private String authName;

    @ApiModelProperty("审批人id")
    private String approvalUserId;

    @ApiModelProperty("多审批人id")
    @TableField(exist = false)
    private List<String> approvalUserIds;

    @ApiModelProperty("审批人姓名")
    private String approvalUserName;

    @ApiModelProperty("机构id")
    private String orgId;

    @ApiModelProperty("机构名称")
    private String orgName;

    @ApiModelProperty("权限状态(1启用;0未启用)")
    private String status;

    @ApiModelProperty("删除标识(0-未删除、1-已删除)")
    private String deleteFlag;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("创建人id")
    private String creatorId;

    @ApiModelProperty("创建人")
    private String creator;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("更新人id")
    private String updateId;

    @ApiModelProperty("更新人")
    private String updater;
}