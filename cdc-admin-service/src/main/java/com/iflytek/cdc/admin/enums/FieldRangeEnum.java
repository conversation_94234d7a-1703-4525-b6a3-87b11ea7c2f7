package com.iflytek.cdc.admin.enums;

import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public enum FieldRangeEnum {
    ALL("ALL", "全部"),
    LIST("LIST", "列表"),
    CUSTOM("CUSTOM", "自定义");

    private final String code;
    private final String value;

    FieldRangeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    // 根据 code 获取枚举值
    public static FieldRangeEnum getByCode(String code) {
        for (FieldRangeEnum range : FieldRangeEnum.values()) {
            if (range.getCode().equals(code)) {
                return range;
            }
        }
        log.error("无效的导出代码: {}", code);
        throw new MedicalBusinessException("无效的导出代码");
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }


}