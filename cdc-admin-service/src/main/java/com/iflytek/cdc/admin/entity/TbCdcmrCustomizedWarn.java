package com.iflytek.cdc.admin.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * tb_cdcmr_customized_warn
 *
 * <AUTHOR>
@Data
public class TbCdcmrCustomizedWarn implements Serializable {
    @ApiModelProperty(value = "自定义标准ID")
    private String id;
    @ApiModelProperty(value = "自定义标准CODE")
    private String code;
    @ApiModelProperty(value = "自定义标准NAME")
    private String name;
    @ApiModelProperty(value = "监测数据来源")
    private String monitorDataSource;
    @ApiModelProperty(value = "预警类型")
    private String warningType;
    @ApiModelProperty(value = "预警类型名称")
    private String warningTypeName;
    @ApiModelProperty(value = "预警关联,可多选,逗号分隔")
    private String warningCorrelation;
    @ApiModelProperty(value = "状态")
    private Integer status;
    @ApiModelProperty(value = "备注")
    private String memo;
    @ApiModelProperty(value = "删除标识")
    private String deleteFlag;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "创建者")
    private String creator;
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    @ApiModelProperty(value = "更新者")
    private String updater;

}