package com.iflytek.cdc.admin.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * tb_cdcmr_customized_sms_rule
 *
 * <AUTHOR>
@Data
public class TbCdcmrCustomizedSmsRule implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private String id;
    /**
     * 姓名
     */
    private String name;
    /**
     * 人员类型
     */
    private String userType;
    /**
     * 手机
     */
    private String phone;
    /**
     * 规则类型
     */
    private String ruleType;
    /**
     * 规则参数
     */
    private String ruleNum;
    /**
     * 省名称
     */
    private String provinceName;
    /**
     * 省编码
     */
    private String provinceCode;
    /**
     * 市名称
     */
    private String cityName;
    /**
     * 市编码
     */
    private String cityCode;
    /**
     * 区县名称
     */
    private String districtName;
    /**
     * 区县编码
     */
    private String districtCode;
    /**
     * 自定义名称
     */
    private String customizedName;
    /**
     * 自定义编码
     */
    private String customizedCode;
    /**
     * 创建者
     */
    private String creator;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新者
     */
    private String updater;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 删除标识
     */
    private Integer deleted;
    /**
     * 业务人员ID
     */
    private String loginUserId;
}