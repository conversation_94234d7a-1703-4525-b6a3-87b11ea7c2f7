package com.iflytek.cdc.admin.util;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.common.enums.AreaLevelEnum;
import com.iflytek.cdc.admin.common.vo.uap.UapUserPo;
import com.iflytek.cdc.admin.model.dm.dto.DiseaseTreeNode;
import com.iflytek.cdc.admin.datamodel.model.vo.ValueDomainVO;
import com.iflytek.cdc.admin.model.mr.dto.CommonQuery;
import com.iflytek.fsp.flylog.sdk.java.core.brave.utils.StringUtil;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.iflytek.cdc.admin.common.interceptor.UserInfoInterceptor.USER_INFO;

@Slf4j
@Service
public class CommonUtils {

    @Value("${codeStartNum:1000}")
    private int startNum;

    /**
     * 通过反射获取对象某个字段的值
     * */
    public <T> Object getValueByFieldName(T obj, String fieldName) {

        try{
            Class<?> clazz = obj.getClass();
            Field field = clazz.getDeclaredField(fieldName);
            // 设置访问权限
            field.setAccessible(true);

            // 获取字段的值
            return field.get(obj);
        }catch (Exception e){
            log.info(String.valueOf(e));
        }
        return null;
    }

    /**
     * 遍历树 判断值是否存在节点上
     * */
    public boolean traverseForest(ValueDomainVO valueDomainVO, String value){

        if(valueDomainVO == null){
            return false;
        }
        if(Objects.equals(value, valueDomainVO.getValue())){
            return true;
        }
        if(valueDomainVO.getChildren() != null) {
            for (ValueDomainVO child : valueDomainVO.getChildren()){
                if(traverseForest(child, value)){
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 合并 森林
     * */
    public ValueDomainVO mergeForest(List<ValueDomainVO> forest) {

        //初始化一个共有根节点
        ValueDomainVO mergedForest = new ValueDomainVO("id", "dataAttrId", "ForestRoot", "ForestRoot");
        Map<String, ValueDomainVO> nodeMap = new HashMap<>();

        for (ValueDomainVO tree : forest) {
            mergeTree(mergedForest, tree, nodeMap);
        }
        setChildrenToNull(mergedForest);
        return mergedForest;
    }

    private void mergeTree(ValueDomainVO mergedForest,
                           ValueDomainVO tree,
                           Map<String, ValueDomainVO> nodeMap) {
        ValueDomainVO existingNode = nodeMap.get(tree.getLabel() + tree.getValue());
        if (existingNode != null) {
            // 如果节点已经存在，则递归合并子树
            for (ValueDomainVO child : tree.getChildren()) {
                mergeTree(existingNode, child, nodeMap);
            }
        } else {
            // 如果节点不存在，则添加
            ValueDomainVO newNode = new ValueDomainVO(tree.getId(), tree.getDataAttrId(), tree.getLabel(), tree.getValue());
            nodeMap.put(tree.getLabel() + tree.getValue(), newNode);
            mergedForest.addChild(newNode);
            for (ValueDomainVO child : tree.getChildren()) {
                mergeTree(newNode, child, nodeMap);
            }
        }
    }


    /**
     * 生成症候群唯一code
     * 采用自增处理
     * */
    public String generateSyndromeCode(int len, List<String> codeList){

        if(CollectionUtils.isNotEmpty(codeList)) {
            //过滤包含字母的diseaseCode，并转integer
            List<Integer> numList = codeList.stream()
                                            .filter(this::isAllDigits)
                                            .map(Integer::parseInt)
                                            .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(numList)) {
                int max = Collections.max(numList);
                return String.format("%0" + len + "d", max + 1);
            }
        }
        return String.format("%0" + len + "d", startNum);
    }

    /**
     * 判断字符串是否全为数字
     * */
    public boolean isAllDigits(String str){

        Pattern pattern = Pattern.compile("^\\d+$");
        Matcher matcher = pattern.matcher(str);
        return matcher.matches();
    }

    /**
     * 将树的叶子节点的children全部设为null
     * */
    public void setChildrenToNull(ValueDomainVO node) {
        if (node.getChildren() == null || node.getChildren().isEmpty()) {
            node.setChildren(null);
        } else {
            for (ValueDomainVO child : node.getChildren()) {
                setChildrenToNull(child);
            }
        }
    }

    /**
     * 根据节点list构造疾病树
     * */
    public void buildDiseaseTree(DiseaseTreeNode currNode, List<DiseaseTreeNode> treeNodeList){

        List<DiseaseTreeNode> childNodes = new ArrayList<>();
        for (DiseaseTreeNode treeNode : treeNodeList) {
            if(Objects.equals(currNode.getId(), treeNode.getDiseaseParentId())){
                buildDiseaseTree(treeNode, treeNodeList);
                childNodes.add(treeNode);
            }
        }
        currNode.setChildNodes(childNodes);
    }

    /**
     * 遍历森林，取出指定节点以及该节点的所有子节点
     * */
    public List<String> getForestNodeByName(String name, List<DiseaseTreeNode> treeNodeList){

        List<String> codeList = new ArrayList<>();
        for (DiseaseTreeNode treeNode : treeNodeList) {
            getMatchNodeByName(codeList, treeNode, name);
        }
        return codeList;
    }

    /**
     * 遍历树，找到名称匹配的节点
     * */
    private void getMatchNodeByName(List<String> codeList, DiseaseTreeNode root, String name){

        if(root == null){
            return;
        }
        if(Objects.equals(name, root.getDiseaseName())){
            addAllNodes(codeList, root);
        }else{
            for (DiseaseTreeNode childNode : root.getChildNodes()) {
                getMatchNodeByName(codeList, childNode, name);
            }
        }
    }

    /**
     * 遍历树，取出该树中所有节点
     * */
    private void addAllNodes(List<String> codeList, DiseaseTreeNode root) {
        if (root == null) {
            return;
        }
        codeList.add(root.getDiseaseCode());
        for (DiseaseTreeNode treeNode : root.getChildNodes()) {
            addAllNodes(codeList, treeNode);
        }
    }

    /**
     * 通用手动分页
     * */
    public <T> PageInfo<T> pageList(List<T> list, Integer pageIndex, Integer pageSize) {

        if(CollectionUtils.isEmpty(list)) {
            return new PageInfo<>(new ArrayList<>());
        }
        PageInfo<T> pageInfo = new PageInfo<>();
        //数据量大小
        int total = list.size();
        //页数
        Integer pageTotal = getPages(total, pageSize);
        //开始索引
        int fromIndex;
        //结束索引
        int toIndex;
        if (!pageIndex.equals(pageTotal)) {
            fromIndex = (pageIndex - 1) * pageSize;
            toIndex = fromIndex + pageSize;
        } else {
            fromIndex = (pageIndex - 1) * pageSize;
            toIndex = total;
        }
        List<T> subList = list.subList(fromIndex, toIndex);
        pageInfo.setPageNum(pageIndex);
        pageInfo.setPageSize(pageSize);
        pageInfo.setTotal(total);
        pageInfo.setPages(pageTotal);
        pageInfo.setList(subList);
        return pageInfo;
    }

    private Integer getPages(Integer total, Integer pageSize) {

        try {
            int result = total % pageSize;
            int page = total / pageSize;
            return result == 0 ? page : page + 1;
        }catch (Exception e) {
            log.info("Pagination parameter exception");
            throw new MedicalBusinessException("参数异常");
        }
    }

    /**
     * CommonQuery
     * 集成该类的查询参数 统一设置区划查询
     * */
    public <T extends CommonQuery> void setAreaQueryDTO(T dto){

        if(org.apache.commons.lang.StringUtils.isNotBlank(dto.getProvinceCode())){
            dto.setAreaLevel(AreaLevelEnum.PROVINCE.getValue());
        }
        if(org.apache.commons.lang.StringUtils.isNotBlank(dto.getCityCode())){
            dto.setAreaLevel(AreaLevelEnum.CITY.getValue());
        }
        if(org.apache.commons.lang.StringUtils.isNotBlank(dto.getDistrictCode())){
            dto.setAreaLevel(AreaLevelEnum.DISTRICT.getValue());
        }
        //如果没有省市区的传参 则根据用户的机构来set区域参数
        if(org.apache.commons.lang.StringUtils.isBlank(dto.getProvinceCode())
                && org.apache.commons.lang.StringUtils.isBlank(dto.getCityCode())
                && org.apache.commons.lang.StringUtils.isBlank(dto.getDistrictCode())){
            setUserAreaCode(dto,
                            T::setProvinceCodes,
                            T::setCityCodes,
                            T::setDistrictCodes,
                            T::setAreaLevel);
        }
    }

    /**
     * 设置用户的区划code
     * */
    private <T> void setUserAreaCode(T t,
                                     BiConsumer<T, List<String>> province,
                                     BiConsumer<T, List<String>> city,
                                     BiConsumer<T, List<String>> district,
                                     BiConsumer<T, Integer> areaLevel){

        UapUserPo uapUserPo = USER_INFO.get();
        if(uapUserPo == null){
            log.error("当前用户信息为空");
            return;
        }
        List<String> provinceCodes = new ArrayList<>();
        List<String> cityCodes = new ArrayList<>();
        List<String> districtCodes = new ArrayList<>();
        if(org.apache.commons.lang.StringUtils.isNotBlank(uapUserPo.getOrgProvinceCode())){
            provinceCodes = this.getSplitList(uapUserPo.getOrgProvinceCode());
            areaLevel.accept(t, AreaLevelEnum.PROVINCE.getValue());
        }
        if (org.apache.commons.lang.StringUtils.isNotBlank(uapUserPo.getOrgCityCode())) {
            cityCodes = this.getSplitList(uapUserPo.getOrgCityCode());
            areaLevel.accept(t, AreaLevelEnum.CITY.getValue());
        }
        if (StringUtils.isNotBlank(uapUserPo.getOrgDistrictCode())) {
            districtCodes = this.getSplitList(uapUserPo.getOrgDistrictCode());
            areaLevel.accept(t, AreaLevelEnum.DISTRICT.getValue());
        }
        province.accept(t, provinceCodes);
        city.accept(t, cityCodes);
        district.accept(t, districtCodes);
    }

    private List<String> getSplitList(String source){
        if (StringUtil.isEmpty(source)) {
            return new ArrayList<>();
        }
        return new ArrayList<>(Arrays.asList(source.split(","))) ;
    }

}
