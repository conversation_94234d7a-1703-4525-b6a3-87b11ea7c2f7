package com.iflytek.cdc.admin.enums;

/**
 * 编码映射表名
 * <AUTHOR>
 */
public enum CodeTableEnum {

    /**
     * 症候群
     **/
    SYNDROME_INFO("cdcmr-zhq-code","tb_cdcmr_syndrome_info_copy"),

    /**
     * 传染病病种
     **/
    INFECT_DIS("cdcmr-infecdis-code","tb_cdcmr_infectious_diseases_copy"),

    /**
     * 传染病诊断
     **/
    INFECT_DIA("cdcmr-infecdia-code","tb_cdcmr_infectious_diagnosis_copy"),
    ;

    private  String  code;

    private  String  tableName;

    CodeTableEnum(String code, String tableName) {
        this.code = code;
        this.tableName = tableName;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public static  String  getTableName(String code){
        for (CodeTableEnum table:CodeTableEnum.values()) {
            if(table.getCode().equals(code)){
                return table.getTableName();
            }
        }
        return null;
    }
}
