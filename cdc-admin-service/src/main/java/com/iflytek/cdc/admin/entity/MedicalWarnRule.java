package com.iflytek.cdc.admin.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;

import java.util.Date;

@Data
@TableName("tb_cdcmr_medical_warn_rule")
public class MedicalWarnRule {

    @TableId
    @ApiModelProperty(value="id")
    private String id;

    @ApiModelProperty(value="规则id")
    @TableField("rule_id")
    private String ruleId;

    @ApiModelProperty(value="规则所属区域代码")
    @TableField("region_id")
    private String regionId;

    @ApiModelProperty(value="病种编码")
    @TableField("disease_code")
    private String diseaseCode;

    @ApiModelProperty(value="病种名称")
    @TableField("disease_name")
    private String diseaseName;

    @ApiModelProperty(value="时间范围单位(1=天，2=周，3=月)")
    @TableField("time_unit")
    private String timeUnit;

    @ApiModelProperty(value="时间运算规则(1=小于等于，2=小于，3=大于等于，4=大于，5=等于)")
    @TableField("time_operate")
    private String timeOperate;

    @ApiModelProperty(value="时间范围数量")
    @TableField("time_scope")
    private Integer timeScope;

    @ApiModelProperty(value="监测对象(1=医疗机构，2=学校，3=街道)")
    @TableField("monitor_object")
    private String monitorObject;

    @ApiModelProperty(value="病例运算规则(1=小于等于，2=小于 3=大于等于，4=大于，5=等于)")
    @TableField("medical_operate")
    private String medicalOperate;

    @ApiModelProperty(value="病例数量")
    @TableField("medical_count")
    private Integer medicalCount;

    @ApiModelProperty(value="病例属性(1=确诊病例，2=疑似病例，9=死亡病例)")
    @TableField("medical_attribute")
    private String medicalAttribute;

    @ApiModelProperty(value="警示类型(1=预警信号，2=上报提示)")
    @TableField("warn_type")
    private String warnType;

    @ApiModelProperty(value="生成规则json")
    @TableField("rule_json")
    private String ruleJson;

    @TableField("create_datetime")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createDatetime;

    @TableField("creator")
    private String creator;

    @TableField("update_datetime")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateDatetime;

    @TableField("updator")
    private String updator;

    @TableField("delete_flag")
    private String deleteFlag;

    @TableField("type")
    private String type;

    /**
     * 自定义预警规范
     */
    @TableField("customized_item_json")
    private String customizedItemJson;
}
