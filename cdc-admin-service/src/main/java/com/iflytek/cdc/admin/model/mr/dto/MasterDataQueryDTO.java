package com.iflytek.cdc.admin.model.mr.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MasterDataQueryDTO {

    @ApiModelProperty(value = "主数据类型")
    @NotNull
    private String masterDataType;

    @ApiModelProperty(value = "查询数据id")
    private String masterDataId;

    @ApiModelProperty(value = "查询数据code")
    private String masterDataCode;

    @ApiModelProperty(value = "查询主数据 类型code")
    private String masterDataTypeCode;

    public MasterDataQueryDTO(String masterDataType, String masterDataId){
        this.masterDataType = masterDataType;
        this.masterDataId = masterDataId;
    }
}
