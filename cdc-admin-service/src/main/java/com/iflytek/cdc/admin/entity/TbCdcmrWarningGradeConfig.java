package com.iflytek.cdc.admin.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * tb_cdcmr_warning_grade_config
 * <AUTHOR>
@Data
public class TbCdcmrWarningGradeConfig implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 配置类型
     */
    @ApiModelProperty(value = "配置类型")
    private String configType;

    /**
     * 元素名称
     */
    @ApiModelProperty(value = "元素名称")
    private String diseaseName;

    /**
     * 元素编码
     */
    @ApiModelProperty(value = "元素编码")
    private String diseaseCode;

    /**
     * 启用标识
     */
    @ApiModelProperty(value = "启用标识")
    private Integer status;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 更新用户
     */
    @ApiModelProperty(value = "更新用户")
    private String updateUser;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    private static final long serialVersionUID = 1L;
}