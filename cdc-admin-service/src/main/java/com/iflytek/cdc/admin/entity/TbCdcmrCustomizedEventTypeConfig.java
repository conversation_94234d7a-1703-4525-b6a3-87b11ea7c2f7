package com.iflytek.cdc.admin.entity;

import lombok.Data;

@Data
public class TbCdcmrCustomizedEventTypeConfig {

    /**
     * id
     */
    private String id;

    /**
     * 预警类型
     */
    private String warningType;

    /**
     * 数据来源
     */
    private String monitorDataSource;

    /**
     * 类型名称
     */
    private String eventTypeName;

    /**
     * 类型
     */
    private String eventType;

    /**
     * 字段的表名简称
     */
    private String statDimPrefix;

    /**
     * statDimeId 取值字段
     */
    private String statDimIdField;

    /**
     * statDimName 取值字段
     */
    private String statDimNameColumn;

    /**
     * 地址关联的表的名称
     */
    private String  statJoinTableName;

    /**
     * 关联字段
     */
    private String joinColumn;

    /**
     * 实际地址编码取值字段
     */
    private String actualAddressCodeField;

    /**
     * 实际地址名称取值字段
     */
    private String actualAddressNameField;

    /**
     * 是否是地址监测类型
     */
    private boolean addressMonitorFlag;

}
