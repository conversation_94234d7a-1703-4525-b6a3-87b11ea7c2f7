package com.iflytek.cdc.admin.util.brief;


import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.IsoFields;
import java.time.temporal.TemporalAdjusters;
import java.time.temporal.WeekFields;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * 周期时间util
 *
 * <AUTHOR>
 * @date 2025/01/02
 */
public class CycleTimeUtil {

    /**
     * 获取指定年份的开始和结束时间
     *
     * @param year 年份
     * @return 包含开始和结束时间的数组
     */
    public static LocalDate[] getYearStartEnd(int year) {
        LocalDate start = LocalDate.of(year, 1, 1);
        LocalDate end = LocalDate.of(year, 12, 31);
        return new LocalDate[]{start, end};
    }

    /**
     * 获取指定年份和月份的开始和结束时间
     *
     * @param year  年份
     * @param month 月份 (1-12)
     * @return 包含开始和结束时间的数组
     */
    public static LocalDate[] getMonthStartEnd(int year, int month) {
        YearMonth yearMonth = YearMonth.of(year, month);
        LocalDateTime start = yearMonth.atDay(1).atStartOfDay();
        LocalDateTime end = yearMonth.atEndOfMonth().atTime(LocalTime.MAX);
        return new LocalDate[]{LocalDate.from(start), LocalDate.from(end)};
    }

    /**
     * 获取指定年份和周数的开始和结束时间
     *
     * @param year       年份
     * @param weekOfYear 周数 (1-52/53)
     * @return 包含开始和结束时间的数组
     */
    public static LocalDate[] getWeekStartEnd(int year, int weekOfYear) {
        LocalDate firstDayOfYear = LocalDate.of(year, 1, 1);
        int dow = firstDayOfYear.getDayOfWeek().getValue();
        int offset = 1 - dow;
        LocalDate firstMonday = firstDayOfYear.plusDays(offset);
        LocalDate startOfWeek = firstMonday.plusWeeks(weekOfYear - 1);
        LocalDate endOfWeek = startOfWeek.plusDays(6);
        LocalDateTime start = startOfWeek.atStartOfDay();
        LocalDateTime end = endOfWeek.atTime(LocalTime.MAX);
        return new LocalDate[]{LocalDate.from(start), LocalDate.from(end)};
    }

    /**
     * 获取指定日期的开始和结束时间
     *
     * @param date 指定日期
     * @return 包含开始和结束时间的数组
     */
    public static LocalDate[] getDayStartEnd(LocalDate date) {
        LocalDateTime start = date.atStartOfDay();
        LocalDateTime end = date.atTime(LocalTime.MAX);
        return new LocalDate[]{LocalDate.from(start), LocalDate.from(end)};
    }

    private final static DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy年M月d日", Locale.CHINA);

    /**
     * 获取日期之间所有日期列表
     *
     * @param startDateStr 开始日期str
     * @param endDateStr   结束日期str
     * @param type         类型
     * @return {@link List }<{@link String }>
     */
    public static List<String> getDatesBetween(String startDateStr, String endDateStr, String type) {

        LocalDate startDate = null;
        LocalDate endDate = null;
        List<String> dates = new ArrayList<>();
        switch (type.toLowerCase()) {
            case "year":
                startDate = LocalDate.parse(startDateStr + "-01-01");
                endDate = LocalDate.parse(endDateStr + "-01-01");
                while (!startDate.isAfter(endDate)) {
                    dates.add(startDate.getYear() + "年");
                    startDate = startDate.plusYears(1);
                }
                break;
            // ========= 新增 季 quarter =========
            case "quarter":
                // quarter 的迭代粒度也是“月”，但步长 3 个月
                LocalDate quarterStart = LocalDate.parse(startDateStr); // 取到月即可
                LocalDate quarterEnd   = LocalDate.parse(endDateStr);
                while (!quarterStart.isAfter(quarterEnd)) {
                    int year = quarterStart.getYear();
                    int q    = quarterStart.get(IsoFields.QUARTER_OF_YEAR);
                    dates.add(year + "年第" + q + "季度");
                    quarterStart = quarterStart.plusMonths(3);
                }
                break;
            case "month":
                startDate = LocalDate.parse(startDateStr + "-01");
                endDate = LocalDate.parse(endDateStr + "-01");
                while (!startDate.isAfter(endDate)) {
                    dates.add(startDate.getYear() + "年" + startDate.getMonthValue() + "月");
                    startDate = startDate.plusMonths(1);
                }
                break;
            case "meadow":
                LocalDate decadeStart = LocalDate.parse(startDateStr);
                LocalDate decadeEnd   = LocalDate.parse(endDateStr);
                // 旬的粒度是“月+旬”，所以先按月遍历
                YearMonth ymStart = YearMonth.from(decadeStart);
                YearMonth ymEnd   = YearMonth.from(decadeEnd);

                YearMonth current = ymStart;
                while (!current.isAfter(ymEnd)) {
                    int year  = current.getYear();
                    int month = current.getMonthValue();
                    int len   = current.lengthOfMonth();   // 该月天数

                    // 上旬 01-10
                    LocalDate d1 = LocalDate.of(year, month, 1);
                    if (!d1.isAfter(decadeEnd) && !d1.plusDays(9).isBefore(decadeStart)) {
                        dates.add(year + "年" + month + "月上旬");
                    }
                    // 中旬 11-20
                    LocalDate d2 = LocalDate.of(year, month, 11);
                    if (!d2.isAfter(decadeEnd) && !d2.plusDays(9).isBefore(decadeStart)) {
                        dates.add(year + "年" + month + "月中旬");
                    }
                    // 下旬 21-月底
                    LocalDate d3 = LocalDate.of(year, month, 21);
                    if (!d3.isAfter(decadeEnd) && !LocalDate.of(year, month, len).isBefore(decadeStart)) {
                        dates.add(year + "年" + month + "月下旬");
                    }
                    current = current.plusMonths(1);
                }
                break;
            case "week":
                startDate = LocalDate.parse(startDateStr);
                endDate = LocalDate.parse(endDateStr);
                dates = getWeeksBetween(startDate, endDate);
                break;
            case "day":
                startDate = LocalDate.parse(startDateStr);
                endDate = LocalDate.parse(endDateStr);
                while (!startDate.isAfter(endDate)) {
                    dates.add(FORMATTER.format(startDate));
                    startDate = startDate.plusDays(1);
                }
                break;
            default:
                throw new IllegalArgumentException("Unsupported type: " + type);
        }
        return dates;
    }

    /**
     * 间隔几周
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return {@link List }<{@link String }>
     */
    public static List<String> getWeeksBetween(LocalDate startDate, LocalDate endDate) {
        startDate = startDate.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        endDate = endDate.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        List<String> weeks = new ArrayList<>();

        WeekFields weekFields = WeekFields.of(Locale.getDefault());
        while (!startDate.isAfter(endDate)) {
            int weekNumber = startDate.get(weekFields.weekOfWeekBasedYear());
            int year = startDate.get(IsoFields.WEEK_BASED_YEAR);
            weeks.add(year + "年第" + weekNumber + "周");
            startDate = startDate.plusWeeks(1);
        }
        return weeks;
    }

    /**
     * 中文日期转换工具
     */
    public static String convertChineseDate(String dateStr) {
        // 定义输入和输出的日期格式
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy年M月d日");
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        LocalDate date = LocalDate.parse(dateStr, inputFormatter);

        return date.format(outputFormatter);
    }

    public static void main(String[] args) {
        System.out.println(convertChineseDate( "2024年1月24日"));
    }

    public static void getMeadowStartEnd(String date, int dYear, int dMonth, int startDay, int endDay) {
        String section = date.substring(date.indexOf("月") + 1, date.indexOf("旬"));
        switch (section) {
            case "上":
                startDay = 1;
                endDay   = 10;
                break;
            case "中":
                startDay = 11;
                endDay   = 20;
                break;
            case "下":
                startDay = 21;
                endDay   = YearMonth.of(dYear, dMonth).lengthOfMonth();
                break;
            default:
                throw new IllegalArgumentException("非法旬段：" + section);
        }
    }
}