package com.iflytek.cdc.admin.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class InfectedWarningEditDTO {

    @ApiModelProperty(value = "id")
    private String id;

    @NotNull
    @ApiModelProperty(value = "疾病名称|预警内容")
    private String diseaseName;

    @ApiModelProperty(value = "备注")
    private String notes;

    @ApiModelProperty(value = "备注")
    private String diseaseParentId;
}
