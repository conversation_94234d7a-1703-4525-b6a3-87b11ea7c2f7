package com.iflytek.cdc.admin.util;

import com.iflytek.cdc.admin.constant.UapOrgConstants;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @date 2021/7/8 18:21
 **/
public class NationUtil {

    public  static  String  getAreaLevel(String provinceCode,String cityCode,String districtCode){
        if (StringUtils.isNotBlank(districtCode)&& StringUtils.isNotBlank(cityCode)&& StringUtils.isNotBlank(provinceCode)){
            return  UapOrgConstants.DISTRICT;
        }else if(StringUtils.isNotBlank(cityCode)&& StringUtils.isBlank(districtCode) &&StringUtils.isNotBlank(provinceCode)){
            return UapOrgConstants.CITY;
        }else if(StringUtils.isBlank(cityCode) && StringUtils.isBlank(districtCode) && StringUtils.isNotBlank(provinceCode)){
            return UapOrgConstants.PROVINCE;
        }
      return null;
    }
}
