package com.iflytek.cdc.admin.entity.dm;

import cn.hutool.core.lang.Pair;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * ;
 * <AUTHOR> dingyuan
 * @date : 2024-8-30
 */
@ApiModel(value = "值域字典 - 值")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TbCdcdmDataDictValue implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id ;

    /**
     * 值域ID
     */
    @ApiModelProperty(value = "值域ID")
    private String dataDictId ;

    /**
     * 值编码
     */
    @ApiModelProperty(value = "值编码")
    private String code ;

    /**
     * 值名称
     */
    @ApiModelProperty(value = "值名称")
    private String name ;

    /**
     * 值描述
     */
    @ApiModelProperty(value = "值描述")
    private String description ;

    /**
     * 父级ID
     */
    @ApiModelProperty(value = "父级ID")
    private String parentId ;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String notes ;

    /**
     * 状态（启用状态 1启用；0未启用）
     */
    @ApiModelProperty(value = "状态（启用状态 1启用；0未启用）")
    private Integer status ;

    /**
     * 删除标识： 0-未删除，1-已删除
     */
    @ApiModelProperty(value = "删除标识： 0-未删除，1-已删除")
    private String deleteFlag ;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime ;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime ;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    private String creatorId ;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名")
    private String creator ;

    /**
     * 更新人id
     */
    @ApiModelProperty(value = "更新人id")
    private String updaterId ;

    /**
     * 更新人name
     */
    @ApiModelProperty(value = "更新人name")
    private String updater ;

    /**
     * 值域种类
     * */
    @ApiModelProperty(value = "值域种类")
    @TableField(exist = false)
    private String type;

    @JsonIgnore
    public Pair<String, String> getIdentifyKey(){
        return new Pair<>(dataDictId, code);
    }


    /**
     * 排序
     */
    private Integer sortOrder;
}
