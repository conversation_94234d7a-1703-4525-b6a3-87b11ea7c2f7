package com.iflytek.cdc.admin.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@ApiModel("事件处理责任人配置表")
@Data
public class TbCdcmrEventChargePerson implements Serializable {
    public static final String TABLE_NAME = "tb_cdcmr_event_charge_person";

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @TableId(value = "id")
    private String id;

    /**
     * 事件类型
     */
    @ApiModelProperty(value = "事件类型")
    @TableField(value = "event_type")
    private String eventType;

    /**
     * 疾病id
     */
    @ApiModelProperty(value = "疾病id")
    @TableField(value = "disease_id")
    private String diseaseId;

    /**
     * 疾病code
     */
    @ApiModelProperty(value = "疾病code")
    @TableField(value = "disease_code")
    private String diseaseCode;

    /**
     * 疾病name
     */
    @ApiModelProperty(value = "疾病name")
    @TableField(value = "disease_name")
    private String diseaseName;

    /**
     * 事件严重等级
     */
    @ApiModelProperty(value = "事件严重等级")
    @TableField(value = "event_level_id")
    private String eventLevelId;

    /**
     * 处理责任人类别
     */
    @ApiModelProperty(value = "处理责任人类别")
    @TableField(value = "deal_person_type")
    private String dealPersonType;

    /**
     * 处理人id
     */
    @ApiModelProperty(value = "处理人信息")
    @TableField(value = "deal_person_info")
    private String dealPersonInfo;


    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField(value = "creator")
    private String creator;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    @TableField(value = "updater")
    private String updater;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 删除标识
     */
    @ApiModelProperty(value = "删除标识")
    @TableField(value = "delete_flag")
    private String deleteFlag;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    @TableField(value = "creator_id")
    private String creatorId;

    /**
     * 修改人id
     */
    @ApiModelProperty(value = "创建人id")
    @TableField(value = "updater_id")
    private String updaterId;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    @TableField(value = "\"status\"")
    private Integer status;

}
