package com.iflytek.cdc.admin.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class MedicalWarnRuleExportDto {

    @ApiModelProperty(value="是否全选(0:否   1:是)")
    private String isAll;

    @ApiModelProperty(value="导出时选中的id列表")
    private List<String> ids;

    @ApiModelProperty(value="病种code")
    private String diseaseCode;

    @ApiModelProperty(value="规则状态")
    private String status;
}
