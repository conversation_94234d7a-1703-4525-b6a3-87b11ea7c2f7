package com.iflytek.cdc.admin.mapper.province;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrEndemicMonitorConfig;
import com.iflytek.cdc.admin.model.mr.dto.EndemicMonitorConfigQueryDTO;
import com.iflytek.cdc.admin.model.mr.vo.DiagnoseListVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbCdcmrEndemicMonitorConfigMapper extends BaseMapper<TbCdcmrEndemicMonitorConfig> {
    List<TbCdcmrEndemicMonitorConfig> queryByDiseaseInfoId(EndemicMonitorConfigQueryDTO queryDTO);

    void updateByEndemicInfoId(String diseaseInfoId);

    void batchInsert(@Param("monitorConfigs") List<TbCdcmrEndemicMonitorConfig> monitorConfigs);

    List<DiagnoseListVO> getDiagnoseCodeList();
}