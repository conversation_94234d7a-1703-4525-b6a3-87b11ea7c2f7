package com.iflytek.cdc.admin.dto;

import com.iflytek.cdc.admin.entity.TbCdcmrPreventionControlWarnRule;
import com.iflytek.cdc.admin.entity.TbCdcmrUnknownReasonWarnRule;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class PreventionControlWarnDto {

    /**
     * 主键
     */
    private String id;

    /**
     * 联防联控编码
     */
    private String preventionControlCode;

    /**
     * 联防联控名称
     */
    private String preventionControlName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 开启状态：0禁用1启用
     */
    private Integer status;

    /**
     * 删除标识：0未删除1删除
     */
    private Integer isDeleted;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 生命周期(天)
     */
    private Integer maxLifeCycle;


    /**
     * 预警短信发送类型编码
     */
    private String smsSendTypeCode;

    /**
     * 预警短信发送类型描述
     */
    private String smsSendTypeDesc;

    private List<TbCdcmrPreventionControlWarnRule> ruleList;

    Integer ruleCount;
}
