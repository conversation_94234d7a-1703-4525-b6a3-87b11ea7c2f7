package com.iflytek.cdc.admin.dto.amap.inputtips;

import java.util.List;

public class Tips {

    private String id;
    private String name;
    private String district;
    private String adcode;
    private String location;
    private String address;
    private String typecode;
    private List<String> city;
    public void setId(String id) {
        this.id = id;
    }
    public String getId() {
        return id;
    }

    public void setName(String name) {
        this.name = name;
    }
    public String getName() {
        return name;
    }

    public void setDistrict(String district) {
        this.district = district;
    }
    public String getDistrict() {
        return district;
    }

    public void setAdcode(String adcode) {
        this.adcode = adcode;
    }
    public String getAdcode() {
        return adcode;
    }

    public void setLocation(String location) {
        this.location = location;
    }
    public String getLocation() {
        return location;
    }

    public void setAddress(String address) {
        this.address = address;
    }
    public String getAddress() {
        return address;
    }

    public void setTypecode(String typecode) {
        this.typecode = typecode;
    }
    public String getTypecode() {
        return typecode;
    }

    public void setCity(List<String> city) {
        this.city = city;
    }
    public List<String> getCity() {
        return city;
    }

}