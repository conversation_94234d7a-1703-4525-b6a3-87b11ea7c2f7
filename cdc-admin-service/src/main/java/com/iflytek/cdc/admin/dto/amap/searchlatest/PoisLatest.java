
package com.iflytek.cdc.admin.dto.amap.searchlatest;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName PoisLatest
 * @Description 高德地图 搜索服务-关键字查询 返回值解析类
 * <AUTHOR>
 * @Date 2021/9/16 9:34
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PoisLatest implements Serializable {

    private static final long serialVersionUID = 5977127520062744620L;

    /**
     * 标准地址 地址名
     */
    private Object name;

    /**
     * 地址描述所在的 省名
     */
    private Object pname;

    /**
     * 地址描述所在的 区县名
     */
    private Object cityname;

    /**
     * 地址描述所在的 区县名
     */
    private Object adname;


    /**
     * 标准地址 地址经纬度
     */
    private Object location;

    /**
     * 标准地址 POI类型
     */
    private Object type;

    /**
     * 标准地址编码
     */
    private Object id;

    /**
     * 标准地址描述
     */
    private Object address;

    public String getName() {
        if (name instanceof String) {
            return (String) name;
        }
        return "";
    }

    public String getPname() {
        if (pname instanceof String) {
            return (String) pname;
        }
        return "";
    }

    public String getCityname() {
        if (cityname instanceof String) {
            return (String) cityname;
        }
        return "";
    }

    public String getAdname() {
        if (adname instanceof String) {
            return (String) adname;
        }
        return "";
    }

    public String getLocation() {
        if (location instanceof String) {
            return (String) location;
        }
        return "";
    }

    public String getType() {
        if (type instanceof String) {
            return (String) type;
        }
        return "";
    }

    public String getId() {
        if (id instanceof String) {
            return (String) id;
        }
        return "";
    }

    public String getAddress() {
        if (address instanceof String) {
            return (String) address;
        }
        return "";
    }
}