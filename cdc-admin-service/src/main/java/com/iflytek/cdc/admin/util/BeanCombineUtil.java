package com.iflytek.cdc.admin.util;

import org.apache.poi.ss.formula.functions.T;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;

/**
 * <AUTHOR>
 * @date 2021/7/8 17:20
 **/
public class BeanCombineUtil {
    /**
     * * 该方法是用于相同对象不同属性值的合并
     * * 如果两个相同对象中同一属性都有值，那么sourceBean中的值会覆盖targetBean重点的值
     * * 如果sourceBean有值，targetBean没有，则采用sourceBean的值
     * * 如果sourceBean没有值，targetBean有，则保留targetBean的值
     *
     * @param sourceBean 被提取的对象bean
     * @param targetBean 用于合并的对象bean
     * @return targetBean, 合并后的对象
     */
    public static <T> T combineCore(T sourceBean, T targetBean) {
        //反射获取对象
        Class<?> sourceBeanClass = sourceBean.getClass();
        Class<?> targetBeanClass = targetBean.getClass();
        //获取字段属性
        Field[] sourceFields = sourceBeanClass.getDeclaredFields();
        Field[] targetFields = targetBeanClass.getDeclaredFields();
        //判断字段属性
        for (int i = 0; i < sourceFields.length; i++) {
            Field sourceField = sourceFields[i];
            //如果是静态属性，则跳过
            if (Modifier.isStatic(sourceField.getModifiers())) {
                continue;
            }
            Field targetField = targetFields[i];
            if (Modifier.isStatic(targetField.getModifiers())) {
                continue;
            }
            //设置可更改属性
            sourceField.setAccessible(true);
            targetField.setAccessible(true);
            try {
                //判断属性字段是否为空
                if ((sourceField.get(sourceBean) != null) && !"serialVersionUID".equals(sourceField.getName().toString())) {
                    targetField.set(targetBean, sourceField.get(sourceBean));
                }
            } catch (IllegalArgumentException | IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        return targetBean;
    }


    /**
     * Bean对象去除属性NULL值并赋予默认值
     *
     * @param obj
     * @throws Exception
     */
    public static <T> T setFieldValueNotNull(T obj) throws Exception {
        for (Field field : obj.getClass().getDeclaredFields()) {
            field.setAccessible(true);
            if (field.get(obj) == null) {
                if ("class java.lang.String".equals(field.getGenericType().toString())) {
                    field.set(obj, "");
                } else if ("class java.lang.Integer".equals(field.getGenericType().toString())) {
                    field.set(obj, 0);
                } else if ("class java.lang.Double".equals(field.getGenericType().toString())) {
                    field.set(obj, 0.0);
                } else if ("class java.lang.Long".equals(field.getGenericType().toString())) {
                    field.set(obj, 0L);
                }
            }
        }
        return obj;
    }


    /**
     * Bean 对象 替换默认值
     *
     * @param obj
     * @throws Exception
     */
    public static <T> T setFieldValueReplace(T obj, Object regexp, Object replacement) throws Exception {
        for (Field field : obj.getClass().getDeclaredFields()) {
            field.setAccessible(true);
            if (field.get(obj).equals(regexp)) {
                field.set(obj, replacement);
            }
        }
        return obj;
    }
}
