package com.iflytek.cdc.admin.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 状态枚举
 * */
@Getter
public enum StatusEnum implements BaseEnum{

    STATUS_ON("1", "已开启"),

    STATUS_OFF("0", "未开启")
    ;

    private final String code;
    private final String desc;

    StatusEnum(String code, String desc) {

        this.code = code;
        this.desc = desc;
    }

    public static Map<String, Object> mapValues() {
        return Arrays.stream(values()).collect(Collectors.toMap(StatusEnum::name, s -> s));
    }

    public static String getValueByCode(String code) {
        if (StringUtils.isBlank(code)){
            return null;
        }else {
            return Arrays.stream(StatusEnum.values())
                    .filter(item -> item.getCode().equals(code))
                    .findFirst()
                    .map(StatusEnum::getDesc)
                    .orElse(null);
        }
    }
}
