package com.iflytek.cdc.admin.mapper.province;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.admin.dto.RiskLevelDetailQueryDTO;
import com.iflytek.cdc.admin.entity.TbCdcmrWarningRiskLevelDetail;
import com.iflytek.cdc.admin.model.mr.dto.RiskDetailQueryDTO;
import com.iflytek.cdc.admin.vo.RiskLevelDetailVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 风险等级详情配置表 Mapper 接口
 */
public interface TbCdcmrWarningRiskLevelDetailMapper extends BaseMapper<TbCdcmrWarningRiskLevelDetail> {

    List<RiskLevelDetailVO> listRiskLevelDetail(RiskLevelDetailQueryDTO queryDTO);

    RiskLevelDetailVO loadByDiseaseIdAndLevelCode(String diseaseId, String riskLevelCode , String warningType);

    RiskLevelDetailVO loadByDiseaseIdAndLevelId(String diseaseId, String riskLevelId);

    List<TbCdcmrWarningRiskLevelDetail> getRiskLevelDetailBy(@Param("idList") List<String> idList);
    
    void deleteDetailInfoBy(@Param("diseaseInfoIdList") List<String> diseaseInfoIdList,
                            @Param("diseaseCodeList") List<String> diseaseCodeList);

    /**
     * 批量插入detail数据
     * */
    void insertBatch(@Param("detailList") List<TbCdcmrWarningRiskLevelDetail> detailList);

}
