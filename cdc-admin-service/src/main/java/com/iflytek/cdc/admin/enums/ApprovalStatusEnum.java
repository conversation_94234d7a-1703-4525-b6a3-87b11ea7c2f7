package com.iflytek.cdc.admin.enums;

import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Getter
@Slf4j
public enum ApprovalStatusEnum {

    /**
     * 待审批
     */
    PENDING_APPROVAL("PENDING_APPROVAL", "待审批"),

    /**
     * 待二级审批
     */
    PENDING_SECOND_APPROVAL("PENDING_SECOND_APPROVAL", "待审批"),

    /**
     * 申请通过
     */
    APPROVED("APPROVED", "申请通过"),

    /**
     * 申请驳回
     */
    REJECTED("REJECTED", "申请驳回"),

    /**
     * 无需审批
     */
    NO_APPROVAL_REQUIRED("NO_APPROVAL_REQUIRED", "无需审批");
    private final String code;

    private final String description;

    ApprovalStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    // 根据 code 获取枚举值
    public static ApprovalStatusEnum getByCode(String code) {
        for (ApprovalStatusEnum status : ApprovalStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        log.error("无效的审批状态: {}", code);
        throw new MedicalBusinessException("无效的审批状态");
    }

    public boolean codeEquals(String code) {
        return this.code.equals(code);
    }
}
