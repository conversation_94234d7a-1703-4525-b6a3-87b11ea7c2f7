
package com.iflytek.cdc.admin.dto;

import com.iflytek.cdc.admin.entity.TbCdcmrFzUapOrganization;
import com.iflytek.cdc.admin.entity.UapOrganization;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * nifi机构树权限，org改动dto
 * <AUTHOR>
 * @Date 2021/10/19
 **/
@ApiModel("插件机构数改动DTO")
@Data
public class PlunOrgChangeDto extends UapOrganization {

    @ApiModelProperty("改动类型，u修改，c新建，d删除")
    private String changeType;



}
