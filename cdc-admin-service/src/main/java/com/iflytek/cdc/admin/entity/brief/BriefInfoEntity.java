package com.iflytek.cdc.admin.entity.brief;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * ${comments}
 * 
 * <AUTHOR>
 * @date 2024-12-30 14:52:02
 */
@Data
@TableName("tb_cdcbr_brief_info")
public class BriefInfoEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@TableId
	private String id;
	/**
	 * 模板标题
	 */
	private String title;
	/**
	 * 分析对象
	 */
	private String analysisObject;
	/**
	 * 内容
	 */
	private String content;
	/**
	 * 业务类型：传染病；XX症候群
	 */
	private String businessType;
	/**
	 * 省编码
	 */
	private String provinceCode;
	/**
	 * 省名称
	 */
	private String provinceName;
	/**
	 * 市编码
	 */
	private String cityCode;
	/**
	 * 市名称
	 */
	private String cityName;
	/**
	 * 区编码
	 */
	private String districtCode;
	/**
	 * 区名称
	 */
	private String districtName;
	/**
	 * 统计具体时间（年；月；周；日）
	 */
	private String statisticsTime;
	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createTime;

	private String statisticsCycle;
	/**
	 * 附件标题
	 */
	private String attachmentTitle;
	private String templateId;

}
