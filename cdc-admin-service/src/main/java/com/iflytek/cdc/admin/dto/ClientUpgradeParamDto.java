package com.iflytek.cdc.admin.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName ClientUpgradeParam
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/7/8 10:12
 * @Version 1.0
 */

@Data
public class ClientUpgradeParamDto extends  QueryListDTO{

    /**
     * 主键id
     */
    private String id;

    /**
     * 机构code
     */
    private String orgCode;

    /**
     * 机构name
     */
    private String orgName;

    /**
     * 版本号
     */
    private String versionCode;

    /**
     * 版本类型
     */
    private String versionType;

    /**
     * 维度查询类型
     */
    private String queryType;

    /**
     * 是否为待升级版本
     */
    private String isUpgradeVersion;

    /**
     * 机构code集合
     **/
    private List<String> orgCodes;
}
