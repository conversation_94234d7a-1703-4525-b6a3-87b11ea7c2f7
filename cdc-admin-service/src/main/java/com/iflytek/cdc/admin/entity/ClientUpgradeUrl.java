package com.iflytek.cdc.admin.entity;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * @ClassName ClientUpgradeUrl
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/6/30 14:16
 * @Version 1.0
 */
/**
    * 机构版本安装包管理表
    */
@Data
@Builder
public class ClientUpgradeUrl implements Serializable {

    @ApiModelProperty(value = "主键id")
    private String id;

    @ApiModelProperty(value = "区域code")
    private String areaCode;

    @ApiModelProperty(value = "区域name")
    private String areaName;

    @ApiModelProperty(value = "机构编码")
    private String orgCode;

    @ApiModelProperty(value = "机构name")
    private String orgName;

    @ApiModelProperty(value = "安装包服务器地址")
    private String packageFileUrl;

    @ApiModelProperty(value = "是否删除")
    private String isDelete;

    @ApiModelProperty(value = "创建者")
    private String createUser;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新者")
    private String updateUser;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}