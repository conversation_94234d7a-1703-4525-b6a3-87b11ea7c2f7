package com.iflytek.cdc.admin.util;

import cn.hutool.json.JSONUtil;
import com.iflytek.cdc.admin.service.RequestApiLogService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.function.Predicate;

@Service
public class RequestApiUtils {
    private static final String SUCCESS = "success";
    private static final String FAIL = "fail";

    @Resource
    private RequestApiLogService requestApiLogService;

    /**
     * 执行并记载
     */
    public  <T> T executeAndLog(String requestId,
                                String requestUrl,
                                Object requestParam,
                                String loginUserId,
                                Request<T> request){
        return executeAndLog(requestId, requestUrl, requestParam, loginUserId, request, t -> true);
    }

    /**
     * 执行并记载
     */
    public  <T> T executeAndLog(String requestId,
                                String requestUrl,
                                Object requestParam,
                                String loginUserId,
                                Request<T> request,
                                Predicate<T> needLog){
        String status = SUCCESS;
        String response;
        String error = null;
        T t = null;
        try {
            t = request.execute();
            response = JSONUtil.toJsonStr(t);
        }catch (Exception e){
            status = FAIL;
            response = e.getLocalizedMessage();
            error = e.getLocalizedMessage();
        }
        if (FAIL.equals(status) || needLog.test(t)){
            requestApiLogService.saveLog(requestId, requestUrl, status, requestParam,response, loginUserId);
        }
        if (FAIL.equals(status)){
            throw new MedicalBusinessException("调用失败：" + error);
        }
        return t;
    }


    public interface Request<T>{
        T execute();

    }


}
