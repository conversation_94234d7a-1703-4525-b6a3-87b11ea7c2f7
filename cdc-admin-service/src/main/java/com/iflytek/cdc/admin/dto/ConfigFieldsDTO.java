package com.iflytek.cdc.admin.dto;

import com.iflytek.cdc.admin.enums.LogicEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ConfigFieldsDTO {

    @ApiModelProperty(value = "展示名称")
    private String title;

    @ApiModelProperty(value = "表id")
    private String tableId;

    @ApiModelProperty(value = "表code")
    private String tableCode;

    @ApiModelProperty(value = "表名称")
    private String table;

    @ApiModelProperty(value = "表schema")
    private String schema;

    @ApiModelProperty(value = "选择模型id")
    private String modelId;

    @ApiModelProperty(value = "选择模型中的表单id")
    private String formId;

    @ApiModelProperty(value = "选择模型中表单的组id")
    private String groupId;

    @ApiModelProperty(value = "字段id")
    private String fieldId;

    @ApiModelProperty(value = "字段编码")
    private String fieldCode;

    @ApiModelProperty(value = "字段名称")
    private String field;

    @ApiModelProperty(value = "字段展示时排序")
    private Integer orderFlag;

    @ApiModelProperty(value = "字段是否展示")
    private String isDisplay;

    @ApiModelProperty(value = "该条数据类型：筛选条件condition/普通字段field")
    private String classify;

    @ApiModelProperty(value = "字段类型")
    private String fieldType;

    @ApiModelProperty(value = "当词条数据为条件时，才有值")
    private String value;

    @ApiModelProperty(value = "自定义过滤条件中的逻辑关系")
    private LogicEnum logic;

    @ApiModelProperty(value = "自定义过滤条件中操作符")
    private String operator;

    @ApiModelProperty(value = "脱敏配置")
    private String desensitizedType;

}