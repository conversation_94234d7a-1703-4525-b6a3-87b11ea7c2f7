package com.iflytek.cdc.admin.enums;

import java.util.*;

public enum SchoolSymptomMonitorObjectEnum {
    DORMITORY("宿舍", 1),
    CLASS("班级", 2),

    GRADE("年级", 3),

    SCHOOL("校区", 4),

    STREET("街道", 5);

    private final String name;

    private final Integer code;

    SchoolSymptomMonitorObjectEnum(String name, Integer code) {
        this.code = code;
        this.name = name;
    }

    public static List<Map<String, Object>> getAllToList() {
        List<Map<String, Object>> list = new ArrayList<>();
        SchoolSymptomMonitorObjectEnum[] values = values();
        for (SchoolSymptomMonitorObjectEnum schoolSymptomMonitorObjectEnum : values) {
            Map<String, Object> valuesMap = new HashMap<>();
            valuesMap.put("name", schoolSymptomMonitorObjectEnum.name);
            valuesMap.put("code", schoolSymptomMonitorObjectEnum.code);
            list.add(valuesMap);
        }
        return list;
    }
}
