package com.iflytek.cdc.admin.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class InspectionMappingPageVO {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "病种code")
    private String diseaseCode;

    @ApiModelProperty(value = "病种名称")
    private String diseaseName;

    @ApiModelProperty(value = "规则状态")
    private String status;

    @ApiModelProperty(value = "病种描述")
    private String ruleDesc;

    @ApiModelProperty(value = "映射数量")
    private String mappingCount;

    @ApiModelProperty(value = "病种父类code")
    private String diseaseParentCode;

    @ApiModelProperty(value = "病种类型 1.甲类2.乙类3.丙类9.其他")
    private String diseaseTypeCode;

    @ApiModelProperty(value = "业务分类 1.一级 2.二级")
    private String businessType;

    @ApiModelProperty(value = "传染病子集")
    private List<InspectionMappingPageVO> subList;
}
