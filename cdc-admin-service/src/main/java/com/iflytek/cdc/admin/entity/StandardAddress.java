package com.iflytek.cdc.admin.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/8/27 14:11
 **/
@Data
public class StandardAddress {


    @ApiModelProperty(value = "主键")
    private String  id;

    @ApiModelProperty(value = "地名")
    private  String  addressName;

    @ApiModelProperty(value = "地址描述")
    private  String  addressDesc;

    @ApiModelProperty(value = "经度")
    private  String  addressLongitude;

    @ApiModelProperty(value = "纬度")
    private  String  addressLatitude;

    @ApiModelProperty(value = "创建时间")
    private  Date    createTime;
}
