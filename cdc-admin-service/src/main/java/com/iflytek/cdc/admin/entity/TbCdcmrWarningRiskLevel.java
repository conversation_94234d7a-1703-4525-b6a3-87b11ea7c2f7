package com.iflytek.cdc.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 传染病预警-风险等级配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="TbCdcmrWarningRiskLevel对象", description="传染病预警-风险等级配置表")
public class TbCdcmrWarningRiskLevel extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "风险分级")
    private String riskLevelCode;

    @ApiModelProperty(value = "风险分级名称")
    private String riskLevelName;

    @ApiModelProperty(value = "标记颜色")
    private String highlightColor;

    @ApiModelProperty(value = "备注")
    private String notes;

    @ApiModelProperty(value = "排序字段")
    private Integer sort;

    @ApiModelProperty(value = "状态（启用状态 1启用；0未启用）")
    private Integer status;


}
