package com.iflytek.cdc.admin.mapper;

import com.iflytek.cdc.admin.dto.ParamConfigDTO;
import com.iflytek.cdc.admin.dto.SearchParamDTO;
import com.iflytek.cdc.admin.dto.UpdateOrgInfoDTO;
import com.iflytek.cdc.admin.entity.ParamConfig;
import com.iflytek.cdc.admin.entity.ParamOrg;
import com.iflytek.cdc.admin.entity.UpdateOrgParam;
import com.iflytek.cdc.admin.sdk.pojo.ParamInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ParamConfigMapper {
    /**
     * 根据主键id查询配置信息
     * @param id
     * @return
     **/
    ParamConfig queryById(String id);

    /**
     * 查询参数维度参数列表
     *
     * @param sd
     * @return
     **/
    List<ParamConfig> queryConfigInfo(SearchParamDTO sd);

    /**
     * 查询机构维度参数列表
     *
     * @param sd
     * @return
     **/
    List<ParamOrg> queryConfigOrgInfo(SearchParamDTO sd);


    /**
     * 根据参数编号查询机构数量详情
     *
     * @param sd
     * @return
     **/
    List<ParamOrg> queryConfigOrgByCode(SearchParamDTO sd);

    /**
     * 根据机构id查询参数配置详情
     *
     * @param orgId
     * @param isDelete
     * @return
     **/
    List<ParamConfig> queryConfigInfoByOrg(@Param("isDelete") String isDelete, @Param("OrgId") String orgId);

    /**
     * 添加参数
     *
     * @param pc
     * @return
     **/
    void insertParamConfig(ParamConfigDTO pc);

    /**
     * 修改参数
     *
     * @param pc
     * @return
     **/
    void updateParamConfig(ParamConfigDTO pc);

    /**
     * 批量插入参数机构配置信息
     *
     * @param po
     * @return
     **/
    void insertParamOrg(List<ParamOrg> po);


    /**
     * 修改参数机构配置信息
     * @param up
     * @return
     **/
    void updateParamOrg(UpdateOrgInfoDTO up);


    /**
     * 根据编号查询配置信息
     *
     * @param configCode
     * @param isDelete
     * @return
     **/
    List<ParamConfig> queryParamConfigByCode(@Param("configCode") String configCode, @Param("isDelete") String isDelete);

    /**
     * 根据编号和机构id查询配置机构信息
     *
     * @param configCode
     * @param orgId
     * @return
     **/
    ParamOrg queryOrgInfoByCodeAndId(@Param("configCode") String configCode, @Param("orgId") String orgId,@Param("isDelete") String  isDelete);

    /**
     * 删除机构配置信息
     *
     * @param configCode
     * @param orgId
     * @param isDelete
     * @param updateUser
     * @return
     **/
    void deleteByCodeAndId(@Param("configCode") String configCode, @Param("orgId") String orgId,@Param("isDelete") String isDelete,
                           @Param("updateUser") String updateUser,@Param("updateUserName") String updateUserName);

    /**
     * 查询配置信息
     *
     * @param configCode
     * @return
     **/
    ParamConfig queryOneParamConfigByCode(@Param("configCode") String configCode,@Param("isDelete") String isDelete);


    /**
     * 根据机构编号删除机构配置信息
     * @param uo
     * @return
     **/
    void deleteOrgByCode(UpdateOrgParam  uo);

    /**
     *  删除参数配置信息
     * @param id
     * @param updateUser
     * @param isDelete
     * @return
     **/
    void deleteConfigById(@Param("id") String id,@Param("updateUser") String updateUser,@Param("isDelete") String isDelete);

    ParamConfig   paramConfigInfo(@Param("configCode") String configCode,@Param("isDelete") String isDelete,@Param("configGroup") String configGroup);


    /**
     * 根据编号和机构id查询配置机构信息
     *
     * @param configCode
     * @param orgId
     * @return
     **/
    ParamOrg orgInfo(@Param("configCode") String configCode, @Param("orgId") String orgId,@Param("isDelete") String  isDelete);

    List<ParamInfo>  queryParamByGroup(@Param("configGroup") String configGroup,@Param("isDelete") String isDelete);

    List<ParamConfig>  queryParamByCode(@Param("keyword") String keyword);

    String getStatusByCodeAndGroup(@Param("configCode") String configCode, @Param("configGroup") String configGroup);

}