package com.iflytek.cdc.admin.vo;

import com.google.gson.annotations.SerializedName;
import com.iflytek.cdc.admin.entity.Area;
import com.iflytek.cdc.admin.entity.TbCdcmrPoisoning;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class LabelValueVO implements Comparable<LabelValueVO> {

    @SerializedName("name")
    private String label;
    @SerializedName("id")
    private String value;

    @Override
    public int compareTo(LabelValueVO o) {
        return this.getValue().compareTo(o.getValue());
    }
}

