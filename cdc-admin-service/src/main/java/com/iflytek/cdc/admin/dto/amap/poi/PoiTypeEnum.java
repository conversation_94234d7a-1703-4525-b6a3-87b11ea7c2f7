package com.iflytek.cdc.admin.dto.amap.poi;

/**
 * <AUTHOR>
 */

public enum PoiTypeEnum {

    CLUTURE(1, "科教文化服务","科教文化场所，学校，科研机构，培训机构等");

    private final int code;
    private final String type;
    private final String desc;

    PoiTypeEnum(int code, String type, String desc) {
        this.code = code;
        this.type = type;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }
    public String getType() {
        return type;
    }
    public String getDesc() {
        return desc;
    }

}
