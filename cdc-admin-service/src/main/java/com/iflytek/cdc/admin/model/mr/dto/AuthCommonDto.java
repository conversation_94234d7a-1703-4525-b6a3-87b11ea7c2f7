package com.iflytek.cdc.admin.model.mr.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class AuthCommonDto {

    @ApiModelProperty(
            value = "页码 从1开始",
            example = "1"
    )
    @TableField(exist = false)
    public Integer pageIndex;
    @ApiModelProperty(
            value = "每页条数",
            example = "10"
    )
    @TableField(exist = false)
    public Integer pageSize;

    @ApiModelProperty(value = "系统类型")
    @TableField(exist = false)
    private String systemType;

    @TableField(exist = false)
    private Date currentTime;

    @ApiModelProperty("审批人或申请人标识")
    @TableField(exist = false)
    private String approverOrApplicantFlag;

    @ApiModelProperty("申请开始时间")
    @TableField(exist = false)
    private Date applyStartTime;

    @ApiModelProperty("申请结束时间")
    @TableField(exist = false)
    private Date applyEndTime;


    @ApiModelProperty("审批开始时间")
    @TableField(exist = false)
    private Date approveStartTime;

    @ApiModelProperty("审批结束时间")
    @TableField(exist = false)
    private Date approveEndTime;
}
