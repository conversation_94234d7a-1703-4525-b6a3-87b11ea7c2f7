package com.iflytek.cdc.admin.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel("传染病规则")
public class InfectiousDiseasesDto {
    @NotNull(message = "病种编码不能为空！")
    @ApiModelProperty("病种编码")
    private String diseasesCode;

    @NotNull(message = "病种名称不能为空！")
    @ApiModelProperty("病种名称")
    private String diseasesName;

    @ApiModelProperty("病种类型")
    private String diseasesType;

    @ApiModelProperty("病种类型名称")
    private String diseasesTypeName;

    @ApiModelProperty("业务分类")
    private String diseasesClassify;

    @ApiModelProperty("业务分类名称")
    private String diseasesClassifyName;

    @NotNull(message = "启用状态不能为空！")
    @ApiModelProperty("启用状态")
    private String isEnable;

    @ApiModelProperty("备注")
    private String remark;
}
