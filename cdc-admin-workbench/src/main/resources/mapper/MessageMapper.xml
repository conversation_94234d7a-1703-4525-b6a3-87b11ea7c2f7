<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.workbench.mapper.MessageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.workbench.entity.Message">
        <id column="id" property="id" />
        <result column="app_code" property="appCode" />
        <result column="message_type" property="messageType" />
        <result column="message_name" property="messageName" />
        <result column="message_content" property="messageContent" />
        <result column="send_time" property="sendTime" />
        <result column="create_time" property="createTime" />
        <result column="sender_id" property="senderId" />
        <result column="sender" property="sender" />
        <result column="receiver_id" property="receiverId" />
        <result column="receiver" property="receiver" />
        <result column="request_param" property="requestParam" />
        <result column="source_system_code" property="sourceSystemCode" />
        <result column="source_system_name" property="sourceSystemName" />
        <result column="system_relative_path" property="systemRelativePath" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="status" property="status" />
        <result column="signal_push_configuration_id" property="signalPushConfigurationId" />
        <result column="message_config_id" property="messageConfigId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, app_code, message_type, message_name, message_content, send_time, create_time,update_time, sender_id, sender, receiver_id, receiver, request_param, source_system_code, source_system_name, system_relative_path, delete_flag, status,signal_push_configuration_id, message_config_id
    </sql>
    <update id="read">
        update app.tb_cdcmr_message
        set status = 1,update_time = now()
        where id =#{id}
        and receiver_id=#{receiverId}
    </update>
    <update id="allRead">
        update app.tb_cdcmr_message
        set status = 1,update_time = now()
        where receiver_id=#{receiverId}
    </update>
    <update id="logicDelete">
        update app.tb_cdcmr_message
        set delete_flag='1',update_time = now()
        where receiver_id=#{receiverId} and id=#{id}
    </update>
    <select id="findList" resultType="com.iflytek.cdc.admin.workbench.entity.Message">
        select <include refid="Base_Column_List"/>
        from app.tb_cdcmr_message
        where delete_flag='0'
        and receiver_id=#{receiverId}
        and send_time &lt;= now()
        <if test="messageType != null">
            and message_type = #{messageType}
        </if>
        <if test="status != null">
            and status = #{status}
        </if>
        order by send_time desc, id desc
    </select>
    <select id="getUnreadCount" resultType="java.lang.Integer">
        select count(1) from app.tb_cdcmr_message
        where delete_flag='0' and status=0 and receiver_id=#{receiverId}
    </select>

    <select id="listBySignalPushConfigurationId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from app.tb_cdcmr_message
        where delete_flag='0'  and status=0 and signal_push_configuration_id=#{id}
        order by send_time desc, id desc
    </select>

    <!-- 批量插入消息 -->
    <insert id="saveBatch" parameterType="java.util.List">
        INSERT INTO app.tb_cdcmr_message (
        id,
        app_code,
        message_type,
        message_name,
        message_content,
        send_time,
        create_time,
        update_time,
        sender_id,
        sender,
        receiver_id,
        receiver,
        request_param,
        source_system_code,
        source_system_name,
        system_relative_path,
        delete_flag,
        status,
        signal_push_configuration_id,
        message_config_id
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.appCode},
            #{item.messageType},
            #{item.messageName},
            #{item.messageContent},
            #{item.sendTime},
            #{item.createTime},
            #{item.updateTime},
            #{item.senderId},
            #{item.sender},
            #{item.receiverId},
            #{item.receiver},
            #{item.requestParam},
            #{item.sourceSystemCode},
            #{item.sourceSystemName},
            #{item.systemRelativePath},
            #{item.deleteFlag},
            #{item.status},
            #{item.signalPushConfigurationId},
            #{item.messageConfigId}
            )
        </foreach>
    </insert>

    <update id="updateBatchById">
        UPDATE app.tb_cdcmr_message
        SET
        delete_flag = tmp.delete_flag,
        update_time = tmp.update_time
        FROM (
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.deleteFlag}, #{item.updateTime})
        </foreach>
        ) AS tmp(id, delete_flag, update_time)
        WHERE app.tb_cdcmr_message.id = tmp.id
    </update>

    <select id="listByReceiverAndTaskAndMessageConfig" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM app.tb_cdcmr_message
        WHERE delete_flag = '0'
        AND status = 0
        AND (
        <foreach collection="dtos" item="dto" separator=" OR ">
            (1=1
            <if test="dto.senderId != null and dto.senderId != ''">
                AND receiver_id = #{dto.senderId}
            </if>
            <if test="dto.messageConfigId != null and dto.messageConfigId != ''">
                AND message_config_id = #{dto.messageConfigId}
            </if>
            <if test="dto.taskId != null and dto.taskId != ''">
                AND message_content LIKE CONCAT('%', #{dto.taskId}, '%')
            </if>
            )
        </foreach>
        )
        AND send_time &gt; NOW()
        ORDER BY send_time DESC, id DESC
    </select>

</mapper>
