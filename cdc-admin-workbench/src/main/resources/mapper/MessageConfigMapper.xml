<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.workbench.mapper.MessageConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.workbench.entity.MessageConfig">
        <id column="id" property="id" />
        <result column="app_code" property="appCode" />
        <result column="message_type" property="messageType" />
        <result column="message_name" property="messageName" />
        <result column="message_content" property="messageContent" />
        <result column="source_system_code" property="sourceSystemCode" />
        <result column="source_system_name" property="sourceSystemName" />
        <result column="system_relative_path" property="systemRelativePath" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="creator_id" property="creatorId" />
        <result column="creator" property="creator" />
        <result column="updater_id" property="updaterId" />
        <result column="updater" property="updater" />
        <result column="delete_flag" property="deleteFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, app_code, message_type, message_name, message_content, source_system_code, source_system_name, system_relative_path, create_time, update_time, creator_id, creator, updater_id, updater, delete_flag
    </sql>
    <select id="getConfigList" resultType="com.iflytek.cdc.admin.workbench.entity.MessageConfig">
        select <include refid="Base_Column_List"/>
        from app.tb_cdcmr_message_config where delete_flag='0' order by id
    </select>

</mapper>
