package com.iflytek.cdc.admin.workbench.enums;

import lombok.Getter;

@Getter
public enum WorkbenchTaskTypeEnum {
    DONE_TASKS(101,"已办任务"),
    TODO_TASKS(102,"代办任务"),
    DETAIL(103,"详情"),
    WORK_LOG_MENU(201,"工作日志左侧天"),
    WORK_LOG_DETAIL(202,"工作日志某天按人汇总"),
    WORK_LOG_DETAIL_LIST(203,"工作日志某人某天"),
    ;

    private Integer code;
    private String name;

    WorkbenchTaskTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}
