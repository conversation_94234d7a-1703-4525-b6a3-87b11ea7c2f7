package com.iflytek.cdc.admin.workbench.service;

import com.iflytek.cdc.admin.workbench.dto.shortcutMenu.ShortcutMenuDTO;
import com.iflytek.cdc.admin.workbench.entity.ShortcutMenu;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 工作台快捷菜单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-06
 */
public interface ShortcutMenuService extends IService<ShortcutMenu> {

    List<ShortcutMenu> getUserHasPermissionMenu(String loginUserId, List<ShortcutMenu> shortcutMenus);

    String add(ShortcutMenu addVo);

    List<ShortcutMenuDTO> getAllShortcutMenu(String loginUserId);

    Boolean updateData(ShortcutMenu updateVo);
}
