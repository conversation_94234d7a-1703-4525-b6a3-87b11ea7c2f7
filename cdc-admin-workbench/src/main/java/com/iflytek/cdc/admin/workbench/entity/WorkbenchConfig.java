package com.iflytek.cdc.admin.workbench.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 工作台快捷菜单
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "tb_cdcmr_workbench_config",schema = "app")
@ApiModel(value="WorkbenchConfig对象", description="工作台快捷菜单")
public class WorkbenchConfig implements Serializable {
    public static final String TB_NAME ="app.tb_cdcmr_workbench_config";
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "app编码")
    private String appCode;

    @ApiModelProperty(value = "任务类别名称")
    private String taskClassName;

    @ApiModelProperty(value = "任务类别编码")
    private String taskClassCode;
//    @ApiModelProperty(value = "菜单编码")
//    private String menuCode;
//
//    @ApiModelProperty(value = "菜单名称")
//    private String menuName;

    @ApiModelProperty(value = "来源系统编码")
    private String sourceSystemCode;

    @ApiModelProperty(value = "来源系统名称")
    private String sourceSystemName;

    @ApiModelProperty(value = "系统中相对路径")
    private String systemRelativePath;

    @ApiModelProperty(value = "服务路径")
    private String serviceUrl;

    @ApiModelProperty(value = "代办、已办、详情请求路径")
    private String requestUrl;

    @ApiModelProperty(value = "请求参数")
    private String requestParam;
    @ApiModelProperty(value = "请求方式 目前只有get 、post")
    private String requestMethod;

    @ApiModelProperty(value = "tb_cdcmr_workbench_config.id点击处理要条往的页面配置id")
    private String childId;

    @ApiModelProperty(value = "任务类型101-已办 102-代办 103-详情;201-工作日志左侧天 202-工作日志某天按人汇总 203-工作日志某人某天")
    private Long taskType;

    @ApiModelProperty(value = "1-我的代办  2-工作日志")
    private Integer configType;

    @ApiModelProperty(value = "是否删除 0未删除 1删除")
    private String deleteFlag;

    @ApiModelProperty(value = "创建人ID")
    private String creatorId;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改人ID")
    private String updaterId;

    @ApiModelProperty(value = "修改人")
    private String updater;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "排序")
    private Integer orderNo;


}
