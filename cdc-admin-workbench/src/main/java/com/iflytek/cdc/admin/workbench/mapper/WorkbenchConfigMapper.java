package com.iflytek.cdc.admin.workbench.mapper;

import com.iflytek.cdc.admin.workbench.dto.userTodo.TaskClassDTO;
import com.iflytek.cdc.admin.workbench.entity.WorkbenchConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 工作台快捷菜单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-06
 */
public interface WorkbenchConfigMapper extends BaseMapper<WorkbenchConfig> {

    List<WorkbenchConfig> findByCode(@Param("appCode") String code,
                                     @Param("taskClassCode") String taskClassCode,
                                     @Param("configType") Integer configType, 
                                     @Param("taskType") Integer taskType);

    List<TaskClassDTO> queryTaskClassList();

    List<WorkbenchConfig> getConfigList();

}
