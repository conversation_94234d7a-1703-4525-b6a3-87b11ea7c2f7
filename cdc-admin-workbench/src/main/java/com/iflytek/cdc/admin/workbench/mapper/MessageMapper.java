package com.iflytek.cdc.admin.workbench.mapper;

import com.iflytek.cdc.admin.common.dto.workbench.SignalPushRuleDTO;
import com.iflytek.cdc.admin.workbench.dto.message.MessageReqDTO;
import com.iflytek.cdc.admin.workbench.entity.Message;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 消息提醒表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
public interface MessageMapper extends BaseMapper<Message> {

    List<Message> findList(MessageReqDTO reqDTO);

    Boolean read(@Param("id") String id, @Param("receiverId") String loginUserId);

    Boolean allRead(@Param("receiverId") String loginUserId);

    Boolean logicDelete(@Param("id") String id, @Param("receiverId") String loginUserId);

    Integer getUnreadCount(@Param("receiverId")String loginUserId);

    List<Message> listBySignalPushConfigurationId(String id);

    void saveBatch(List<Message> list);

    void updateBatchById(List<Message> messageList);

    List<Message> listByReceiverAndTaskAndMessageConfig(@Param("dtos") List<SignalPushRuleDTO> dtos);
}
