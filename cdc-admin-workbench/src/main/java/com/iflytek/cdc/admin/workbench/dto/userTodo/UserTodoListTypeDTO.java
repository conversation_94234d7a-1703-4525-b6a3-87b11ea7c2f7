package com.iflytek.cdc.admin.workbench.dto.userTodo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class UserTodoListTypeDTO {

    @ApiModelProperty(value = "类别编码")
    private String taskClassCode;

    @ApiModelProperty(value = "类别名称")
    private String taskClassName;

//    @ApiModelProperty(value = "来源系统简称")
//    private String systemSourceSimpleName;

    @ApiModelProperty(value = "用户待办列表")
    private List<UserTodoListDTO> list;
}
