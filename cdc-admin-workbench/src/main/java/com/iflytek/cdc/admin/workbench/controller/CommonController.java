package com.iflytek.cdc.admin.workbench.controller;

import com.iflytek.cdc.admin.common.apiservice.UapServiceApi;
import com.iflytek.cdc.admin.common.vo.uap.BaseSelectDto;
import com.iflytek.cdc.admin.common.vo.uap.UapPortalApp;
import com.iflytek.cdc.admin.common.vo.uap.UapPortalAppDetail;
import com.iflytek.cdc.admin.common.vo.uap.UserInfoDto;
import com.iflytek.cdc.admin.workbench.dto.shortcutMenu.SystemSourceDto;
import com.iflytek.cdc.admin.workbench.dto.userTodo.TaskClassDTO;
import com.iflytek.cdc.admin.workbench.service.CommonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Api(tags = "工作台-通用接口查询", value = "通用接口查询")
@RequestMapping("/{version}/pt/workbench/common")
public class CommonController {
    @Resource
    private UapServiceApi uapServiceApi;
    @Resource
    private CommonService commonService;

    @ApiOperation("获取用户有权限的全部应用列表")
    @GetMapping("/queryUapAppList")
    public List<UapPortalApp> queryUapAppList(@RequestParam("loginUserId") String loginUserId) {
        return uapServiceApi.queryUserPortalApp(loginUserId);
    }
    @ApiOperation("获取用户有权限的全部应用列表-拉平")
    @GetMapping("/queryUapAppDetailList")
    public List<UapPortalAppDetail> queryUapAppDetailList(@RequestParam("loginUserId") String loginUserId) {
        return uapServiceApi.getUserAppDetailList(loginUserId);
    }

    @ApiOperation("获取用户信息")
    @GetMapping("/queryUserInfo")
    public UserInfoDto queryUserInfo(@RequestParam String loginUserId, @RequestParam(required = false) String appCode) {
        return uapServiceApi.queryUserInfo(loginUserId, appCode);
    }
    @ApiOperation("获取系统来源下拉列表")
    @GetMapping("/getSystemSourceSelect")
    public List<SystemSourceDto> getSystemSourceSelect(@RequestParam String loginUserId) {
        return commonService.getSystemSourceSelect(loginUserId);
    }

    @ApiOperation("根据登录人和当前机构查询科室下拉列表")
    @GetMapping("/queryDeptSelectListByLoginUserId")
    public List<BaseSelectDto> queryDeptSelectListByLoginUserId(@RequestParam String orgId, @RequestParam String loginUserId) {
        return commonService.queryDeptSelectListByLoginUserId(orgId, loginUserId);
    }
    @ApiOperation("获取代办任务类别")
    @GetMapping("/queryTaskClassList")
    public List<TaskClassDTO> queryTaskClassList() {
        return commonService.queryTaskClassList();
    }

}
