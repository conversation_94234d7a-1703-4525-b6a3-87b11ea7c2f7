package com.iflytek.cdc.admin.workbench.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.iflytek.cdc.admin.common.apiservice.BatchCommonService;
import com.iflytek.cdc.admin.common.apiservice.UapServiceApi;
import com.iflytek.cdc.admin.common.vo.uap.UapAuthNode;
import com.iflytek.cdc.admin.common.vo.uap.UapPortalApp;
import com.iflytek.cdc.admin.common.vo.uap.UapPortalAppDetail;
import com.iflytek.cdc.admin.common.vo.uap.UapUserPo;
import com.iflytek.cdc.admin.workbench.dto.shortcutMenu.ShortcutMenuDTO;
import com.iflytek.cdc.admin.workbench.entity.ShortcutMenu;
import com.iflytek.cdc.admin.workbench.enums.WorkLogSystemSourceEnum;
import com.iflytek.cdc.admin.workbench.mapper.ShortcutMenuMapper;
import com.iflytek.cdc.admin.workbench.service.ShortcutMenuService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.iflytek.cdc.admin.common.interceptor.UserInfoInterceptor.USER_INFO;

/**
 * <p>
 * 工作台快捷菜单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-06
 */
@Slf4j
@Service
public class ShortcutMenuServiceImpl extends ServiceImpl<ShortcutMenuMapper, ShortcutMenu> implements ShortcutMenuService {
    @Resource
    private UapServiceApi uapServiceApi;
    
    @Resource
    private BatchCommonService batchCommonService;

    @Override
    public List<ShortcutMenuDTO> getAllShortcutMenu(String loginUserId) {
        List<ShortcutMenu> shortcutMenuList = baseMapper.findAll();
        List<ShortcutMenu> userHasPermissionMenu = getUserHasPermissionMenu(loginUserId, shortcutMenuList);
        List<ShortcutMenuDTO> shortcutMenuDTOList = new ArrayList<>();
        ShortcutMenuDTO shortcutMenuDto;
        if (CollUtil.isNotEmpty(userHasPermissionMenu)) {
            Map<String, List<ShortcutMenu>> map = userHasPermissionMenu.stream().collect(Collectors.groupingBy(ShortcutMenu::getSourceSystemCode));
            for (Map.Entry<String, List<ShortcutMenu>> entry : map.entrySet()) {
                shortcutMenuDto = new ShortcutMenuDTO();
                shortcutMenuDto.setSourceSystemCode(entry.getKey());
                shortcutMenuDto.setSourceSystemName(entry.getValue().get(0).getSourceSystemName());
                shortcutMenuDto.setChildren(entry.getValue());
                shortcutMenuDTOList.add(shortcutMenuDto);
            }
        }
        return shortcutMenuDTOList;
    }

    @Override
    public String add(ShortcutMenu addVo) {
        String id = batchCommonService.uuid(ShortcutMenu.TB_NAME);
        UapUserPo uapUserPo = USER_INFO.get();
        addVo.setId(id);
        addVo.setCreatorId(uapUserPo.getId());
        addVo.setCreator(uapUserPo.getName());
        addVo.setCreateTime(LocalDateTime.now());
        addVo.setUpdater(uapUserPo.getName());
        addVo.setUpdaterId(uapUserPo.getId());
        addVo.setUpdateTime(LocalDateTime.now());
        baseMapper.insert(addVo);
        return id;
    }

    @Override
    public Boolean updateData(ShortcutMenu updateVo) {
        UapUserPo uapUserPo = USER_INFO.get();
        updateVo.setUpdater(uapUserPo.getName());
        updateVo.setUpdaterId(uapUserPo.getId());
        updateVo.setUpdateTime(LocalDateTime.now());
        return baseMapper.updateById(updateVo)>0;
    }

    @Override
    public List<ShortcutMenu> getUserHasPermissionMenu(String loginUserId, List<ShortcutMenu> shortcutMenuList) {
        List<ShortcutMenu> result = new ArrayList<>();
        //调用uap接口获取用户的应用列表
        List<UapPortalApp> uapPortalApps = uapServiceApi.queryUserPortalApp(loginUserId);
        if (CollUtil.isNotEmpty(uapPortalApps)) {
            List<String> portalAppCodeList = getUserPortalAppCodeList(uapPortalApps);
            log.info("用户有权限的app列表：{}", JSONObject.toJSONString(portalAppCodeList));
            //获取用户有权限的app
            shortcutMenuList = shortcutMenuList.stream().filter(s -> portalAppCodeList.contains(s.getAppCode())).collect(Collectors.toList());
            if (CollUtil.isEmpty(shortcutMenuList)) {
                return result;
            }
            //获取用户有权限的所有菜单
            Map<String, List<String>> appAllMenuUrlMap = getAppAllMenuUrlList(loginUserId, shortcutMenuList);
            //将有权限的快捷菜单加到返回结果中
            for (ShortcutMenu shortcutMenu : shortcutMenuList) {
                List<String> menuList = appAllMenuUrlMap.get(shortcutMenu.getAppCode());
                if (WorkLogSystemSourceEnum.CDC_WUHU_ECD_PLATFORM.getSystemCode().equals(shortcutMenu.getAppCode())) {
                    if (menuList.contains("/" + shortcutMenu.getSystemRelativePath())) {
                        result.add(shortcutMenu);
                    }
                } else {
                    if (menuList.contains(shortcutMenu.getSystemRelativePath())) {
                        result.add(shortcutMenu);
                    }
                }

            }
        }
        return result;
    }

    private List<String> getUserPortalAppCodeList(List<UapPortalApp> uapPortalApps) {
        List<UapPortalAppDetail> allApps = new ArrayList<>();
        for (UapPortalApp uapPortalApp : uapPortalApps) {
            List<UapPortalAppDetail> appList = uapPortalApp.getAppList();
            allApps.addAll(appList);
        }
        return allApps.stream().map(UapPortalAppDetail::getCode).distinct().collect(Collectors.toList());
    }

    private Map<String, List<String>> getAppAllMenuUrlList(String loginUserId, List<ShortcutMenu> shortcutMenuList) {
        Map<String, List<String>> appMenuMap = new HashMap<>();
        List<String> appCodeList = shortcutMenuList.stream().map(ShortcutMenu::getAppCode).distinct().collect(Collectors.toList());
        for (String appCode : appCodeList) {
            List<UapAuthNode> authNodeList = uapServiceApi.getMenu(loginUserId, appCode);
            //获取改系统所有的菜单
            List<String> allUrls = new ArrayList<>();
            for (UapAuthNode node : authNodeList) {
                if (node.getNodes() != null) {
                    for (UapAuthNode childNode : node.getNodes()) {
                        if (childNode.getChecked()) {
                            List<String> urls = collectUrlPaths(childNode);
                            allUrls.addAll(urls);
                        }
                    }
                }
            }
            appMenuMap.put(appCode, allUrls);
        }
        return appMenuMap;
    }

    // 递归方法，用于收集所有URL路径
    private List<String> collectUrlPaths(UapAuthNode node) {
        List<String> urlPaths = new ArrayList<>();
        collectUrlPathsRecursive(node, urlPaths, "");
        return urlPaths;
    }

    private void collectUrlPathsRecursive(UapAuthNode node, List<String> urlPaths, String currentPath) {
        if (node.getChecked()) {
            String path = "";
            if (StrUtil.isNotBlank(node.getUrl())) {
                path = currentPath + "/" + node.getUrl();

                //2025-03-27 18:06 与前端 胡世豪 shhu6 李壮 zhuangli5  讨论结论：有/就把前面的丢弃
                if (node.getUrl().startsWith("/")){
                    path = node.getUrl();
                }
            }
            if (node.getNodes() == null || node.getNodes().isEmpty()) {
                // 如果没有子节点，添加当前路径
                path = path.replaceAll("//","/");
                urlPaths.add(path);
            } else {
                // 如果有子节点，递归调用子节点
                for (UapAuthNode childNode : node.getNodes()) {
                    collectUrlPathsRecursive(childNode, urlPaths, path);
                }
            }
        }
    }
}
