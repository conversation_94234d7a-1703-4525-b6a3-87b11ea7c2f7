package com.iflytek.cdc.admin.workbench.controller;


import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.workbench.annotation.OperationLogAnnotation;
import com.iflytek.cdc.admin.workbench.dto.message.MessageAddDTO;
import com.iflytek.cdc.admin.workbench.dto.message.MessageConfigAddDTO;
import com.iflytek.cdc.admin.workbench.dto.message.MessageReqDTO;
import com.iflytek.cdc.admin.workbench.dto.workLog.LogExportReqDto;
import com.iflytek.cdc.admin.workbench.entity.Message;
import com.iflytek.cdc.admin.workbench.entity.MessageConfig;
import com.iflytek.cdc.admin.workbench.service.MessageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 消息提醒表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
@RestController
@Api(tags = "工作台-消息提醒", value = "工作台-消息提醒")
public class MessageController {

    @Resource
    private MessageService messageService;

    @PostMapping("/{version}/pb/message/save")
    @ApiOperation("新增消息提醒")
    public String save(@RequestBody MessageAddDTO addDTO) {
        return messageService.insert(addDTO);
    }

    /**
     * 消息提醒-批量新增
     */
    @PostMapping("/{version}/pb/message/saveBatch")
    @ApiOperation("批量新增消息提醒")
    public void saveBatch(@RequestBody List<MessageAddDTO> addDTO) {
        messageService.insertBatch(addDTO);
    }
    /**
     * 
     * @param reqDTO
     * @param loginUserId
     * @return
     */
    @PostMapping("/{version}/pt/message/findList")
    @ApiOperation("查询消息记录")
    public PageInfo<Message> findList(@RequestBody MessageReqDTO reqDTO, @RequestParam("loginUserId") String loginUserId) {
        return messageService.findList(reqDTO,loginUserId);
    }

    @OperationLogAnnotation(operationName = "消息提醒-单个已读")
    @PostMapping("/{version}/pt/message/read")
    @ApiOperation("单个已读")
    public Boolean read(@RequestParam("id") String id, @RequestParam("loginUserId") String loginUserId) {
        return messageService.read(id,loginUserId);
    }
    @OperationLogAnnotation(operationName = "消息提醒-全部已读")
    @PostMapping("/{version}/pt/message/allRead")
    @ApiOperation("全部已读")
    public Boolean read(@RequestParam("loginUserId") String loginUserId) {
        return messageService.allRead(loginUserId);
    }

    @OperationLogAnnotation(operationName = "消息提醒-单个删除")
    @PostMapping("/{version}/pt/message/delete")
    @ApiOperation("单个删除")
    public Boolean delete(@RequestParam("id") String id, @RequestParam("loginUserId") String loginUserId) {
        return messageService.delete(id,loginUserId);
    }
    
    
    @GetMapping("/{version}/pt/message/getUnreadCount")
    @ApiOperation("获取未读数量")
    public Integer getUnreadCount(@RequestParam("loginUserId") String loginUserId) {
        return messageService.getUnreadCount(loginUserId);
    }


    //------------------------------------消息提醒配置开始----------------------------------------------

    @GetMapping("/{version}/pb/message/getConfigById")
    @ApiOperation("获取消息提醒配置通过id")
    public MessageConfig getConfigById(@RequestParam("id") String id) {
        return messageService.getConfigById(id);
    }

    @GetMapping("/{version}/pb/message/getConfigList")
    @ApiOperation("获取全部消息提醒配置")
    public List<MessageConfig> getConfigList() {
        return messageService.getConfigList();
    }
    @PostMapping("/{version}/pb/message/addConfig")
    @ApiOperation("新增消息提醒配置")
    public String addConfig(@RequestBody MessageConfigAddDTO dto) {
        return messageService.addConfig(dto);
    }


    //------------------------------------消息提醒配置结束----------------------------------------------
}

