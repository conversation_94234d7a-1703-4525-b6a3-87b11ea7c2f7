package com.iflytek.cdc.admin.workbench.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 工作台用户快捷菜单
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "tb_cdcmr_user_shortcut_menu",schema = "app")
@ApiModel(value="UserShortcutMenu对象", description="工作台用户快捷菜单")
public class UserShortcutMenu implements Serializable {

    private static final long serialVersionUID = 1L;

    public static final String TABLE_NAME = "app.tb_cdcmr_user_shortcut_menu";
    @ApiModelProperty(value = "分布式主键")
    private String id;

    @ApiModelProperty(value = "uap用户id")
    private String uapUserId;

    @ApiModelProperty(value = "快捷菜单id")
    private String shortcutMenuId;

    @ApiModelProperty(value = "是否删除 0未删除 1删除")
    private String deleteFlag;

    @ApiModelProperty(value = "创建人ID")
    private String creatorId;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改人ID")
    private String updaterId;

    @ApiModelProperty(value = "修改人")
    private String updater;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    private Integer orderNo;


}
