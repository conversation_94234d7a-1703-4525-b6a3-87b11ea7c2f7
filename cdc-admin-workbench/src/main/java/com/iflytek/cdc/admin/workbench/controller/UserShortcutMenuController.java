package com.iflytek.cdc.admin.workbench.controller;


import com.iflytek.cdc.admin.workbench.entity.ShortcutMenu;
import com.iflytek.cdc.admin.workbench.service.UserShortcutMenuService;
import com.iflytek.cdc.admin.workbench.vo.shortcutMenu.UserShortcutMenuUpdateVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 工作台用户快捷菜单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-06
 */
@RestController
@Api(tags = "工作台-用户快捷菜单", value = "工作台用户快捷菜单")
@RequestMapping("/{version}/pt/userShortcutMenu")
public class UserShortcutMenuController {
    @Resource
    private UserShortcutMenuService userShortcutMenuService;
    @ApiOperation("获取当前用户快捷菜单")
    @GetMapping("/getCurrentUserShortcutMenu")
    public List<ShortcutMenu> getCurrentUserShortcutMenu(@RequestParam("loginUserId") String loginUserId) {
        return userShortcutMenuService.getCurrentUserShortcutMenu(loginUserId);
    }

    @ApiOperation("修改当前用户快捷菜单")
    @PostMapping("/updateCurrentUserShortcutMenu")
    public Boolean updateCurrentUserShortcutMenu(@Validated @RequestBody UserShortcutMenuUpdateVO userShortcutMenuUpdateVo, @RequestParam("loginUserId") String loginUserId) {
        return userShortcutMenuService.updateCurrentUserShortcutMenu(userShortcutMenuUpdateVo,loginUserId);
    }

}

