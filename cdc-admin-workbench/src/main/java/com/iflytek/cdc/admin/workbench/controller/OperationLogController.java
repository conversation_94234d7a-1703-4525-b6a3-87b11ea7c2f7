package com.iflytek.cdc.admin.workbench.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iflytek.cdc.admin.common.constants.CommonConstants;
import com.iflytek.cdc.admin.workbench.dto.workLog.*;
import com.iflytek.cdc.admin.workbench.entity.OperationLog;
import com.iflytek.cdc.admin.workbench.service.OperationLogService;
import com.iflytek.cdc.admin.workbench.vo.workLog.WorkLogMenuQueryVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 操作记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-06
 */
@RestController
@Api(tags = "工作台-操作日志", value = "工作台-操作日志")
@RequestMapping("/{version}/pt/operationLog")
public class OperationLogController {
    @Resource
    private OperationLogService operationLogService;

    @ApiOperation("获取工作日志左侧菜单")
    @PostMapping("/getUserWorkLogMenu")
    public List<WorkLogMenuMonthDto> getUserWorkLogMenu(@Validated @RequestBody WorkLogMenuQueryVo workLogMenuQueryVo,String loginUserId,String loginUserName) {
        return operationLogService.getUserWorkLogMenu(workLogMenuQueryVo,loginUserId,loginUserName);
    }

    @ApiOperation("根据天获取工作日志")
    @PostMapping("/getWorkLogDetailByDay")
    public Page<WorkLogDetailDto> getWorkLogDetailByDay(@Validated @RequestBody WorkLogDetailQueryDto workLogDetailQueryDto,String loginUserId,String loginUserName) {
        return operationLogService.getWorkLogDetailByDay(workLogDetailQueryDto,loginUserId,loginUserName);
    }

    @ApiOperation("获取用户当天的工作日志列表")
    @PostMapping("/getUserWorkLogDetailListByDay")
    public Page<WorkLogUserDetailDto> getUserWorkLogDetailListByDay(@Validated @RequestBody WorkLogUserDetailQueryDto workLogUserDetailQueryDto) {
        return operationLogService.getUserWorkLogDetailListByDay(workLogUserDetailQueryDto);
    }

    @PostMapping("/save")
    @ApiOperation("保存导出文件记录")
    public String save(@RequestBody LogExportReqDto reqDto,
                                @RequestParam String loginUserId) {
        reqDto.setLoginUserId(loginUserId);
        operationLogService.insert(reqDto);
        return reqDto.getId();
    }

    @PostMapping("/updateStatus")
    @ApiOperation("激活状态")
    public String updateStatus(@RequestBody LogExportReqDto reqDto,
                                  @RequestParam String loginUserId) {
        OperationLog update = new OperationLog();
        Assert.isTrue(operationLogService.selectByPrimaryKey(reqDto.getId())!=null,"不存在此记录");

        update.setId(reqDto.getId());
        update.setFileSize(reqDto.getFileSize() );
        update.setStatus(CommonConstants.STATUS_ON);
        operationLogService.updateByPrimaryKeySelective(update);
        return reqDto.getId();
    }

}

