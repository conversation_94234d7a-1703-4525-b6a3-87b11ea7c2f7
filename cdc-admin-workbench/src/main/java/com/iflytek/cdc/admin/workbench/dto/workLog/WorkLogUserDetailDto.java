package com.iflytek.cdc.admin.workbench.dto.workLog;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class WorkLogUserDetailDto {

    @ApiModelProperty(value = "主键id")
    private String id;

    @ApiModelProperty(value = "操作时间")
    private Date operationTime;

    @ApiModelProperty(value = "操作模块")
    private String operationModule;

    @ApiModelProperty(value = "操作名称")
    private String operationName;

    @ApiModelProperty(value = "备注")
    private String remark;
}