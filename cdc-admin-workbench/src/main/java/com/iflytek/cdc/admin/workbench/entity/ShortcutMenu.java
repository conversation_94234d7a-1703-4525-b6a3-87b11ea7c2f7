package com.iflytek.cdc.admin.workbench.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 工作台快捷菜单
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "tb_cdcmr_shortcut_menu", schema = "app")
@ApiModel(value="ShortcutMenu对象", description="工作台快捷菜单")
public class ShortcutMenu implements Serializable {

    public static final long serialVersionUID = 1L;

    public static final String TB_NAME = "app.tb_cdcmr_shortcut_menu";


    @ApiModelProperty(value = "分布式主键")
    private String id;

    @ApiModelProperty(value = "菜单编码")
    private String menuCode;

    @ApiModelProperty(value = "菜单名称")
    private String menuName;

    @ApiModelProperty(value = "来源系统编码")
    private String sourceSystemCode;

    @ApiModelProperty(value = "来源系统名称")
    private String sourceSystemName;

    @ApiModelProperty(value = "系统中相对路径")
    private String systemRelativePath;

    @ApiModelProperty(value = "所属角色类型（1-科员，2-科长，主任）")
    private String roleType;

    @ApiModelProperty(value = "是否默认，0-否，1-是")
    private String isDefault;

    @ApiModelProperty(value = "是否删除 0未删除 1删除")
    private String deleteFlag;

    @ApiModelProperty(value = "创建人ID")
    private String creatorId;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改人ID")
    private String updaterId;

    @ApiModelProperty(value = "修改人")
    private String updater;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "排序")
    private Integer orderNo;

    @ApiModelProperty(value = "图标")
    private String icon;

    @ApiModelProperty(value = "app编码")
    private String appCode;


}
