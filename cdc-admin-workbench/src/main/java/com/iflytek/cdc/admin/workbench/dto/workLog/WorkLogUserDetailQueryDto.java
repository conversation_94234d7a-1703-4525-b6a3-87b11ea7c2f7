package com.iflytek.cdc.admin.workbench.dto.workLog;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class WorkLogUserDetailQueryDto  {
    @ApiModelProperty("当前页 默认第一页")
    private int current = 1;

    @ApiModelProperty("每页显示条数 默认10条")
    private int size = 10;

    @NotBlank(message = "用户id不能为空")
    @ApiModelProperty(value = "用户id")
    private String loginUserId;

    @NotBlank(message = "日期不能为空")
    @ApiModelProperty(value = "日期（天）")
    private String day;

    @ApiModelProperty(value = "关键字")
    private String keyWord;

    //智慧化预警系统参数
    @ApiModelProperty(value = "系统来源编码")
    private String appCode;
}
