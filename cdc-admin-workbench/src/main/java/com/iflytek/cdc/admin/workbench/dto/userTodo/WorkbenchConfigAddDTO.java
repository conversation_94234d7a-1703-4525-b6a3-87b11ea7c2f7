package com.iflytek.cdc.admin.workbench.dto.userTodo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class WorkbenchConfigAddDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "app编码")
    private String appCode;

    @ApiModelProperty(value = "任务类别名称")
    private String taskClassName;

    @ApiModelProperty(value = "任务类别编码")
    private String taskClassCode;

    @ApiModelProperty(value = "来源系统编码")
    private String sourceSystemCode;

    @ApiModelProperty(value = "来源系统名称")
    private String sourceSystemName;

    @ApiModelProperty(value = "系统中相对路径")
    private String systemRelativePath;

    @ApiModelProperty(value = "服务路径")
    private String serviceUrl;

    @ApiModelProperty(value = "代办、已办、详情请求路径")
    private String requestUrl;

    @ApiModelProperty(value = "请求参数")
    private String requestParam;

    @ApiModelProperty(value = "请求方式 目前只有get 、post")
    private String requestMethod;

    @ApiModelProperty(value = "tb_cdcmr_workbench_config.id点击处理要条往的页面配置id")
    private String childId;

    @ApiModelProperty(value = "任务类型101-已办 102-代办 103-详情;201-工作日志左侧天 202-工作日志某天按人汇总 203-工作日志某人某天")
    private Long taskType;

    @ApiModelProperty(value = "1-我的代办  2-工作日志")
    private Integer configType;

}
