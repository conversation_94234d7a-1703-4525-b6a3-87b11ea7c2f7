package com.iflytek.cdc.admin.workbench.service;

import com.iflytek.cdc.admin.common.vo.uap.BaseSelectDto;
import com.iflytek.cdc.admin.workbench.dto.shortcutMenu.SystemSourceDto;
import com.iflytek.cdc.admin.workbench.dto.userTodo.TaskClassDTO;

import java.util.List;

public interface CommonService {
    List<SystemSourceDto> getSystemSourceSelect(String loginUserId);

    List<BaseSelectDto> queryDeptSelectListByLoginUserId(String orgId, String loginUserId);

    /**
     * 获取代办任务类别
     * @return
     */
    List<TaskClassDTO> queryTaskClassList();

}
