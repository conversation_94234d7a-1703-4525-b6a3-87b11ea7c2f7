package com.iflytek.cdc.admin.workbench.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.iflytek.cdc.admin.common.apiservice.ApiService;
import com.iflytek.cdc.admin.common.apiservice.BatchCommonService;
import com.iflytek.cdc.admin.common.apiservice.UapServiceApi;
import com.iflytek.cdc.admin.common.constants.CommonConstants;
import com.iflytek.cdc.admin.common.vo.uap.UapPortalAppDetail;
import com.iflytek.cdc.admin.common.vo.uap.UapUserPo;
import com.iflytek.cdc.admin.workbench.dto.userTodo.UserTodoListDTO;
import com.iflytek.cdc.admin.workbench.dto.userTodo.UserTodoListTypeDTO;
import com.iflytek.cdc.admin.workbench.dto.userTodo.WorkbenchConfigAddDTO;
import com.iflytek.cdc.admin.workbench.entity.WorkbenchConfig;
import com.iflytek.cdc.admin.workbench.enums.WorkbenchConfigEnum;
import com.iflytek.cdc.admin.workbench.enums.WorkbenchTaskTypeEnum;
import com.iflytek.cdc.admin.workbench.mapper.WorkbenchConfigMapper;
import com.iflytek.cdc.admin.workbench.service.WorkbenchConfigService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iflytek.cdc.admin.workbench.vo.userTodo.UserTodoListVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.iflytek.cdc.admin.common.interceptor.UserInfoInterceptor.USER_INFO;

/**
 * <p>
 * 工作台快捷菜单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-06
 */
@Slf4j
@Service
public class WorkbenchConfigServiceImpl extends ServiceImpl<WorkbenchConfigMapper, WorkbenchConfig> implements WorkbenchConfigService {

    @Resource
    private BatchCommonService batchCommonService;
    @Resource
    private UapServiceApi uapServiceApi;

    @Resource
    private ApiService apiService;

    @Resource
    private WorkbenchConfigMapper workbenchConfigMapper;
    @Value("${cdc-data-service:http://cdc-data-service}")
    private String dataServiceApiUrl;
    @Value("${cdc-platform-api:http://cdc-platform-api}")
    private String platformServiceApiUrl;
    @Value("${api:rest}")
    private String apiTool;

    private String changeRestHttpUlr(String serviceUrl){
        if (apiTool.equalsIgnoreCase("http")){
            serviceUrl = serviceUrl.replace("http://cdc-data-service",dataServiceApiUrl)
                    .replace("http://cdc-platform-api",platformServiceApiUrl);
            return serviceUrl;
        }
        return serviceUrl;
    }
    
    
    @Override
    public List<UserTodoListDTO> getUserTodoList(UserTodoListVO vo) {
        UapUserPo uapUserPo = USER_INFO.get();
        String loginUserId = uapUserPo.getId();
        String loginUserName = uapUserPo.getLoginName();
        List<UserTodoListDTO> result = new ArrayList<>();

        List<WorkbenchConfig> configs = workbenchConfigMapper.findByCode(null, vo.getTaskClassCode(), WorkbenchConfigEnum.TASKS.getCode(), WorkbenchTaskTypeEnum.TODO_TASKS.getCode());
        //遍历任务类别
        UserTodoListVO reqVO;
        for (WorkbenchConfig config : configs) {
            String url = changeRestHttpUlr(config.getServiceUrl())  
                    + config.getRequestUrl() 
                    + "?loginUserId=" + loginUserId 
                    + "&loginUserName=" + loginUserName
                    + "&limitNum=100";
            reqVO = new UserTodoListVO();
            BeanUtils.copyProperties(vo,reqVO);
            reqVO.setRequestParam(config.getRequestParam());
            reqVO.setAppCode(config.getAppCode());
            try {
                List<UserTodoListDTO> userTodoList;
                if (CommonConstants.METHOD_GET.equalsIgnoreCase(config.getRequestMethod())){
                    url = url+"&RequestParam="+config.getRequestParam();
                    userTodoList = apiService.doGetList(url,  UserTodoListDTO.class);
                } else {
                    userTodoList = apiService.doPostList(url, reqVO, UserTodoListDTO.class);
                }

                buildConfData(null, config, userTodoList);
                if (CollUtil.isNotEmpty(userTodoList)) {
                    result.addAll(userTodoList);
                }

            } catch (Exception e) {
                log.error("调用代办接口出错url#{}, 请求参数#{}", url, JSONObject.toJSONString(reqVO), e);
            }
        }

        //按派发时间降序排序
        result.sort(Comparator.comparing(UserTodoListDTO::getTaskStartDate).reversed());
        return result;
    }
//    @Override
//    public List<UserTodoListDTO> getUserTodoList(UserTodoListVO vo) {
//        UapUserPo uapUserPo = USER_INFO.get();
//        String loginUserId = uapUserPo.getId();
//        String loginUserName = uapUserPo.getLoginName();
//        List<UserTodoListDTO> result = new ArrayList<>();
//
//        List<UapPortalAppDetail> userAllApps = uapServiceApi.getUserAppDetailList(loginUserId);
//        if (CollUtil.isNotEmpty(userAllApps)) {
//            //遍历系统
//            for (UapPortalAppDetail appDetail : userAllApps) {
//                //选择了系统只查询该系统，跳过不需要的系统
//                if (StrUtil.isNotBlank(vo.getAppCode())){
//                    if (!vo.getAppCode().equals(appDetail.getCode())){
//                        continue;
//                    }
//                }
//
//                List<WorkbenchConfig> configs = workbenchConfigMapper.findByCode(appDetail.getCode(), vo.getTaskClassCode(), WorkbenchConfigEnum.TASKS.getCode(), WorkbenchTaskTypeEnum.TODO_TASKS.getCode());
//                //遍历任务类别
//                UserTodoListVO reqVO;
//                for (WorkbenchConfig config : configs) {
//                    String url = changeRestHttpUlr(config.getServiceUrl())  + config.getRequestUrl() + "?loginUserId=" + loginUserId + "&loginUserName=" + loginUserName;
//                    reqVO = new UserTodoListVO();
//                    BeanUtils.copyProperties(vo,reqVO);
//                    reqVO.setRequestParam(config.getRequestParam());
//                    reqVO.setAppCode(config.getAppCode());
//                    try {
//                        List<UserTodoListDTO> userTodoList;
//                        if (CommonConstants.METHOD_GET.equalsIgnoreCase(config.getRequestMethod())){
//                            url = url+"&RequestParam="+config.getRequestParam();
//                            userTodoList = apiService.doGetList(url,  UserTodoListDTO.class);
//                        } else {
//                            userTodoList = apiService.doPostList(url, reqVO, UserTodoListDTO.class);
//                        }
//
//                        buildConfData(null, config, userTodoList);
//                        if (CollUtil.isNotEmpty(userTodoList)) {
//                            result.addAll(userTodoList);
//                        }
//
//                    } catch (Exception e) {
//                        log.error("调用代办接口出错url#{}, 请求参数#{}", url, JSONObject.toJSONString(reqVO), e);
//                    }
//                }
//            }
//        }
//        //按派发时间降序排序
//        result.sort(Comparator.comparing(UserTodoListDTO::getTaskStartDate).reversed());
//        return result;
//    }

    /**
     * 跳转相关的配置参数
     *
     * @param appDetail
     * @param config
     * @param userTodoList
     */
    private static void buildConfData(UapPortalAppDetail appDetail, WorkbenchConfig config, List<UserTodoListDTO> userTodoList) {
        for (UserTodoListDTO listDTO : userTodoList) {
            //优先取接口返回的 然后取配置表的 兼容流调
            if (StrUtil.isBlank(listDTO.getRelativePath())){
                listDTO.setRelativePath(config.getSystemRelativePath());
                listDTO.setSystemSourceName(config.getSourceSystemName());
                listDTO.setSystemSourceCode(config.getSourceSystemCode());
            }

            listDTO.setTaskType(config.getTaskType());
            listDTO.setAppCode(config.getAppCode());
//            listDTO.setAppName(appDetail.getName());
            listDTO.setTaskClassCode(config.getTaskClassCode());
            listDTO.setTaskClassName(config.getTaskClassName());
        }
    }


    @Override
    public List<UserTodoListTypeDTO> getUserTodoListBy(UserTodoListVO vo) {
        return getUserTaskListTypeDTOS(vo,WorkbenchTaskTypeEnum.TODO_TASKS.getCode());
    }

    @Override
    public List<UserTodoListTypeDTO> getUserDoneListBy(UserTodoListVO vo) {
        return getUserTaskListTypeDTOS(vo,WorkbenchTaskTypeEnum.DONE_TASKS.getCode());
    }

    private List<UserTodoListTypeDTO> getUserTaskListTypeDTOS(UserTodoListVO vo, Integer taskType) {
        List<UserTodoListTypeDTO> result = new ArrayList<>();
        UapUserPo uapUserPo = USER_INFO.get();
        String loginUserId = uapUserPo.getId();
        String loginUserName = uapUserPo.getLoginName();


        List<WorkbenchConfig> configs = workbenchConfigMapper.findByCode(null,
                vo.getTaskClassCode(),
                WorkbenchConfigEnum.TASKS.getCode(),
                taskType);
        Map<String, List<WorkbenchConfig>> listMap = configs.stream().collect(Collectors.groupingBy(WorkbenchConfig::getTaskClassCode));

        listMap.forEach((taskClassCode, workbenchConfigs) -> {
            List<UserTodoListDTO> list = new ArrayList<>();
            UserTodoListTypeDTO dto = new UserTodoListTypeDTO();
            result.add(dto);

            WorkbenchConfig config1 = workbenchConfigs.get(0);

            dto.setTaskClassCode(config1.getTaskClassCode());
            dto.setTaskClassName(config1.getTaskClassName());
            dto.setList(list);
            //遍历任务类别
            UserTodoListVO reqVO = null;
            for (WorkbenchConfig config : workbenchConfigs) {
                String url = changeRestHttpUlr(config.getServiceUrl()) + config.getRequestUrl() + "?loginUserId=" + loginUserId + "&loginUserName=" + loginUserName;
                try {

                    reqVO = new UserTodoListVO();
                    BeanUtils.copyProperties(vo, reqVO);
                    reqVO.setRequestParam(config.getRequestParam());
                    reqVO.setAppCode(config.getAppCode());

                    List<UserTodoListDTO> userTodoList;

                    if (CommonConstants.METHOD_GET.equalsIgnoreCase(config.getRequestMethod())) {
                        url = url + "&RequestParam=" + config.getRequestParam();
                        userTodoList = apiService.doGetList(url, UserTodoListDTO.class);
                    } else {
                        userTodoList = apiService.doPostList(url, reqVO, UserTodoListDTO.class);
                    }

                    buildConfData(null, config, userTodoList);
                    if (CollUtil.isNotEmpty(userTodoList)) {
                        list.addAll(userTodoList);
                    }
                } catch (Exception e) {
                    log.error("调用接口出错url#{}, 请求参数#{}", url, JSONObject.toJSONString(reqVO), e);

                }
            }


            if (Objects.equals(WorkbenchTaskTypeEnum.DONE_TASKS.getCode(), taskType)) {
                //按处理时间降序排序
                list.sort(Comparator.comparing(UserTodoListDTO::getTaskProcessTime).reversed());
            } else {
                //按派发时间降序排序
                list.sort(Comparator.comparing(UserTodoListDTO::getTaskStartDate).reversed());
            }
        });


        return result;
    }

//    private List<UserTodoListTypeDTO> getUserTaskListTypeDTOS(UserTodoListVO vo , Integer taskType) {
//        List<UserTodoListTypeDTO> result = new ArrayList<>();
//        UapUserPo uapUserPo = USER_INFO.get();
//        String loginUserId = uapUserPo.getId();
//        String loginUserName = uapUserPo.getLoginName();
//
//        List<UapPortalAppDetail> userAllApps = uapServiceApi.getUserAppDetailList(loginUserId);
//        if (CollUtil.isNotEmpty(userAllApps)) {
//            //遍历系统
//            for (UapPortalAppDetail appDetail : userAllApps) {
//                //选择了系统只查询该系统，跳过不需要的系统
//                if (StrUtil.isNotBlank(vo.getAppCode())){
//                    if (!vo.getAppCode().equals(appDetail.getCode())){
//                        continue;
//                    }
//                }
//
//                List<WorkbenchConfig> configs = workbenchConfigMapper.findByCode(appDetail.getCode(),
//                        vo.getTaskClassCode(),
//                        WorkbenchConfigEnum.TASKS.getCode(),
//                        taskType);
//                if (CollUtil.isEmpty(configs)){
//                    continue;
//                }
//                List<UserTodoListDTO> list = new ArrayList<>();
//                UserTodoListTypeDTO dto = new UserTodoListTypeDTO();
//                result.add(dto);
//                dto.setTaskClassCode(appDetail.getCode());
//                dto.setTaskClassName(appDetail.getName());
//                dto.setList(list);
//                //遍历任务类别
//                UserTodoListVO reqVO = null;
//                for (WorkbenchConfig config : configs) {
//                    String url = changeRestHttpUlr(config.getServiceUrl())  + config.getRequestUrl() + "?loginUserId=" + loginUserId + "&loginUserName=" + loginUserName;
//                    try {
//
//                        reqVO = new UserTodoListVO();
//                        BeanUtils.copyProperties(vo,reqVO);
//                        reqVO.setRequestParam(config.getRequestParam());
//                        reqVO.setAppCode(config.getAppCode());
//
//                        List<UserTodoListDTO> userTodoList ;
//
//                        if (CommonConstants.METHOD_GET.equalsIgnoreCase(config.getRequestMethod())){
//                            url = url+"&RequestParam="+config.getRequestParam();
//                            userTodoList = apiService.doGetList(url,  UserTodoListDTO.class);
//                        } else {
//                            userTodoList = apiService.doPostList(url, reqVO, UserTodoListDTO.class);
//                        }
//
//                        buildConfData(appDetail, config, userTodoList);
//                        if (CollUtil.isNotEmpty(userTodoList)) {
//                            list.addAll(userTodoList);
//                        }
//                    } catch (Exception e) {
//                        log.error("调用接口出错url#{}, 请求参数#{}", url, JSONObject.toJSONString(reqVO), e);
//
//                    }
//                }
//                if (Objects.equals(WorkbenchTaskTypeEnum.DONE_TASKS.getCode(),taskType)){
//                    //按处理时间降序排序
//                    list.sort(Comparator.comparing(UserTodoListDTO::getTaskProcessTime).reversed());
//                } else {
//                    //按派发时间降序排序
//                    list.sort(Comparator.comparing(UserTodoListDTO::getTaskStartDate).reversed());
//                }
//            }
//        }
//        return result;
//    }

    @Override
    public WorkbenchConfig getConfigById(String id) {
        return workbenchConfigMapper.selectById(id);
    }

    @Override
    public List<WorkbenchConfig> getConfigList() {
        return workbenchConfigMapper.getConfigList();
    }

    @Override
    public String addConfig(WorkbenchConfigAddDTO dto) {
        if (StrUtil.isBlank(dto.getId())){
            dto.setId(batchCommonService.uuid(WorkbenchConfig.TB_NAME));
        }
        WorkbenchConfig workbenchConfig = new WorkbenchConfig();
        BeanUtils.copyProperties(dto,workbenchConfig);
        workbenchConfig.setCreateTime(LocalDateTime.now());
        workbenchConfig.setUpdateTime(LocalDateTime.now());
        workbenchConfig.setDeleteFlag(CommonConstants.DELETE_FLAG_0);
        workbenchConfigMapper.insert(workbenchConfig);
        return dto.getId();
    }
}
