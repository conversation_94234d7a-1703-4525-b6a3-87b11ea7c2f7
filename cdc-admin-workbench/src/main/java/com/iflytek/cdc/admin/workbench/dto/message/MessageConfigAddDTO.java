package com.iflytek.cdc.admin.workbench.dto.message;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class MessageConfigAddDTO implements Serializable {



    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "app编码")
    private String appCode;

    @ApiModelProperty(value = "消息类型  1-待办提醒 ；2-系统通知")
    private Integer messageType;

    @ApiModelProperty(value = "消息名称")
    private String messageName;

    @ApiModelProperty(value = "消息内容")
    private String messageContent;

    @ApiModelProperty(value = "来源系统编码")
    private String sourceSystemCode;

    @ApiModelProperty(value = "来源系统名称")
    private String sourceSystemName;

    @ApiModelProperty(value = "系统中相对路径")
    private String systemRelativePath;
}
