package com.iflytek.cdc.admin.workbench.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.iflytek.cdc.admin.common.apiservice.UapServiceApi;
import com.iflytek.cdc.admin.common.constants.CommonConstants;
import com.iflytek.cdc.admin.common.enums.RoleTypeEnum;
import com.iflytek.cdc.admin.common.vo.uap.*;
import com.iflytek.cdc.admin.workbench.dto.shortcutMenu.SystemSourceDto;
import com.iflytek.cdc.admin.workbench.dto.userTodo.TaskClassDTO;
import com.iflytek.cdc.admin.workbench.enums.WorkLogSystemSourceEnum;
import com.iflytek.cdc.admin.workbench.mapper.WorkbenchConfigMapper;
import com.iflytek.cdc.admin.workbench.service.CommonService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static com.iflytek.cdc.admin.common.interceptor.UserInfoInterceptor.USER_INFO;

@Slf4j
@Service
public class CommonServiceImpl implements CommonService {
    @Resource
    private UapServiceApi uapServiceApi;

    @Resource
    private WorkbenchConfigMapper workbenchConfigMapper;

    @Override
    public List<SystemSourceDto> getSystemSourceSelect(String loginUserId) {
        List<SystemSourceDto> result = new ArrayList<>();
        SystemSourceDto systemSourceDto;
        //调用uap接口获取用户的应用列表
        List<UapPortalApp> uapPortalApps = uapServiceApi.queryUserPortalApp(loginUserId);
        if (CollUtil.isNotEmpty(uapPortalApps)) {
            List<String> portalAppCodeList = getUserPortalAppCodeList(uapPortalApps);
            for (WorkLogSystemSourceEnum entry : WorkLogSystemSourceEnum.values()) {
                if (portalAppCodeList.contains(entry.getSystemCode())) {
                    systemSourceDto = new SystemSourceDto();
                    systemSourceDto.setSystemCode(entry.getSystemCode());
                    systemSourceDto.setSystemName(entry.getSystemName());
                    systemSourceDto.setSystemSimpleName(entry.getSystemSimpleName());
                    result.add(systemSourceDto);
                }
            }
        }
        return result;
    }
    private List<String> getUserPortalAppCodeList(List<UapPortalApp> uapPortalApps) {
        List<UapPortalAppDetail> allApps = new ArrayList<>();
        for (UapPortalApp uapPortalApp : uapPortalApps) {
            List<UapPortalAppDetail> appList = uapPortalApp.getAppList();
            allApps.addAll(appList);
        }
        return allApps.stream().map(UapPortalAppDetail::getCode).distinct().collect(Collectors.toList());
    }
    @Override
    public List<BaseSelectDto> queryDeptSelectListByLoginUserId(String orgId, String loginUserId) {
        if (StringUtils.isEmpty(orgId) || StringUtils.isEmpty(loginUserId)) {
            return Collections.emptyList();
        }
        return uapServiceApi.queryDeptSelectListByLoginUserIdAndOrg(orgId, loginUserId);

    }

    /**
     * 获取代办任务类别
     * @return
     */
    @Override
    public List<TaskClassDTO> queryTaskClassList() {
        return workbenchConfigMapper.queryTaskClassList();
    }
}
