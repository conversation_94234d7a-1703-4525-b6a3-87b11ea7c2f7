package com.iflytek.cdc.admin.workbench.mapper;

import com.iflytek.cdc.admin.workbench.entity.UserShortcutMenu;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 工作台用户快捷菜单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-06
 */
public interface UserShortcutMenuMapper extends BaseMapper<UserShortcutMenu> {

    List<UserShortcutMenu> selectByLoginUserId(@Param("loginUserId") String loginUserId);

    void deleteByUapUserId(@Param("loginUserId")String loginUserId);

}
