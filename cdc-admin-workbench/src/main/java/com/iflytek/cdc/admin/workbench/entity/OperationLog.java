package com.iflytek.cdc.admin.workbench.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 操作记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "tb_cdcmr_operation_log",schema = "app")
@ApiModel(value="OperationLog对象", description="操作记录表")
public class OperationLog implements Serializable {

    public static final String TB_NAME ="app.tb_cdcmr_operation_log";

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private String id;

    private String appCode;

    @ApiModelProperty(value = "操作者所属机构id")
    private String orgId;

    @ApiModelProperty(value = "操作者所属机构名称")
    private String orgName;

    @ApiModelProperty(value = "操作者所属科室id")
    private String departmentId;

    @ApiModelProperty(value = "操作者所属科室名称")
    private String departmentName;

    @ApiModelProperty(value = "模块或阶段")
    private String operationModule;

    @ApiModelProperty(value = "具体的操作名称")
    private String operationName;

    private String creatorId;

    private String creator;

    /**
     * 请求路径
     */
    @ApiModelProperty(value="请求路径")
    private String queryUrl;

    /**
     * 查询参数
     */
    @ApiModelProperty(value="请求参数")
    private String queryParam;
    private Long fileSize;

    private Integer status;

    private LocalDateTime createTime;


}
