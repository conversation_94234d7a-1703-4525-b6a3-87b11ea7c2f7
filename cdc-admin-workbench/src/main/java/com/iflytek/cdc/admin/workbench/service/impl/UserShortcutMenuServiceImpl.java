package com.iflytek.cdc.admin.workbench.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.iflytek.cdc.admin.common.apiservice.BatchCommonService;
import com.iflytek.cdc.admin.workbench.entity.ShortcutMenu;
import com.iflytek.cdc.admin.workbench.entity.UserShortcutMenu;
import com.iflytek.cdc.admin.workbench.mapper.ShortcutMenuMapper;
import com.iflytek.cdc.admin.workbench.mapper.UserShortcutMenuMapper;
import com.iflytek.cdc.admin.workbench.service.ShortcutMenuService;
import com.iflytek.cdc.admin.workbench.service.UserShortcutMenuService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iflytek.cdc.admin.workbench.vo.shortcutMenu.UserShortcutMenuDetailVO;
import com.iflytek.cdc.admin.workbench.vo.shortcutMenu.UserShortcutMenuUpdateVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 工作台用户快捷菜单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-06
 */
@Service
public class UserShortcutMenuServiceImpl extends ServiceImpl<UserShortcutMenuMapper, UserShortcutMenu> implements UserShortcutMenuService {

    @Resource
    private ShortcutMenuMapper shortcutMenuMapper;
    @Resource
    private ShortcutMenuService shortcutMenuService;
    
    @Resource
    private BatchCommonService batchCommonService;

    @Override
    public List<ShortcutMenu> getCurrentUserShortcutMenu(String loginUserId) {
        List<UserShortcutMenu> userShortcutMenus = baseMapper.selectByLoginUserId(loginUserId);
        List<ShortcutMenu> shortcutMenus ;
        if (CollUtil.isNotEmpty(userShortcutMenus)) {
            List<String> menuIds = userShortcutMenus.stream().map(UserShortcutMenu::getShortcutMenuId).collect(Collectors.toList());
            shortcutMenus = shortcutMenuMapper.selectBatchIds(menuIds);
        } else {
            shortcutMenus = shortcutMenuMapper.findDefaultShortcutMenus();
        }
        List<ShortcutMenu> userHasPermissionMenu =  shortcutMenuService.getUserHasPermissionMenu(loginUserId,shortcutMenus);
        sortUserShortcutMenus(userHasPermissionMenu, userShortcutMenus);
        return userHasPermissionMenu;
    }

    /**
     * 排序
     * @param shortcutMenus
     * @param userShortcutMenus
     */
    private void sortUserShortcutMenus(List<ShortcutMenu> shortcutMenus, List<UserShortcutMenu> userShortcutMenus) {
        for (ShortcutMenu shortcutMenu : shortcutMenus) {
            for (UserShortcutMenu userShortcutMenu : userShortcutMenus) {
                if (shortcutMenu.getId().equals(userShortcutMenu.getShortcutMenuId())) {
                    shortcutMenu.setOrderNo(userShortcutMenu.getOrderNo());
                    break;
                }
            }
        }
        shortcutMenus.sort(Comparator.comparingInt(ShortcutMenu::getOrderNo));
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateCurrentUserShortcutMenu(UserShortcutMenuUpdateVO userShortcutMenuUpdateVo, String loginUserId) {
        baseMapper.deleteByUapUserId(loginUserId);
        //重新添加
        UserShortcutMenu tbCdcportalUserShortcutMenu;
        List<UserShortcutMenu> userShortcutMenus = new ArrayList<>();
        for (UserShortcutMenuDetailVO userShortcutMenuDetailVo : userShortcutMenuUpdateVo.getUserShortcutMenu()) {
            tbCdcportalUserShortcutMenu = new UserShortcutMenu();
            tbCdcportalUserShortcutMenu.setId(batchCommonService.uuid(UserShortcutMenu.TABLE_NAME));
            tbCdcportalUserShortcutMenu.setUapUserId(loginUserId);
            tbCdcportalUserShortcutMenu.setShortcutMenuId(userShortcutMenuDetailVo.getShortcutMenuId());
            tbCdcportalUserShortcutMenu.setOrderNo(userShortcutMenuDetailVo.getOrderNo());
            userShortcutMenus.add(tbCdcportalUserShortcutMenu);
        }
        return this.saveBatch(userShortcutMenus);
    }
}
